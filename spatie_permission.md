```php
// Clear All Permission Cache
app()->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();

// Add Permission to Role
$role = Role::find(1);
$role->givePermissionTo('reorder_sliders::tablet');

// Create Permission and than Add Permission to Role
$role = Role::find(1);
$permission = Permission::create([
  "name" => "delete_redirect",
  "guard_name" => "web"
]);
$role->givePermissionTo($permission);

```
