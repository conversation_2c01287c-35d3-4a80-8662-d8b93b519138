{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "ext-soap": "*", "bezhansalleh/filament-shield": "^2.0", "codeat3/blade-akar-icons": "^1.23", "codeat3/blade-mono-icons": "^1.3", "doctrine/dbal": "^3.3", "elasticsearch/elasticsearch": "^8.12", "eonvisualmedia/laravel-klaviyo": "^2.1.0", "f9webltd/laravel-api-response-helpers": "^1.5", "filament/filament": "^2.0", "filament/forms": "^2.0", "filament/notifications": "^2.0", "filament/spatie-laravel-media-library-plugin": "^2.0", "filament/spatie-laravel-settings-plugin": "^2.0", "filament/tables": "^2.0", "graham-campbell/digitalocean": "^10.3", "guzzlehttp/guzzle": "^7.2", "helpscout/api": "^3.6", "inspector-apm/inspector-laravel": "^4.7", "iyzico/iyzipay-php": "^2.0", "konnco/filament-import": "^1.4", "laravel/framework": "^9.11", "laravel/horizon": "^5.9", "laravel/octane": "^1.5", "laravel/passport": "^11.3", "laravel/sanctum": "^3.2", "laravel/scout": "^9.4", "laravel/tinker": "^2.7", "laravel/vapor-core": "^2.30", "league/flysystem-aws-s3-v3": "^3.0", "livewire/livewire": "^2.10", "lorisleiva/laravel-actions": "^2.4", "malios/php-to-ascii-table": "^3.0", "mikemclin/laravel-wp-password": "^2.0", "milon/barcode": "^10.0", "nazarii-kretovych/laravel-api-model-driver": "*", "netresearch/jsonmapper": "^4.0", "observertech/laravel-repository": "*", "opcodesio/log-viewer": "^2.3", "owen-it/laravel-auditing": "^13.5", "pxlrbt/filament-excel": "^1.1", "ryangjchandler/filament-navigation": "^v0.5.0", "spatie/laravel-backup": "^8.2", "spatie/laravel-blink": "^1.6", "spatie/laravel-collection-macros": "^7.12", "spatie/laravel-enum": "^3.0", "spatie/laravel-model-states": "^2.11", "spatie/laravel-query-builder": "^5.1", "spatie/laravel-ray": "^1.31", "spatie/laravel-sitemap": "^6.2", "spatie/laravel-slack-alerts": "^1.2", "spatie/laravel-sluggable": "^3.4", "spiral/roadrunner": "^2.8.2", "stechstudio/filament-impersonate": "^2.5", "tymon/jwt-auth": "^2.0.0", "yedincisenol/parasut": "*"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "fakerphp/faker": "^1.9.1", "laradumps/laradumps": "^2.2", "laravel/pint": "^0.1.5", "laravel/sail": "^1.0.1", "laravel/telescope": "^4.15", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "pestphp/pest": "^1.23", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan filament:upgrade"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "path", "url": "./lib/**"}]}