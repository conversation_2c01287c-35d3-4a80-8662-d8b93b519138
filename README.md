## Arama alanına resource ekleme

Örneğin sipariş no ile sipariş bulmak için all-orders resource içerisine

```php
protected static ?string $recordTitleAttribute = 'order_number';
```

eklendiğinde tüm siparişlerde aranıp bulunabilir.

Arama için birden çok field kullanılabilir.

```php
public static function getGloballySearchableAttributes(): array
{
    return ['title', 'slug', 'author.name', 'category.name'];
}
```

## Yeni resource ekleme

Sonundaki alan model adını ifade etmek üzere

```bash
php artisan make:filament-resource Customer --generate
```

## Yeni relation ekleme

Örneğin siparişin ödeme adımlarını görebilmek için eklenmesi gereken

```bash
php artisan make:filament-relation-manager AllOrdersResource orderTransaction id
```

Burada AllOrdersResource hangi resource içerisine eklenmesi gerekti<PERSON>, orderTransaction relation adı (model içeriisndeki fonsiyon adı, Order Model class içerisinde tanımlı bu örnekte), id ise relation içerisindeki title değeri.

## Yeni Sayfa Ekleme

Eğer standart sayfalar yeterli gelmiyorsa özel sayfalar oluşturulabilir.

```bash
php artisan make:filament-page Settings
```

## Bekleyen ödeme planları için raporlama

Bitiş tarihi ve başlangıç tarihi arasında bekleyen ödeme planlarını listeler.

```sql
SELECT order_transactions.id AS transaction_id,
       order_transactions.order_id AS 'SiparisId', order_transactions.due_date AS 'SiparisVadeTarihi', order_transactions.amount AS 'Tutar', CASE WHEN orders.status = 'App\\States\\Order\\OrderCompleted' THEN
        "Tamamlandı"
        WHEN orders.status = 'App\\States\\Order\\OrderRenting' THEN
            "Kiralama Devam Ediyor"
        WHEN orders.status = 'App\\States\\Order\\OrderCancelled' THEN
            "İptal"
        WHEN orders.status = 'App\\States\\Order\\OrderShipped' THEN
            "Kargolandı"
        WHEN orders.status = 'App\\States\\Order\\OrderDenied' THEN
            "Reddedildi"
        WHEN orders.status = 'App\\States\\Order\\OrderAtLegalPursuit' THEN
            "Yasal Takip Siparişi"
        WHEN orders.status = 'App\\States\\Order\\OrderApproved' THEN
            "Onaylandı"
        END AS 'SiparisDurumu1', orders.status AS 'SiparisDurumu', orders.order_number AS 'SiparisNo', orders.created_at AS 'Siparistarihi', users.full_name_db AS 'MusteriAdi'
FROM order_transactions
         LEFT JOIN orders ON orders.id = order_transactions.order_id
         LEFT JOIN users ON users.id = orders.user_id
WHERE order_transactions.payment_status_id = 2
  AND order_transactions.deleted_at IS NULL
  AND order_transactions.due_date BETWEEN '2000-01-01'
    AND '2023-05-25'
  AND orders.deleted_at IS NULL
```

Tüm ödeme planları

```sql
SELECT order_transactions.id AS transaction_id,
       order_transactions.order_id AS 'SiparisId', order_transactions.due_date AS 'SiparisVadeTarihi', order_transactions.amount AS 'Tutar', CASE WHEN orders.status = 'App\\States\\Order\\OrderCompleted' THEN
        "Tamamlandı"
        WHEN orders.status = 'App\\States\\Order\\OrderRenting' THEN
            "Kiralama Devam Ediyor"
        WHEN orders.status = 'App\\States\\Order\\OrderCancelled' THEN
            "İptal"
        WHEN orders.status = 'App\\States\\Order\\OrderShipped' THEN
            "Kargolandı"
        WHEN orders.status = 'App\\States\\Order\\OrderDenied' THEN
            "Reddedildi"
        WHEN orders.status = 'App\\States\\Order\\OrderAtLegalPursuit' THEN
            "Yasal Takip Siparişi"
        WHEN orders.status = 'App\\States\\Order\\OrderApproved' THEN
            "Onaylandı"
        WHEN orders.status = 'App\\States\\Order\\OrderReceived' THEN
            "Yeni Sipariş"
        WHEN orders.status = 'App\\States\\Order\\OrderEvaluation' THEN
            "Sipariş Değerlendiriliyor"
        WHEN orders.status = 'App\\States\\Order\\OrderDocumentWaiting' THEN
            "Belge Bekleniyor"
        END AS 'SiparisDurumu1', orders.status AS 'SiparisDurumu', orders.order_number AS 'SiparisNo', orders.created_at AS 'Siparistarihi', users.full_name_db AS 'MusteriAdi'
FROM order_transactions
         LEFT JOIN orders ON orders.id = order_transactions.order_id
         LEFT JOIN users ON users.id = orders.user_id
WHERE order_transactions.payment_status_id = 2
  AND order_transactions.deleted_at IS NULL
  AND order_transactions.due_date BETWEEN '2000-01-01'
    AND '2033-06-07'
  AND orders.deleted_at IS NULL
  and orders.status IS NOT NULL

```

## Cache yönetimi

```bash
php artisan cache:clear
php artisan config:clear
php artisan view:clear
php artisan route:clear
php artisan clear-compiled
php artisan optimize
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
```

### Kiralama için yeni ay ekleme adımları

1. lunar_subscription_months tablosuna yeni ay ekle ve sıralamasını ayarla

```sql
SELECT orders.id as 'Sipariş ID', orders.order_number as 'Sipariş NO', CASE
    WHEN orders.status = 'App\\States\\Order\\OrderCompleted' THEN
        "Tamamlandı"
    WHEN orders.status = 'App\\States\\Order\\OrderRenting' THEN
        "Kiralama Devam Ediyor"
    WHEN orders.status = 'App\\States\\Order\\OrderCancelled' THEN
        "İptal"
    WHEN orders.status = 'App\\States\\Order\\OrderShipped' THEN
        "Kargolandı"
    WHEN orders.status = 'App\\States\\Order\\OrderDenied' THEN
        "Reddedildi"
    WHEN orders.status = 'App\\States\\Order\\OrderAtLegalPursuit' THEN
        "Yasal Takip Siparişi"
    WHEN orders.status = 'App\\States\\Order\\OrderApproved' THEN
        "Onaylandı"
    WHEN orders.status = 'App\\States\\Order\\OrderReceived' THEN
        "Yeni Sipariş"
    WHEN orders.status = 'App\\States\\Order\\OrderEvaluation' THEN
        "Sipariş Değerlendiriliyor"
    WHEN orders.status = 'App\\States\\Order\\OrderDocumentWaiting' THEN
        "Belge Bekleniyor"
    WHEN orders.status = 'App\\States\\Order\\OrderUserCantBeReached' THEN
        "Müşteriye Ulaşılamıyor"
    END AS 'SiparisDurumu1', orders.status AS 'SiparisDurumu', orders.created_at as 'Sipariş Tarihi', orders.last_due_date as 'Son Ödeme Planı Tarihi', order_items.contract_expired_at as 'Ürün Sözleşme Son Tarihi', CASE
    WHEN order_items.plan = 1 THEN
        "1 AY"
    WHEN order_items.plan = 2 THEN
        "3 AY"
    WHEN order_items.plan = 3 THEN
        "6 AY"
    WHEN order_items.plan = 4 THEN
        "12 AY"
    WHEN order_items.plan = 5 THEN
        "18 AY"
    WHEN order_items.plan = 6 THEN
        "2 AY"
    END AS 'Kiralama Süresi'
from order_items
         left join orders on orders.id = order_items.order_id
where order_items.deleted_at IS NULL
order by order_items.id DESC
```

### Birden fazla onaylanmış ürünü olan kiralaması devam eden siparişler

```sql
SELECT *
from orders
         inner JOIN (SELECT order_id
                     from order_items
                     where order_items.deleted_at IS NULL
                       and order_items.is_user_suitable_control = 1
                     GROUP BY order_items.order_id
                     HAVING COUNT(*) > 1) as ot on ot.order_id = orders.id
where orders.status = 'App\\States\\Order\\OrderRenting' limit 10


SELECT order_id
from order_items
where order_items.deleted_at IS NULL
  and order_items.is_user_suitable_control = 1
GROUP BY order_items.order_id
HAVING COUNT(*) > 1 limit 100
```

### Güncel Siparişler ve Müşterileri Raporu

```sql
SELECT orders.id AS 'Sipariş ID', orders.order_number AS 'Sipariş NO', CASE WHEN orders.status = 'App\\States\\Order\\OrderCompleted' THEN
    'Tamamlandı'
    WHEN orders.status = 'App\\States\\Order\\OrderRenting' THEN
        'Kiralama Devam Ediyor'
    WHEN orders.status = 'App\\States\\Order\\OrderCancelled' THEN
        'İptal'
    WHEN orders.status = 'App\\States\\Order\\OrderShipped' THEN
        'Kargolandı'
    WHEN orders.status = 'App\\States\\Order\\OrderDenied' THEN
        'Reddedildi'
    WHEN orders.status = 'App\\States\\Order\\OrderAtLegalPursuit' THEN
        'Yasal Takip Siparişi'
    WHEN orders.status = 'App\\States\\Order\\OrderApproved' THEN
        'Onaylandı'
    WHEN orders.status = 'App\\States\\Order\\OrderReceived' THEN
        'Yeni Sipariş'
    WHEN orders.status = 'App\\States\\Order\\OrderEvaluation' THEN
        'Sipariş Değerlendiriliyor'
    WHEN orders.status = 'App\\States\\Order\\OrderDocumentWaiting' THEN
        'Belge Bekleniyor'
    WHEN orders.status = 'App\\States\\Order\\OrderUserCantBeReached' THEN
        'Müşteriye Ulaşılamıyor'
    WHEN orders.status = 'App\\States\\Order\\OrderRefunded' THEN
        'İade Edildi'
    END AS 'SiparisDurumu', orders.status AS 'SiparisDurumu', orders.created_at AS 'Sipariş Tarihi', orders.total as 'Kira Tutarı', orders.user_id AS 'Müşteri ID', users.full_name_db as 'Müşteri Adı', users.phone As 'Tel NO'
FROM orders
         LEFT JOIN users on users.id = orders.user_id
where orders.deleted_at IS NULL
```

### Müşteri Listesi

```sql

SELECT orders.id AS 'Sipariş ID', orders.order_number AS 'Sipariş NO', CASE WHEN orders.status = 'App\\States\\Order\\OrderCompleted' THEN
    'Tamamlandı'
    WHEN orders.status = 'App\\States\\Order\\OrderRenting' THEN
        'Kiralama Devam Ediyor'
    WHEN orders.status = 'App\\States\\Order\\OrderCancelled' THEN
        'İptal'
    WHEN orders.status = 'App\\States\\Order\\OrderShipped' THEN
        'Kargolandı'
    WHEN orders.status = 'App\\States\\Order\\OrderDenied' THEN
        'Reddedildi'
    WHEN orders.status = 'App\\States\\Order\\OrderAtLegalPursuit' THEN
        'Yasal Takip Siparişi'
    WHEN orders.status = 'App\\States\\Order\\OrderApproved' THEN
        'Onaylandı'
    WHEN orders.status = 'App\\States\\Order\\OrderReceived' THEN
        'Yeni Sipariş'
    WHEN orders.status = 'App\\States\\Order\\OrderEvaluation' THEN
        'Sipariş Değerlendiriliyor'
    WHEN orders.status = 'App\\States\\Order\\OrderDocumentWaiting' THEN
        'Belge Bekleniyor'
    WHEN orders.status = 'App\\States\\Order\\OrderUserCantBeReached' THEN
        'Müşteriye Ulaşılamıyor'
    WHEN orders.status = 'App\\States\\Order\\OrderRefunded' THEN
        'İade Edildi'
    END AS 'SiparisDurumu', orders.status AS 'SiparisDurumu', orders.created_at AS 'Sipariş Tarihi', orders.total as 'Kira Tutarı', orders.user_id AS 'Müşteri ID', users.full_name_db as 'Müşteri Adı', users.phone As 'Tel NO'
FROM orders
         LEFT JOIN users on users.id = orders.user_id
where orders.deleted_at IS NULL
```


### Google Kategorileri 
https://github.com/jwcobb/laravel-google-product-categories