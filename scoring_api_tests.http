### Skorlama Sistemi API Test Dosyası
### Bu dosya VS Code REST Client uzantısı veya benzer araçlarla kullanılabilir
### Laravel uygulaması: http://localhost veya test environment URL'i

@baseUrl = https://kb.test
@apiKey = test_api_key_1234567890

### ===========================================
### 1. SKORLAMA TALEBİ GÖNDERME (Başarılı)
### ===========================================
# @name skorlamaTalebiBasarili
POST {{baseUrl}}/api/scoring HTTP/1.1
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
    "scoring_source_id": 1,
    "full_name": "Ahmet Yılmaz",
    "tckn": "12345678901",
    "email": "<EMAIL>",
    "birth_date": "1985-05-15",
    "requested_amount": 50000,
    "requested_duration_months": 24,
    "additional_data": {
        "phone": "05551234567",
        "monthly_income": 8000,
        "occupation": "Mühendis"
    }
}

### ===========================================
### 2. SKORLAMA TALEBİ - Validasyon Hatası Testi
### ===========================================
# @name skorlamaTalebiValidasyonHatasi
POST {{baseUrl}}/api/scoring HTTP/1.1
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
    "scoring_source_id": 999,
    "full_name": "",
    "tckn": "123",
    "email": "gecersiz-email",
    "birth_date": "2030-01-01",
    "requested_amount": 500,
    "requested_duration_months": 100
}

### ===========================================
### 3. SKORLAMA TALEBİ - İkinci Test Kaynağı
### ===========================================
# @name skorlamaTalebiIkinciKaynak
POST {{baseUrl}}/api/scoring HTTP/1.1
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
    "scoring_source_id": 2,
    "full_name": "Ayşe Demir",
    "tckn": "98765432109",
    "email": "<EMAIL>",
    "birth_date": "1990-08-20",
    "requested_amount": 75000,
    "requested_duration_months": 36,
    "additional_data": {
        "phone": "05559876543",
        "monthly_income": 12000,
        "occupation": "Doktor",
        "work_experience_years": 8
    }
}

### ===========================================
### 4. SKORLAMA TALEBİ - Üçüncü Test Kaynağı (Minimum Tutar)
### ===========================================
# @name skorlamaTalebiUcuncuKaynak
POST {{baseUrl}}/api/scoring HTTP/1.1
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
    "scoring_source_id": 3,
    "full_name": "Mehmet Öztürk",
    "tckn": "11223344556",
    "email": "<EMAIL>",
    "birth_date": "1988-12-10",
    "requested_amount": 1000,
    "requested_duration_months": 1,
    "additional_data": {
        "phone": "05551122334"
    }
}

### ===========================================
### 5. SKORLAMA TALEBİ - Maksimum Tutar
### ===========================================
# @name skorlamaTalebiMaksimumTutar
POST {{baseUrl}}/api/scoring HTTP/1.1
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
    "scoring_source_id": 1,
    "full_name": "Fatma Kaya",
    "tckn": "55667788990",
    "email": "<EMAIL>",
    "birth_date": "1975-03-25",
    "requested_amount": 500000,
    "requested_duration_months": 60,
    "additional_data": {
        "phone": "05555566778",
        "monthly_income": 25000,
        "occupation": "Avukat",
        "work_experience_years": 15,
        "has_collateral": true
    }
}

### ===========================================
### 6. SKORLAMA DURUMU SORGULAMA
### ===========================================
### Not: Yukarıdaki isteklerden birinin ULID'sini buraya yazın
@ulid = 01JWRPCJTZPF8GA7V243NJQS5A

# @name skorlamaDurumuSorgula
GET {{baseUrl}}/api/scoring/{{ulid}}/status HTTP/1.1
Authorization: Bearer {{apiKey}}

### ===========================================
### 7. SKORLAMA DURUMU - Geçersiz ULID
### ===========================================
# @name skorlamaDurumuGecersizUlid
GET {{baseUrl}}/api/scoring/gecersiz-ulid/status HTTP/1.1
Authorization: Bearer {{apiKey}}

### ===========================================
### 8. SKORLAMA SONUCU WEBHOOK'U (Findex'ten Gelecek)
### ===========================================
### Bu endpoint normalde Findex tarafından çağrılır
### Test için manuel olarak çağırabilirsiniz

# @name skorlamaSonucuWebhook
POST {{baseUrl}}/api/scoring/result HTTP/1.1
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
    "ulid": "{{ulid}}",
    "score": 350,
    "findex_journal_id": "FDX123456789",
    "processed_at": "2024-01-15T10:30:00Z"
}

### ===========================================
### 9. SKORLAMA SONUCU WEBHOOK'U - Düşük Skor (Red)
### ===========================================
# @name skorlamaSonucuWebhookDusukSkor
POST {{baseUrl}}/api/scoring/result HTTP/1.1
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
    "ulid": "{{ulid}}",
    "score": 180,
    "findex_journal_id": "FDX987654321",
    "processed_at": "2024-01-15T10:35:00Z"
}

### ===========================================
### 10. SKORLAMA SONUCU WEBHOOK'U - Yüksek Skor (Onay)
### ===========================================
# @name skorlamaSonucuWebhookYuksekSkor
POST {{baseUrl}}/api/scoring/result HTTP/1.1
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
    "ulid": "{{ulid}}",
    "score": 450,
    "findex_journal_id": "FDX555666777",
    "processed_at": "2024-01-15T10:40:00Z"
}

### ===========================================
### 11. SKORLAMA SONUCU WEBHOOK'U - Geçersiz Veri
### ===========================================
# @name skorlamaSonucuWebhookGecersizVeri
POST {{baseUrl}}/api/scoring/result HTTP/1.1
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
    "ulid": "gecersiz-ulid-format",
    "score": 600,
    "processed_at": "gecersiz-tarih"
}

### ===========================================
### 12. SKORLAMA SONUCU WEBHOOK'U - Bulunamayan ULID
### ===========================================
# @name skorlamaSonucuWebhookBulunamayanUlid
POST {{baseUrl}}/api/scoring/result HTTP/1.1
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
    "ulid": "01HXXXXXXXXXXXXXXXXXXZ",
    "score": 300,
    "findex_journal_id": "FDX111222333"
}

### ===========================================
### TEST SENARYOLARI AÇIKLAMALARI
### ===========================================

###
### Başarılı Akış:
### 1. Skorlama talebi gönder (#1, #3, #4, #5)
### 2. Response'dan ULID'yi al
### 3. ULID'yi @ulid değişkenine ata
### 4. Durum sorgula (#6)
### 5. Webhook ile sonuç gönder (#8, #9, #10)
### 6. Tekrar durum sorgula (#6)
###

###
### Hata Senaryoları:
### - Geçersiz validasyon verileri (#2)
### - Geçersiz ULID ile durum sorgulama (#7)
### - Geçersiz webhook verisi (#11)
### - Bulunamayan ULID ile webhook (#12)
###

###
### Değişkenler:
### @baseUrl: Uygulama URL'i (localhost, staging, production)
### @apiKey: API anahtarı (gerekirse)
### @ulid: Test edilecek skorlama talebinin ULID'si
###

###
### Scoring Source ID'leri:
### 1: Test Kredi Kuruluşu 1
### 2: Demo Finans Şirketi  
### 3: Beta Test Partneri
###

###
### Validasyon Kuralları:
### - scoring_source_id: 1-3 arası geçerli ID
### - tckn: 11 haneli rakam
### - email: Geçerli email formatı
### - birth_date: Bugünden önce
### - requested_amount: 1000-500000 TL arası
### - requested_duration_months: 1-60 ay arası
### - additional_data.phone: Opsiyonel, maks 20 karakter
###

###
### Skorlama Sistemi Durumları:
### - pending: Beklemede
### - sent_to_redis: Redis'e gönderildi
### - scored: Skorlandı
### - approved: Onaylandı
### - rejected: Reddedildi
### - manually_processed: Manuel işlendi
### - webhook_sent: Webhook gönderildi
###
``` 