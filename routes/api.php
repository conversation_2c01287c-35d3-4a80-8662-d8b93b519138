<?php

use App\Http\Controllers\Auth\User\UserAddressController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\FindexAutomationController;
use App\Http\Controllers\Order\OrderController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductOptionController;
use App\Http\Controllers\ProductOptionTypeController;
use App\Http\Controllers\ProductPriceController;
use App\Http\Controllers\ScoringWebhookController;
use App\Http\Controllers\User\UserOrderController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::get('products/{id}/options', [ProductController::class, 'options']);

Route::apiResource('orders', OrderController::class)->except('store');
Route::apiResource('users.addresses', UserAddressController::class);
Route::apiResource('users.orders', UserOrderController::class)->only('index', 'show');

Route::apiResources([
    'categories' => CategoryController::class,
    'products' => ProductController::class,
    'product-options' => ProductOptionController::class,
    'product-option-types' => ProductOptionTypeController::class,
    'product-prices' => ProductPriceController::class,
    'sliders' => \App\Http\Controllers\SliderController::class,
]);

Route::post('/tosla/confirm-payment', [\App\Http\Controllers\Tosla\ToslaController::class, 'confirmPayment'])->middleware(['role:tosla']);
Route::post('/tosla/check-status', [\App\Http\Controllers\Tosla\ToslaController::class, 'checkStatus'])->middleware(['role:tosla']);

// Skorlama sistemi webhook route'ları
Route::prefix('scoring')->group(function () {
    Route::post('/', [ScoringWebhookController::class, 'receive'])->name('scoring.receive');
    Route::get('/{ulid}/status', [ScoringWebhookController::class, 'status'])->name('scoring.status');

    // Skorlama sonucu webhook'u (Skorlabunu'dan gelecek)
    Route::post('/result', [\App\Http\Controllers\ScoringResultWebhookController::class, 'receive'])->name('scoring.result.receive');
    // Ödeme SMS'i göndermek üzere skorlama kaynağından çağrılacak endpoint (Optional, sadece ScoringSource içerisine send_payment_sms_on_approval true ise çağrılacak)
    Route::post('/{ulid}/payment-sms', [\App\Http\Controllers\PaymentSmsTriggerController::class, 'send'])->name('scoring.payment_sms');
});

// Findex Automation endpoint'leri
Route::post('/findex-automation/result', [FindexAutomationController::class, 'receiveResult'])->name('findex.automation.result');
Route::post('/scoring/{ulid}/update', [FindexAutomationController::class, 'updateStatus'])->name('scoring.update');
