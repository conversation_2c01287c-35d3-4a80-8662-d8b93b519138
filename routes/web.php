<?php

use App\Http\Controllers\Admin\GrantController;
use App\Http\Controllers\Auth\AuthenticatedController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\Auth\User\GuestCartController;
use App\Http\Controllers\B2BOrderController;
use App\Http\Controllers\BackupController;
use App\Http\Controllers\BusinessFormController;
use App\Http\Controllers\CreditCardController;
use App\Http\Controllers\PaymentSmsController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;
use App\Http\Controllers\Admin\SuperAdminOrderController;

Route::get('/', function () {
    return view('welcome');
});

//Route::group(['prefix' => 'admin'], function () {
//    \Aschmelyun\Larametrics\Larametrics::routes();
//});


Route::prefix('api/auth/user/cart')->middleware('auth.guest')->group(function () {
    // Misa<PERSON><PERSON>
    Route::get('/guest/cart', [GuestCartController::class, 'index']);
    Route::post('/guest/add-product', [GuestCartController::class, 'addProduct']);
    Route::put('/guest/update', [GuestCartController::class, 'update']);
    Route::delete('/guest/remove-product', [GuestCartController::class, 'removeProduct']);
    Route::delete('/guest/clear', [GuestCartController::class, 'clear']);
    Route::put('/guest/update-cart-item', [GuestCartController::class, 'updateCartItem']);
});

Route::post('api/auth/register', [RegisteredUserController::class, 'store'])->middleware('auth.guest')->name('register');
Route::post('api/auth/login', [AuthenticatedController::class, 'store'])->middleware('auth.guest')->name('login');
Route::post('api/auth/tg', [AuthenticatedController::class, 'tgLogin']);
Route::get('/irsaliye/{id}', [\App\Http\Controllers\WayBillController::class, 'index']);
Route::get('/order/get-obf/{order}', [\App\Http\Controllers\OrderController::class, 'getOBF'])->name('order.get-obf');
Route::get('/order/get-mks/{order}', [\App\Http\Controllers\OrderController::class, 'getMKS'])->name('order.get-mks');
Route::post('/api/auth/user/complete-cc-3ds', [CreditCardController::class, 'complete3DS']);
Route::post('/api/business-request', [BusinessFormController::class, 'saveForm']);
Route::post('/api/kiralamotor-request', [BusinessFormController::class, 'saveMotorcycleForm']);

Route::get('/get-cargo-return-code', [\App\Http\Controllers\CargoController::class, 'getCargoReturnCode']);

Route::get('/filament/download/{path}', function ($path) {
    try {
        $decodedPath = trim(base64_decode($path));

        // S3'ten dosyayı al
        if (!Storage::disk('s3')->exists($decodedPath)) {
            abort(404, 'Dosya bulunamadı');
        }

        $fileContent = Storage::disk('s3')->get($decodedPath);

        // Dosyayı decrypt etmeyi dene
        try {
            $fileContent = decrypt($fileContent);
        } catch (\Exception $e) {
            // Decrypt edilemezse, dosya zaten plain text olabilir
            // Hata yakalanır ama dosya olduğu gibi indirilir
            logger()->error('Scoring request notes file download error', [
                'path' => $decodedPath ?? null,
                'error' => $e->getMessage()
            ]);
        }

        // Dosya adını ve uzantısını al
        $fileName = basename($decodedPath);
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);

        // Dosya adında tarih varsa kullan, yoksa yeni tarih ekle
        if (!preg_match('/\d{4}-\d{2}-\d{2}/', $fileName)) {
            $fileName = pathinfo($fileName, PATHINFO_FILENAME) . '_' . date('Y-m-d_H-i-s') . '.' . $extension;
        }

        // İndirme yanıtı döndür
        return response($fileContent)
            ->header('Content-Type', 'application/octet-stream')
            ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"');
    } catch (\Exception $e) {
        \Log::error('File download error', [
            'path' => $path ?? null,
            'error' => $e->getMessage()
        ]);

        abort(500, 'Dosya indirilemedi');
    }
})->name('filament.download')->middleware(['auth:sanctum']);

Route::post('/webhook/skorlabunu-handler', [App\Http\Controllers\SkorlabunuWebhookController::class, 'handle'])->name('webhook.skorlabunu-handler');

// Loglar üzerinden sipariş oluşturma çalışması, ödeme planı oluşmuyor, log üzerinden kopyala yapıştır üzerinde revize gerekiyor. Geliştirme yapılmalı
Route::middleware(['auth:sanctum'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/tools/create-order-form', [SuperAdminOrderController::class, 'showCreateOrderForm'])
        ->name('order.create_form');
    Route::post('/orders/create-from-json', [SuperAdminOrderController::class, 'createOrderFromJson'])
        ->name('order.create_from_json');
});

Route::get('/super-admin-grant-permission', GrantController::class)->middleware('auth:sanctum')->name('super-admin-grant-permission');

Route::get('/backups/delete', [BackupController::class, 'delete'])
    ->name('backups.delete')
    ->middleware('signed');

// Yeni Pegasus Puan Gönderme Aracı Rotası
Route::get('/tools/pegasus-reward', function () {
    if (!auth()->user()->hasRole('Super Admin')) {
        abort(403, 'Bu sayfayı görüntüleme yetkiniz bulunmamaktadır.');
    }
    return view('tools.pegasus-reward');
})->middleware('auth:sanctum')->name('tools.pegasus-reward');

// commented because of this endpoint should defined in backend-octane project
// SMS Ödeme Linki Route'ları 
// Route::prefix('payment')->name('payment.')->group(function () {
//     Route::get('/{token}', [PaymentSmsController::class, 'show'])->name('show');
//     Route::post('/{token}/process', [PaymentSmsController::class, 'process'])->name('process');
//     Route::get('/{token}/success', [PaymentSmsController::class, 'showSuccess'])->name('success');
// });


// B2B Order routes
Route::middleware(['auth:sanctum'])->group(function () {
    Route::get('/b2b-order/{record}/download-agreement', [B2BOrderController::class, 'downloadAgreement'])
        ->name('b2b-order.download-agreement');
});
