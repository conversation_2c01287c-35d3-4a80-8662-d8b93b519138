<?php

use App\Http\Controllers\Auth\AuthenticatedController;
use App\Http\Controllers\Auth\EmailVerificationNotificationController;
use App\Http\Controllers\Auth\NewPasswordController;
use App\Http\Controllers\Auth\PasswordResetLinkController;
use App\Http\Controllers\Auth\User\UserAddressController;
use App\Http\Controllers\Auth\User\UserCartController;
use App\Http\Controllers\Auth\User\UserCouponController;
use App\Http\Controllers\Auth\User\UserOrderController;
use App\Http\Controllers\Auth\User\UserPasswordUpdateController;
use App\Http\Controllers\Auth\User\UserProfileController;
use App\Http\Controllers\Auth\User\UserSelectAddressController;
use App\Http\Controllers\Checkout\CheckoutController;
use App\Http\Controllers\CreditCardController;
use App\Http\Controllers\FavouriteController;
use App\Http\Controllers\SupportRequestController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/email/verify/{id}/{hash}', function (Request $request, $id, $hash) {
    return redirect()->away(env('FRONTEND_URL') . '/email/verify/' . $id . '/' . $hash);
})->name('verification.verify');

Route::middleware('guest')->group(function () {
    Route::post('forgot-password', [PasswordResetLinkController::class, 'store'])->name('password.email');
    Route::get('send-forgot-password-email', [PasswordResetLinkController::class, 'sendForgotPasswordEmail']); // Geçici route
    Route::post('reset-password', [NewPasswordController::class, 'store'])->name('password.reset');
    //Route::post('reset-password', [NewPasswordController::class, 'store'])->name('password.update');
});

Route::prefix('user')->group(function () {
    Route::prefix('cart')->group(function () {
        Route::get('/', [UserCartController::class, 'index']);

        Route::get('/get-auth', [UserCartController::class, 'getAuth']);

        /* Misafir kullanıcılarınn sepet işlemleri Laravel Passport header verileri ile çakıştığı için web.php içerisine taşındı */

        Route::middleware('auth')->group(function () {
            Route::post('add-product', [UserCartController::class, 'addProduct']);
            Route::put('update', [UserCartController::class, 'update']);
            Route::delete('remove-product', [UserCartController::class, 'removeProduct']);
            Route::delete('clear', [UserCartController::class, 'clear']);
            Route::put('update-cart-item', [UserCartController::class, 'updateCartItem']);

            Route::post('checkout', CheckoutController::class);
            Route::post('checkout-with-registered-card', [CheckoutController::class, 'compeletePaymentWithRegisteredCard']);
        });
    });

    Route::middleware('auth')->group(function () {
        Route::post('email/verification-notification', [EmailVerificationNotificationController::class, 'store'])->middleware(['throttle:6,1'])->name('verification.send');
        Route::post('email/verification/{user}', [EmailVerificationNotificationController::class, 'setAsVerified'])->middleware(['throttle:6,1']);

        Route::get('/', [UserProfileController::class, 'index']);
        Route::put('update', [UserProfileController::class, 'update']);
        Route::put('/validate-tckn/update', [UserProfileController::class, 'validateTCKN']);

        Route::put('update-password', UserPasswordUpdateController::class);

        Route::get('addresses/cities', [UserAddressController::class, 'getCities']);
        Route::apiResource('addresses', UserAddressController::class);

        Route::get('selected-address', [UserSelectAddressController::class, 'show']);
        Route::put('select-address/{id}', [UserSelectAddressController::class, 'update']);

        Route::get('orders', [UserOrderController::class, 'index']);
        Route::get('orders/{user}/admin', [UserOrderController::class, 'adminUserOrders']);
        Route::get('orders/{id}', [UserOrderController::class, 'show']);
        Route::put('orders/{id}/update-address', [UserOrderController::class, 'updateAddress']);

        Route::get('coupons', [UserCouponController::class, 'index']);
        Route::get('coupons/{id}', [UserCouponController::class, 'show']);

        Route::delete('logout', [AuthenticatedController::class, 'destroy']);

        Route::post('favourite/{pid}/toogle', [FavouriteController::class, 'toogle']);
        Route::post('favourite/{pid}/status', [FavouriteController::class, 'status']);
        Route::post('favourites', [FavouriteController::class, 'all']);
        Route::post('support-request', [SupportRequestController::class, 'create']);
        Route::get('support-request', [SupportRequestController::class, 'index']);

        Route::post('save-cc', [CreditCardController::class, 'create']);
        Route::post('save-cc-3ds', [CreditCardController::class, 'create3DS']);
        Route::get('cc', [CreditCardController::class, 'index']);
    });
});
