## Geçişten sonraki tüm siparişler

```sql
SELECT orders.id AS 'Sipariş ID', orders.order_number AS 'Sipariş NO', CASE WHEN orders.status = 'App\\States\\Order\\OrderCompleted' THEN
    'Tamamlandı'
    WHEN orders.status = 'App\\States\\Order\\OrderRenting' THEN
        'Kiralama Devam Ediyor'
    WHEN orders.status = 'App\\States\\Order\\OrderCancelled' THEN
        'İptal'
    WHEN orders.status = 'App\\States\\Order\\OrderShipped' THEN
        'Kargolandı'
    WHEN orders.status = 'App\\States\\Order\\OrderDenied' THEN
        'Reddedildi'
    WHEN orders.status = 'App\\States\\Order\\OrderAtLegalPursuit' THEN
        'Yasal Takip Siparişi'
    WHEN orders.status = 'App\\States\\Order\\OrderApproved' THEN
        'Onaylandı'
    WHEN orders.status = 'App\\States\\Order\\OrderReceived' THEN
        'Yeni Sipar<PERSON>ş'
    WHEN orders.status = 'App\\States\\Order\\OrderEvaluation' THEN
        'Sipariş Değerlendiriliyor'
    WHEN orders.status = 'App\\States\\Order\\OrderDocumentWaiting' THEN
        'Belge Bekleniyor'
    WHEN orders.status = 'App\\States\\Order\\OrderUserCantBeReached' THEN
        'Müşteriye Ulaşılamıyor'
    WHEN orders.status = 'App\\States\\Order\\OrderRefunded' THEN
        'İade Edildi'
    END AS 'SiparisDurumu',
	-- orders.status AS 'SiparisDurumu',  orders.created_at AS 'Sipariş Tarihi', orders.total as 'İlk Kira Tutarı', orders.user_id AS 'Müşteri ID', users.full_name_db as 'Müşteri Adı', 
	-- users.phone As 'Tel NO', order_items.product_id, rapor_urun.`name`,
       order_items.cancelled_at as 'Ürün Sonradan İptal Zamanı', case
        WHEN order_items.plan = 1 THEN
            '1 AY'
        WHEN order_items.plan = 2 THEN
            '3 AY'
        WHEN order_items.plan = 3 THEN
            '6 AY'
        WHEN order_items.plan = 4 THEN
            '12 AY'
        WHEN order_items.plan = 5 THEN
            '18 AY'
        WHEN order_items.plan = 6 THEN
            '2 AY'
        END AS 'Kiralama Süresi'
FROM orders
         LEFT JOIN users on users.id = orders.user_id
         left join order_items on order_items.order_id = orders.id
         left join rapor_variant on rapor_variant.id = order_items.product_id
         left join rapor_urun ON rapor_variant.product_id = rapor_urun.id
where orders.deleted_at IS NULL
  and orders.created_at >= '2023-05-18'
  and order_items.deleted_at IS NULL    
```

## Kalkınma Bankası Raporu

```sql

SELECT orders.id as 'Order ID', order_items.id as 'Order Item ID', orders.user_id as 'Musteri ID', CASE
    WHEN users.is_company = 1 THEN 'B2B'
    WHEN users.is_company = 0 THEN 'B2C'
    END AS 'Op Typ', rapor_urun.id as 'Urun ID', rapor_urun.`name` As 'Urun Adı', rapor_urun.category_name as 'Ana Kategori', CASE
    WHEN orders.status = 'App\\States\\Order\\OrderCompleted' THEN 'Tamamlandı'
    WHEN orders.status = 'App\\States\\Order\\OrderRenting' THEN 'Kiralama Devam Ediyor'
    WHEN orders.status = 'App\\States\\Order\\OrderCancelled' THEN 'İptal'
    WHEN orders.status = 'App\\States\\Order\\OrderShipped' THEN 'Kargolandı'
    WHEN orders.status = 'App\\States\\Order\\OrderDenied' THEN 'İptal'
    WHEN orders.status = 'App\\States\\Order\\OrderAtLegalPursuit' THEN 'İptal'
    WHEN orders.status = 'App\\States\\Order\\OrderApproved' THEN 'Onaylandı'
    WHEN orders.status = 'App\\States\\Order\\OrderReceived' THEN 'Yeni Sipariş'
    WHEN orders.status = 'App\\States\\Order\\OrderEvaluation' THEN 'Sipariş Değerlendiriliyor'
    WHEN orders.status = 'App\\States\\Order\\OrderDocumentWaiting' THEN 'Sipariş Değerlendiriliyor'
    WHEN orders.status = 'App\\States\\Order\\OrderUserCantBeReached' THEN 'İptal'
    WHEN orders.status = 'App\\States\\Order\\OrderRefunded' THEN 'İade Edildi'
    WHEN orders.status = 'App\\States\\Order\\OrderPaused' THEN 'Kiralama Devam Ediyor'
    END AS 'SiparisDurumu', CASE
    WHEN order_items.plan = 1 THEN '1'
    WHEN order_items.plan = 2 THEN '3'
    WHEN order_items.plan = 3 THEN '6'
    WHEN order_items.plan = 4 THEN '12'
    WHEN order_items.plan = 5 && users.is_company = 0 THEN '18'
    WHEN order_items.plan = 5 && users.is_company = 1 THEN '24'
    END AS 'Kiralama Süresi (Ay)', order_items.quantity as 'Sip Urun Adet', order_transactions.amount as 'Kira Tutarı',
-- 	CASE
-- 		WHEN order_transactions.payment_status_id = 1 THEN 'Ödendi'
-- 		WHEN order_transactions.payment_status_id = 2 THEN 'Ödenmedi'
-- 	END AS 'Kira Ödeme Statü',
	MONTH(order_transactions.due_date) as 'Plan Ay',
	YEAR(order_transactions.due_date) as 'Plan Yıl'
from orders
    LEFT JOIN order_items
on order_items.order_id = orders.id
    LEFT JOIN rapor_variant on order_items.product_id = rapor_variant.id
    LEFT JOIN rapor_urun on rapor_urun.id = rapor_variant.product_id
    INNER JOIN order_transactions on order_transactions.order_id = orders.id
    LEFT JOIN users on users.id = orders.user_id
where orders.deleted_at IS NULL
  and order_items.deleted_at IS NULL
  and order_transactions.deleted_at IS NULL
  and order_transactions.due_date
    < '2023-12-31 23:59:59'
  and orders.created_at
    < '2023-12-31 23:59:59'
ORDER BY orders.id DESC
```
