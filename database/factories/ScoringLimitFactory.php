<?php

namespace Database\Factories;

use App\Models\ScoringLimit;
use App\Models\ScoringRequest;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ScoringLimit>
 */
class ScoringLimitFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ScoringLimit::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $approvedLimit = $this->faker->randomFloat(2, 1000, 50000);
        $score = $this->faker->numberBetween(300, 850);
        
        return [
            'tckn' => $this->generateTckn(),
            'user_id' => null, // Will be set when needed
            'scoring_request_id' => null, // Will be set when needed
            'score' => $score,
            'approved_limit' => $approvedLimit,
            'remaining_limit' => $approvedLimit, // Initially same as approved_limit
            'valid_until' => now()->addDays(30),
        ];
    }

    /**
     * Indicate that the scoring limit is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'valid_until' => now()->subDay(),
        ]);
    }

    /**
     * Indicate that the scoring limit is partially used.
     */
    public function partiallyUsed(): static
    {
        return $this->state(fn (array $attributes) => [
            'remaining_limit' => $attributes['approved_limit'] * 0.5,
        ]);
    }

    /**
     * Indicate that the scoring limit is fully used.
     */
    public function fullyUsed(): static
    {
        return $this->state(fn (array $attributes) => [
            'remaining_limit' => 0,
        ]);
    }

    /**
     * Indicate that the scoring limit belongs to a user.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
            'tckn' => $user->tckn ?? $attributes['tckn'],
        ]);
    }

    /**
     * Indicate that the scoring limit belongs to a scoring request.
     */
    public function forScoringRequest(ScoringRequest $scoringRequest): static
    {
        return $this->state(fn (array $attributes) => [
            'scoring_request_id' => $scoringRequest->id,
            'tckn' => $scoringRequest->tckn,
        ]);
    }

    /**
     * Indicate that the scoring limit has low score.
     */
    public function lowScore(): static
    {
        return $this->state(fn (array $attributes) => [
            'score' => $this->faker->numberBetween(300, 450),
            'approved_limit' => $this->faker->randomFloat(2, 1000, 5000),
        ]);
    }

    /**
     * Indicate that the scoring limit has high score.
     */
    public function highScore(): static
    {
        return $this->state(fn (array $attributes) => [
            'score' => $this->faker->numberBetween(700, 850),
            'approved_limit' => $this->faker->randomFloat(2, 20000, 50000),
        ]);
    }

    /**
     * Generate a valid Turkish ID number (TCKN)
     */
    protected function generateTckn(): string
    {
        // Generate first 9 digits
        $digits = [];
        for ($i = 0; $i < 9; $i++) {
            $digits[] = $i === 0 ? rand(1, 9) : rand(0, 9);
        }

        // Calculate 10th digit
        $sumOdd = $digits[0] + $digits[2] + $digits[4] + $digits[6] + $digits[8];
        $sumEven = $digits[1] + $digits[3] + $digits[5] + $digits[7];
        $digits[9] = ((7 * $sumOdd) - $sumEven) % 10;

        // Calculate 11th digit
        $sum = array_sum($digits);
        $digits[10] = $sum % 10;

        return implode('', $digits);
    }
}