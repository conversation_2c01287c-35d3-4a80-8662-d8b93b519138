<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('b2b_orders', function (Blueprint $table) {
            $table->boolean('has_payment_due')->default(false)->after('subscription_status');
            $table->integer('payment_due_days')->default(0)->after('has_payment_due');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('b2b_orders', function (Blueprint $table) {
            $table->dropColumn('has_payment_due');
            $table->dropColumn('payment_due_days');
        });
    }
};
