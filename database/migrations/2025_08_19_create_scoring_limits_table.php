<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('scoring_limits', function (Blueprint $table) {
            $table->id();
            $table->string('tckn')->index()->comment('Turkish ID number');
            $table->unsignedBigInteger('user_id')->nullable()->index();
            $table->unsignedBigInteger('scoring_request_id')->nullable()->index();
            $table->integer('score')->comment('The score value');
            $table->decimal('approved_limit', 10, 2)->comment('Approved credit limit');
            $table->decimal('remaining_limit', 10, 2)->comment('Remaining available limit');
            $table->datetime('valid_until')->index()->comment('Expiry date (30 days from scoring)');
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('scoring_request_id')->references('id')->on('scoring_requests')->onDelete('set null');
            
            // Index for validity checks
            $table->index(['tckn', 'valid_until']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('scoring_limits');
    }
};