<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('completed_order_survey_mails', function (Blueprint $table) {
            $table->foreignId('survey_type_id')->nullable()->constrained('survey_types')->onDelete('cascade');
        });

        // Mevcut kayıtları completed_order survey type ile güncelle
        $completedOrderSurveyTypeId = DB::table('survey_types')->where('name', 'completed_order')->value('id');
        if ($completedOrderSurveyTypeId) {
            DB::table('completed_order_survey_mails')
                ->whereNull('survey_type_id')
                ->update(['survey_type_id' => $completedOrderSurveyTypeId]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('completed_order_survey_mails', function (Blueprint $table) {
            $table->dropForeign(['survey_type_id']);
            $table->dropColumn('survey_type_id');
        });
    }
};
