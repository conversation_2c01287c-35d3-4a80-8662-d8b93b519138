<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_transactions', function (Blueprint $table) {
            $table->after('filename', function (Blueprint $table) {
                $table->date('delay_emailed_at')->nullable();
                $table->date('offical_delay_emailed_at')->nullable();
                $table->date('notice_of_termination_emailed_at')->nullable();
                $table->string('customer_contact_status')->default('App\\States\\OrderTransactionCustomerContact\\NotContacted'); // default state is NotContacted
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_transactions', function (Blueprint $table) {
            //
        });
    }
};
