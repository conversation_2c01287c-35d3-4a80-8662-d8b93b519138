<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('b2b_orders', function (Blueprint $table) {
            $table->string('external_responsible_person_phone')->nullable()->after('external_responsible_person');
        });
    }

    public function down()
    {
        Schema::table('b2b_orders', function (Blueprint $table) {
            $table->dropColumn('external_responsible_person_phone');
        });
    }
};
