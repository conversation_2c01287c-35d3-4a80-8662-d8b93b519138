<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('transactions', 'order_transaction_id')) {
            Schema::table('transactions', function (Blueprint $table) {
                // order_id varsa ondan sonra, yoksa en sona ekle
                if (Schema::hasColumn('transactions', 'order_id')) {
                    $table->integer('order_transaction_id')->unsigned()->nullable()->after('order_id');
                } else {
                    $table->integer('order_transaction_id')->unsigned()->nullable();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('transactions', function (Blueprint $table) {
            //
        });
    }
};
