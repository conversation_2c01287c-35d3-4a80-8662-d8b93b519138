<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('order_transactions', function (Blueprint $table) {
            $table->timestamp('delay_reminder_emailed_at')->nullable()->after('delay_emailed_at');
        });
    }

    public function down()
    {
        Schema::table('order_transactions', function (Blueprint $table) {
            $table->dropColumn('delay_reminder_emailed_at');
        });
    }
};
