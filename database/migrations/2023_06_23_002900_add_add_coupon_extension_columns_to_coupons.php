<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('coupons', function (Blueprint $table) {
            $table->after('max_cart_amount', function (Blueprint $table) {
                $table->unsignedInteger('effected_months')->default(1);
                $table->boolean('is_subscription_months_rules_enabled')->default(false);
                $table->string('rule_operator')->nullable();
                $table->unsignedInteger('subscription_months_id')->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('coupons', function (Blueprint $table) {
            //
        });
    }
};
