<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('scoring_requests', function (Blueprint $table) {
            $table->string('findex_pdf_path')->nullable()->comment('S3\'teki encrypted findex PDF dosya yolu');
            $table->longText('findex_evaluation_table')->nullable()->comment('Findex evaluation table HTML verisi');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('scoring_requests', function (Blueprint $table) {
            $table->dropColumn(['findex_pdf_path', 'findex_evaluation_table']);
        });
    }
};
