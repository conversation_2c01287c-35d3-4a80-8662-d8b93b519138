<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('completed_survey_responses', function (Blueprint $table) {
            $table->id();
            $table->string('hash_code', 64)->unique();
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->tinyInteger('satisfaction_rating')->comment('1-5 arası memnuniyet puanı');
            $table->string('product_expectation')->comment('Ürün beklenti değerlendirmesi');
            $table->tinyInteger('cleanliness_rating')->comment('0-10 arası temizlik puanı');
            $table->tinyInteger('return_process_rating')->comment('0-10 arası iade süreci puanı');
            $table->tinyInteger('customer_service_rating')->comment('0-10 arası müşteri hizmetleri puanı');
            $table->tinyInteger('pricing_rating')->comment('0-10 arası fiyat değerlendirme puanı');
            $table->string('would_recommend')->comment('Tavsiye seçimi');
            $table->text('product_suggestions')->nullable()->comment('Ürün önerileri');
            $table->json('interested_services')->comment('İlgilenilen hizmetler');
            $table->text('additional_comments')->nullable()->comment('Ek yorumlar');
            $table->boolean('wants_special_offers')->default(false)->comment('Özel teklifler istiyor mu');
            $table->string('ip_address', 45)->nullable()->comment('Gönderim IP adresi');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['hash_code']);
            $table->index(['order_id']);
            $table->index(['user_id']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('completed_survey_responses');
    }
};
