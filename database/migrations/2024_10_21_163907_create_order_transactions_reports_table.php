<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_transactions_reports', function (Blueprint $table) {
            $table->id();
            $table->integer('order_id')->unsigned();
            $table->datetime('due_date');
            $table->decimal('amount', 10, 2)->unsigned();
            $table->datetime('next_try_at')->nullable();
            $table->integer('payment_status_id')->unsigned();
            $table->datetime('last_payment_check')->nullable();
            $table->text('note')->nullable();
            $table->text('bank_last_message')->nullable();
            $table->integer('moderator')->unsigned()->nullable();
            $table->string('payment_type', 255)->nullable();
            $table->integer('card_id')->unsigned();
            $table->string('filename', 255)->nullable();
            $table->date('delay_emailed_at')->nullable();
            $table->date('delay_sms_at')->nullable();
            $table->date('offical_delay_emailed_at')->nullable();
            $table->date('notice_of_termination_emailed_at')->nullable();
            $table->string('customer_contact_status', 255)->default('App\\States\\OrderTransactionCustomerContact\\NotContacted');
            $table->integer('late_payments_in_days')->nullable();
            $table->integer('recuring_order_id')->unsigned()->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_transactions_reports');
    }
};
