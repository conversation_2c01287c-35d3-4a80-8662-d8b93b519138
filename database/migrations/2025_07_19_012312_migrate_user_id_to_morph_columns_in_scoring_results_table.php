<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // User ID'leri morph kolonlarına taşı
        DB::table('scoring_results')
            ->whereNotNull('user_id')
            ->update([
                'scorable_type' => 'App\Models\User',
                'scorable_id' => DB::raw('user_id')
            ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Morph kolonlarından user_id'ye geri taşı
        DB::table('scoring_results')
            ->where('scorable_type', 'App\Models\User')
            ->update([
                'user_id' => DB::raw('scorable_id')
            ]);
    }
};
