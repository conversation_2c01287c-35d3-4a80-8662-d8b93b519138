<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_transactions_temp', function (Blueprint $table) {
            $table->after('amount', function (Blueprint $table) {
                $table->unsignedInteger('payment_status_id'); // 1: pending, 2: paid
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_transactions_temp', function (Blueprint $table) {
            //
        });
    }
};
