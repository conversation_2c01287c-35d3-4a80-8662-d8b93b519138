<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Order\Order;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Order::class)->constrained()->cascadeOnDelete();
            $table->morphs('product');
            $table->unsignedInteger('quantity');
            $table->float('price', 11, 2);
            $table->float('sub_total', 11, 2);
            $table->boolean('tax_included')->default(true);
            $table->float('tax_rate', 4, 2)->default(0);
            $table->float('tax_amount')->default(0);
            $table->string('discount_type')->nullable();
            $table->float('discount_value')->default(0);
            $table->float('discount_amount', 11, 2)->default(0);
            $table->float('total', 11, 2);
            $table->unsignedInteger('plan')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_items');
    }
};
