<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('xml_service_pricing_rates', function (Blueprint $table) {
            $table->id();
            $table->string('xml_service_name'); // aynet, alba, vs
            $table->unsignedBigInteger('subscription_months_id');
            $table->decimal('operational_rate', 8, 4)->nullable(); // Operasyonel oran
            $table->decimal('term_rate', 8, 4)->nullable(); // Vadeli oran
            $table->timestamps();

            // // Foreign key
            // $table->foreign('subscription_months_id')
            //     ->references('id')
            //     ->on('subscription_months')
            //     ->onDelete('cascade');

            // Unique constraint - her servis + kiralama ayı kombinasyonu benzersiz olmalı
            $table->unique(['xml_service_name', 'subscription_months_id'], 'xml_service_unique');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('xml_service_pricing_rates');
    }
};
