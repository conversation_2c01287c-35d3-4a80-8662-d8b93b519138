<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('scoring_sources', function (Blueprint $table) {
            $table->string('payment_result_webhook_url')->nullable()->after('send_payment_sms_on_approval');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('scoring_sources', function (Blueprint $table) {
            $table->dropColumn('payment_result_webhook_url');
        });
    }
};
