<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Tablo zaten mevcutsa migration'ı skip et
        if (Schema::hasTable('completed_order_survey_mails')) {
            return;
        }

        Schema::create('completed_order_survey_mails', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id')->comment('İlgili sipariş');
            $table->string('hash_code', 64)->unique()->comment('Anket için benzersiz hash kodu');
            $table->timestamp('sent_at')->comment('Mail gönderim tarihi');
            $table->timestamp('completed_at')->nullable()->comment('Anket tamamlanma tarihi');
            $table->boolean('is_completed')->default(false)->comment('Anket tamamlandı mı?');
            $table->timestamps();

            // Foreign key'i geçici olarak kapatıyoruz
            // $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
            $table->index(['hash_code', 'is_completed']);
            $table->index(['order_id', 'sent_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('completed_order_survey_mails');
    }
};
