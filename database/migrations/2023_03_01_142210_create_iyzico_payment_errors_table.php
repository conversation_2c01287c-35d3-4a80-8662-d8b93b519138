<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('iyzico_payment_errors', function (Blueprint $table) {
            $table->id();
            $table->string('error_code'); // İleride harf de içerebilir
            $table->text('error_message');
            $table->unsignedInteger('transaction_id');
            $table->boolean('is_handled')->default(false);
            $table->timestamp('handled_at')->nullable();
            $table->text('raw_result');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('iyzico_payment_errors');
    }
};
