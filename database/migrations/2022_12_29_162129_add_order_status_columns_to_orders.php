<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->after('user_id', function (Blueprint $table) {
                $table->string('order_number')->nullable();
                $table->dateTime('finance_approved_at')->nullable();

                $table->unsignedBigInteger('wp_order_id')->nullable();
                $table->unsignedBigInteger('wp_renewal_order_id')->nullable();
                $table->string('affiliate')->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            //
        });
    }
};
