<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('scoring_requests', function (Blueprint $table) {
            $table->string('skorlabunu_tracking_id')->nullable()->unique()->after('findex_evaluation_table');
            $table->index('skorlabunu_tracking_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('scoring_requests', function (Blueprint $table) {
            $table->dropIndex(['skorlabunu_tracking_id']);
            $table->dropColumn('skorlabunu_tracking_id');
        });
    }
};