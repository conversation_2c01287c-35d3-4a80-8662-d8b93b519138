<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('b2b_orders', function (Blueprint $table) {
            $table->string('scanned_agreement_path')->nullable()->after('payment_due_days');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('b2b_orders', function (Blueprint $table) {
            $table->dropColumn('scanned_agreement_path');
        });
    }
};
