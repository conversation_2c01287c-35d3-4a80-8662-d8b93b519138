<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // webhook_sent durumundaki ve onaylanmış kayıtları güncelle
        DB::statement("
            UPDATE scoring_requests
            SET status = 'webhook_sent_approved'
            WHERE status = 'webhook_sent'
              AND id IN (
                SELECT scoring_request_id
                FROM scoring_results_new
                WHERE is_approved = 1
              )
        ");

        // webhook_sent durumundaki ve reddedilmiş kayıtları güncelle
        DB::statement("
            UPDATE scoring_requests
            SET status = 'webhook_sent_rejected'
            WHERE status = 'webhook_sent'
              AND id IN (
                SELECT scoring_request_id
                FROM scoring_results_new
                WHERE is_approved = 0
              )
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // webhook_sent_approved durumundaki kayıtları eski haline döndür
        DB::statement("
            UPDATE scoring_requests
            SET status = 'webhook_sent'
            WHERE status = 'webhook_sent_approved'
        ");

        // webhook_sent_rejected durumundaki kayıtları eski haline döndür
        DB::statement("
            UPDATE scoring_requests
            SET status = 'webhook_sent'
            WHERE status = 'webhook_sent_rejected'
        ");
    }
};