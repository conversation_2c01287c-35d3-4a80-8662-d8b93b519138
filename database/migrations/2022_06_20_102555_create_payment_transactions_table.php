<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_transaction_id');
            $table->unsignedBigInteger('order_id');
            $table->unsignedInteger('payment_status_id'); // 1: pending, 2: paid, 3: failed etc
            $table->unsignedInteger('card_id');
            $table->unsignedInteger('moderator')->nullable(); // Eğer bir çalışan tarafından ödeme işlendi ise çalışanın id si
            $table->string('payment_type')->nullable(); // IYZICO, Havale
            $table->text('bank_last_message')->nullable();
            $table->unsignedDecimal('amount', 10, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_transactions');
    }
};
