<?php

namespace Tests\Helpers;

use App\Models\ScoringRequest;
use App\Models\ScoringSource;
use App\Models\ScoringResultNew;
use App\Models\User;
use App\Models\Note;
use App\Mail\ScoringModerationRequiredMail;
use Illuminate\Support\Facades\Mail;
use PHPUnit\Framework\Assert;

trait ScoringTestHelper
{
    /**
     * Create a scoring request with a specific score
     */
    protected function createScoringRequestWithScore(int $score, array $additionalData = []): array
    {
        $source = ScoringSource::factory()->create();
        
        $scoringRequest = ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id,
            'additional_data' => array_merge([
                'phone' => '+905551234567',
            ], $additionalData)
        ]);

        $scoringResult = ScoringResultNew::factory()->create([
            'scoring_request_id' => $scoringRequest->id,
            'score' => $score,
            'is_approved' => $score >= 275, // New threshold
            'approved_amount' => $score >= 275 ? $scoringRequest->requested_amount : 0
        ]);

        return [
            'source' => $source,
            'request' => $scoringRequest,
            'result' => $scoringResult
        ];
    }

    /**
     * Create a scoring request with partner multiplier
     */
    protected function createScoringRequestWithPartnerMultiplier(float $multiplier, int $score = 400): ScoringRequest
    {
        $source = ScoringSource::factory()->create();
        
        return ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id,
            'additional_data' => [
                'phone' => '+905551234567',
                'partner_data' => [
                    'name' => 'Test Partner',
                    'model_line_multiplier' => $multiplier
                ]
            ]
        ]);
    }

    /**
     * Create a scoring request with product value
     */
    protected function createScoringRequestWithProductValue(float $value, int $score = 400): ScoringRequest
    {
        $source = ScoringSource::factory()->create();
        
        return ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id,
            'additional_data' => [
                'phone' => '+905551234567',
                'product_value' => $value
            ]
        ]);
    }

    /**
     * Assert that moderation email was sent
     */
    protected function assertModerationEmailSent(ScoringRequest $scoringRequest): void
    {
        Mail::assertQueued(ScoringModerationRequiredMail::class, function ($mail) use ($scoringRequest) {
            return $mail->scoringRequest->id === $scoringRequest->id;
        });
    }

    /**
     * Assert that moderation email was not sent
     */
    protected function assertModerationEmailNotSent(): void
    {
        Mail::assertNothingQueued();
    }

    /**
     * Assert webhook response contains multiplier
     */
    protected function assertWebhookContainsMultiplier($response, float $expectedMultiplier): void
    {
        $data = $response->json('data');
        
        Assert::assertArrayHasKey('multiplier', $data, 'Response should contain multiplier');
        Assert::assertEquals($expectedMultiplier, $data['multiplier'], "Multiplier should be {$expectedMultiplier}");
    }

    /**
     * Assert webhook response contains max_rent_price
     */
    protected function assertWebhookContainsMaxRentPrice($response, float $expectedPrice): void
    {
        $data = $response->json('data');
        
        Assert::assertArrayHasKey('max_rent_price', $data, 'Response should contain max_rent_price');
        Assert::assertEquals($expectedPrice, $data['max_rent_price'], "Max rent price should be {$expectedPrice}");
    }

    /**
     * Set config value for testing
     */
    protected function setTestConfig(string $key, $value): void
    {
        config(['scoring.' . $key => $value]);
    }

    /**
     * Create a user with TCKN
     */
    protected function createUserWithTckn(string $tckn): User
    {
        return User::factory()->create([
            'tckn' => $tckn
        ]);
    }

    /**
     * Assert that scoring request was approved
     */
    protected function assertScoringRequestApproved(ScoringRequest $scoringRequest): void
    {
        $scoringRequest->refresh();
        $result = $scoringRequest->scoringResult;
        
        Assert::assertNotNull($result, 'Scoring result should exist');
        Assert::assertTrue($result->is_approved, 'Scoring should be approved');
        Assert::assertGreaterThan(0, $result->approved_amount, 'Approved amount should be greater than 0');
    }

    /**
     * Assert that scoring request was rejected
     */
    protected function assertScoringRequestRejected(ScoringRequest $scoringRequest): void
    {
        $scoringRequest->refresh();
        $result = $scoringRequest->scoringResult;
        
        Assert::assertNotNull($result, 'Scoring result should exist');
        Assert::assertFalse($result->is_approved, 'Scoring should be rejected');
        Assert::assertEquals(0, $result->approved_amount, 'Approved amount should be 0');
    }

    /**
     * Mock config for specific source IDs
     */
    protected function enableAutoEvaluationForSources($sourceIds): void
    {
        if (is_array($sourceIds)) {
            config(['scoring.enabled_sources' => implode(',', $sourceIds)]);
        } else {
            config(['scoring.enabled_sources' => $sourceIds]);
        }
    }

    /**
     * Disable auto evaluation
     */
    protected function disableAutoEvaluation(): void
    {
        config(['scoring.enabled' => false]);
    }

    /**
     * Set moderation threshold
     */
    protected function setModerationThreshold(float $amount): void
    {
        config(['scoring.moderation.required_above_amount' => $amount]);
    }

    /**
     * Set moderation emails
     */
    protected function setModerationEmails(array $emails): void
    {
        config(['scoring.moderation.notification_emails' => $emails]);
    }

    /**
     * Disable moderation notifications
     */
    protected function disableModerationNotifications(): void
    {
        config(['scoring.moderation.send_notifications' => false]);
    }
}