<?php

namespace Tests\Unit\Actions\OrderTransaction;

use App\Actions\OrderTransaction\sendPaymentToParasut;
use App\Models\Order\Order;
use App\Models\OrderTransaction;
use App\Models\User;
use App\Services\Parasut\Parasut;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;
use Mockery;

class SendPaymentToParasutTest extends TestCase
{
    use RefreshDatabase;

    protected OrderTransaction $orderTransaction;
    protected sendPaymentToParasut $action;

    protected function setUp(): void
    {
        parent::setUp();

        $this->action = new sendPaymentToParasut();

        // Create test data
        $user = User::factory()->create();

        // Add parasut account id as meta
        $user->meta()->create([
            'key' => 'parasut_customer_pid',
            'value' => '123456'
        ]);

        $order = Order::factory()->create([
            'user_id' => $user->id,
            'order_number' => 'TEST-001'
        ]);

        $this->orderTransaction = OrderTransaction::factory()->create([
            'order_id' => $order->id,
            'amount' => 1000,
            'due_date' => now()->addDays(30)
        ]);

        // Mock Parasut token  
        Mockery::mock('alias:' . Parasut::class)
            ->shouldReceive('getToken')
            ->andReturn('test-token');
    }

    public function test_successful_payment_saves_parasut_payment_id()
    {
        // Arrange
        Http::fake([
            'api.parasut.com/*' => Http::response([
                'data' => [
                    'id' => 'payment-123',
                    'type' => 'transactions',
                    'attributes' => [
                        'amount' => 1000
                    ]
                ]
            ], 200)
        ]);

        // Act
        ($this->action)($this->orderTransaction);

        // Assert
        $this->assertTrue(
            $this->orderTransaction->meta()
                ->where('key', 'parasut_payment_sent')
                ->exists()
        );

        $paymentMeta = $this->orderTransaction->meta()
            ->where('key', 'parasut_payment_sent')
            ->first();

        $this->assertNotNull($paymentMeta);
        $this->assertEquals('payment-123', $paymentMeta->value);
    }

    public function test_rate_limit_error_saves_wait_time_to_cache()
    {
        // Arrange
        Http::fake([
            'api.parasut.com/*' => Http::response([
                'errors' => [
                    [
                        'title' => 'Too many requests',
                        'detail' => 'Try again in 5 seconds.'
                    ]
                ]
            ], 429)
        ]);

        // Act & Assert
        try {
            ($this->action)($this->orderTransaction);
            $this->fail('Expected exception was not thrown');
        } catch (\Exception $e) {
            $this->assertEquals('RATE_LIMIT:5', $e->getMessage());
        }

        // Assert cache was set
        $this->assertNotNull(Cache::get('parasut_rate_limit_until'));
        $this->assertGreaterThan(
            now()->timestamp,
            Cache::get('parasut_rate_limit_until')
        );
    }

    public function test_rate_limit_with_different_wait_times()
    {
        // Test with 10 seconds
        Http::fake([
            'api.parasut.com/*' => Http::response([
                'errors' => [
                    [
                        'title' => 'Too many requests',
                        'detail' => 'Try again in 10 seconds.'
                    ]
                ]
            ], 429)
        ]);

        try {
            ($this->action)($this->orderTransaction);
        } catch (\Exception $e) {
            $this->assertEquals('RATE_LIMIT:10', $e->getMessage());
        }

        $rateLimitUntil = Cache::get('parasut_rate_limit_until');
        $this->assertGreaterThanOrEqual(
            now()->addSeconds(9)->timestamp,
            $rateLimitUntil
        );
        $this->assertLessThanOrEqual(
            now()->addSeconds(11)->timestamp,
            $rateLimitUntil
        );
    }

    public function test_general_error_sends_email()
    {
        // Arrange
        // Mail::fake() kullanmıyoruz çünkü Mail::html() direkt gönderim yapıyor
        // Bunun yerine mail gönderimini engellemek için null driver kullanacağız
        config(['mail.default' => 'array']);

        Http::fake([
            'api.parasut.com/*' => Http::response([
                'errors' => [
                    [
                        'title' => 'Bad Request',
                        'detail' => 'Invalid parameters'
                    ]
                ]
            ], 400)
        ]);

        // Act
        ($this->action)($this->orderTransaction);

        // Assert
        // Mail::html() test edilmesi zor olduğu için, 
        // en azından hata durumunda action'ın tamamlandığını test ediyoruz

        // TODO: Mail gönderimini test et
        $this->assertTrue(true); // Action hata vermeden tamamlandı
    }

    public function test_skips_if_payment_already_sent()
    {
        // Arrange
        $this->orderTransaction->meta()->create([
            'key' => 'parasut_payment_sent',
            'value' => 'true'
        ]);

        Http::fake();

        // Act
        ($this->action)($this->orderTransaction);

        // Assert
        Http::assertNothingSent();
    }

    public function test_cache_lock_prevents_concurrent_execution()
    {
        // Arrange
        Cache::lock('parasut_payment_' . $this->orderTransaction->id, 10)->get();

        Http::fake();

        // Act
        ($this->action)($this->orderTransaction);

        // Assert
        Http::assertNothingSent();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
