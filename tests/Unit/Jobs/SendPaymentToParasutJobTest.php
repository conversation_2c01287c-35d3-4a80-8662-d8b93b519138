<?php

namespace Tests\Unit\Jobs;

use App\Jobs\sendPaymentToParasut;
use App\Models\Order;
use App\Models\OrderTransaction;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;
use Mockery;

class SendPaymentToParasutJobTest extends TestCase
{
    use RefreshDatabase;

    protected OrderTransaction $orderTransaction;
    protected User $user;
    protected Order $order;

    protected function setUp(): void
    {
        parent::setUp();
        
        Queue::fake();
        
        // Create test data
        $this->user = User::factory()->create();
        
        // App\Models\Order\Order modelinin factory'sini kullan
        $orderModel = \App\Models\Order\Order::factory()->create([
            'user_id' => $this->user->id,
            'order_number' => 'TEST-JOB-001'
        ]);
        
        // App\Models\Order olarak kaydet (type hint uyumu için)
        $this->order = Order::find($orderModel->id);
        
        $this->orderTransaction = OrderTransaction::factory()->create([
            'order_id' => $this->order->id,
            'amount' => 2000,
            'due_date' => now()->addDays(15)
        ]);
    }

    public function test_job_waits_when_rate_limit_is_active()
    {
        // Arrange
        $futureTimestamp = now()->addSeconds(30)->timestamp;
        Cache::put('parasut_rate_limit_until', $futureTimestamp, 60);
        
        $job = new sendPaymentToParasut($this->orderTransaction);
        
        // Mock the release method
        $job = Mockery::mock(sendPaymentToParasut::class . '[release]', [$this->orderTransaction]);
        $job->shouldReceive('release')
            ->once()
            ->with(Mockery::on(function ($seconds) {
                return $seconds > 0 && $seconds <= 30;
            }));
        
        // Act
        $job->handle();
        
        // Assert - Mockery will verify the expectation automatically
        $this->addToAssertionCount(1);
    }

    public function test_job_continues_when_no_rate_limit()
    {
        // Arrange
        Cache::forget('parasut_rate_limit_until');
        
        // Mock the job to spy on the isParasutAccountExists method
        $job = Mockery::mock(sendPaymentToParasut::class . '[isParasutAccountExists]', [$this->orderTransaction]);
        $job->shouldReceive('isParasutAccountExists')
            ->once()
            ->andReturn(true); // Parasut account exists
        
        // Mock the orderTransaction methods
        $this->orderTransaction = Mockery::mock($this->orderTransaction)->makePartial();
        $this->orderTransaction->shouldReceive('sendPaymentToParasut')
            ->once();
        
        // Update job's orderTransaction
        $job->orderTransaction = $this->orderTransaction;
        
        // Act
        $job->handle();
        
        // Assert
        $this->orderTransaction->shouldHaveReceived('sendPaymentToParasut');
        $job->shouldHaveReceived('isParasutAccountExists');
        $this->addToAssertionCount(2);
    }

    public function test_job_creates_parasut_account_if_not_exists()
    {
        // Arrange
        Cache::forget('parasut_rate_limit_until');
        
        // User'ın parasut account'u olmamalı
        $this->user->meta()->where('key', 'parasut_customer_pid')->delete();
        
        // App\Models\Order tipinde bir order oluştur
        $orderForParasut = Order::find($this->order->id);
        
        // Mock user - createParasutAccount metodunu bekle
        $this->user = Mockery::mock($this->user)->makePartial();
        $this->user->shouldReceive('createParasutAccount')
            ->once()
            ->with(Mockery::on(function ($arg) use ($orderForParasut) {
                return $arg->id === $orderForParasut->id;
            }));
        
        // Update relationships
        $this->order->user = $this->user;
        $this->orderTransaction->order = $this->order;
        
        // Mock sendPaymentToParasut
        $this->orderTransaction = Mockery::mock($this->orderTransaction)->makePartial();
        $this->orderTransaction->shouldReceive('sendPaymentToParasut')
            ->once();
        
        $job = new sendPaymentToParasut($this->orderTransaction);
        
        // Act
        $job->handle();
        
        // Assert
        $this->user->shouldHaveReceived('createParasutAccount');
        $this->orderTransaction->shouldHaveReceived('sendPaymentToParasut');
        $this->addToAssertionCount(2);
    }

    public function test_job_handles_rate_limit_exception()
    {
        // Arrange
        Cache::forget('parasut_rate_limit_until');
        
        // Mock the job with both release and isParasutAccountExists methods
        $job = Mockery::mock(sendPaymentToParasut::class . '[release,isParasutAccountExists]', [$this->orderTransaction]);
        $job->shouldReceive('isParasutAccountExists')
            ->once()
            ->andReturn(true); // Parasut account exists
        $job->shouldReceive('release')
            ->once()
            ->with(15);
        
        // Mock the orderTransaction to throw rate limit exception
        $this->orderTransaction = Mockery::mock($this->orderTransaction)->makePartial();
        $this->orderTransaction->shouldReceive('sendPaymentToParasut')
            ->once()
            ->andThrow(new \Exception('RATE_LIMIT:15'));
        
        // Update job's orderTransaction
        $job->orderTransaction = $this->orderTransaction;
        
        // Act
        $job->handle();
        
        // Assert
        $job->shouldHaveReceived('release')->with(15);
        $job->shouldHaveReceived('isParasutAccountExists');
        $this->orderTransaction->shouldHaveReceived('sendPaymentToParasut');
        $this->addToAssertionCount(3);
    }

    public function test_job_rethrows_non_rate_limit_exceptions()
    {
        // Arrange
        Cache::forget('parasut_rate_limit_until');
        
        // Mock the job to spy on the isParasutAccountExists method
        $job = Mockery::mock(sendPaymentToParasut::class . '[isParasutAccountExists]', [$this->orderTransaction]);
        $job->shouldReceive('isParasutAccountExists')
            ->once()
            ->andReturn(true); // Parasut account exists
        
        // Mock the orderTransaction to throw general exception
        $this->orderTransaction = Mockery::mock($this->orderTransaction)->makePartial();
        $this->orderTransaction->shouldReceive('sendPaymentToParasut')
            ->once()
            ->andThrow(new \Exception('General error'));
        
        // Update job's orderTransaction
        $job->orderTransaction = $this->orderTransaction;
        
        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('General error');
        $job->handle();
    }

    public function test_job_skips_parasut_account_creation_if_exists()
    {
        // Arrange
        Cache::forget('parasut_rate_limit_until');
        
        // Set parasut account ID
        $this->user->meta()->create([
            'key' => 'parasut_customer_pid',
            'value' => '789012'
        ]);
        
        // Mock user - should NOT receive createParasutAccount
        $this->user = Mockery::mock($this->user)->makePartial();
        $this->user->shouldNotReceive('createParasutAccount');
        
        // Update relationships
        $this->order->user = $this->user;
        $this->orderTransaction->order = $this->order;
        
        // Mock sendPaymentToParasut
        $this->orderTransaction = Mockery::mock($this->orderTransaction)->makePartial();
        $this->orderTransaction->shouldReceive('sendPaymentToParasut')
            ->once();
        
        $job = new sendPaymentToParasut($this->orderTransaction);
        
        // Act
        $job->handle();
        
        // Assert
        $this->user->shouldNotHaveReceived('createParasutAccount');
        $this->orderTransaction->shouldHaveReceived('sendPaymentToParasut');
        $this->addToAssertionCount(2);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}