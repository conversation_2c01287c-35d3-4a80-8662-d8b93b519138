<?php

namespace Tests\Unit;

use App\Models\ScoringLimit;
use App\Models\ScoringRequest;
use App\Models\ScoringResultNew;
use App\Models\ScoringSource;
use App\Models\User;
use App\Services\ScoringResultService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class ScoringLimitDuplicationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
    }

    /** @test */
    public function it_creates_exactly_two_records_when_scoring_is_completed()
    {
        // Arrange
        $tckn = '***********';
        $user = User::factory()->create(['tckn' => $tckn]);
        $scoringSource = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'tckn' => $tckn,
            'scoring_source_id' => $scoringSource->id,
            'requested_amount' => 10000
        ]);

        $service = new ScoringResultService();

        // Get initial counts
        $initialScoringResults = ScoringResultNew::count();
        $initialScoringLimits = ScoringLimit::count();

        // Act
        $result = $service->createResult(
            scoringRequest: $scoringRequest,
            score: 750,
            isApproved: true,
            approvedAmount: 8000
        );

        // Assert - Exactly one ScoringResultNew record created
        $this->assertEquals($initialScoringResults + 1, ScoringResultNew::count());
        
        // Assert - Exactly one ScoringLimit record created
        $this->assertEquals($initialScoringLimits + 1, ScoringLimit::count());

        // Verify the records are correct
        $scoringResult = ScoringResultNew::where('scoring_request_id', $scoringRequest->id)->first();
        $this->assertNotNull($scoringResult);
        $this->assertEquals(750, $scoringResult->score);
        $this->assertEquals(8000, $scoringResult->approved_amount);

        $scoringLimit = ScoringLimit::where('tckn', $tckn)->first();
        $this->assertNotNull($scoringLimit);
        $this->assertEquals(750, $scoringLimit->score);
        $this->assertEquals(8000, $scoringLimit->approved_limit);
        $this->assertEquals(8000, $scoringLimit->remaining_limit);
    }

    /** @test */
    public function it_does_not_create_duplicate_scoring_limits_for_same_request()
    {
        // Arrange
        $tckn = '***********';
        $user = User::factory()->create(['tckn' => $tckn]);
        $scoringSource = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'tckn' => $tckn,
            'scoring_source_id' => $scoringSource->id,
            'requested_amount' => 10000
        ]);

        $service = new ScoringResultService();

        // Act - First call
        $result1 = $service->createResult(
            scoringRequest: $scoringRequest,
            score: 750,
            isApproved: true,
            approvedAmount: 8000
        );

        $firstLimitCount = ScoringLimit::where('scoring_request_id', $scoringRequest->id)->count();

        // Try to create again (should fail or be prevented by business logic)
        try {
            $result2 = $service->createResult(
                scoringRequest: $scoringRequest,
                score: 750,
                isApproved: true,
                approvedAmount: 8000
            );
        } catch (\Exception $e) {
            // Expected to fail due to duplicate scoring result
        }

        // Assert - No additional scoring limit created
        $secondLimitCount = ScoringLimit::where('scoring_request_id', $scoringRequest->id)->count();
        $this->assertEquals($firstLimitCount, $secondLimitCount);
        $this->assertEquals(1, $secondLimitCount);
    }

    /** @test */
    public function it_validates_30_day_expiry_period()
    {
        // Arrange
        $tckn = '***********';
        $user = User::factory()->create(['tckn' => $tckn]);
        $scoringSource = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'tckn' => $tckn,
            'scoring_source_id' => $scoringSource->id,
            'requested_amount' => 10000
        ]);

        $service = new ScoringResultService();

        // Act
        $result = $service->createResult(
            scoringRequest: $scoringRequest,
            score: 750,
            isApproved: true,
            approvedAmount: 8000
        );

        // Assert
        $scoringLimit = ScoringLimit::where('tckn', $tckn)->first();
        $this->assertNotNull($scoringLimit);

        // Check valid_until is 30 days from now
        $expectedValidUntil = now()->addDays(30);
        $actualValidUntil = $scoringLimit->valid_until;

        // Allow 1 minute tolerance for test execution time
        $this->assertTrue(
            $actualValidUntil->between(
                $expectedValidUntil->copy()->subMinute(),
                $expectedValidUntil->copy()->addMinute()
            ),
            "Valid until date should be 30 days from now"
        );

        // Verify it's valid now
        $this->assertTrue($scoringLimit->isValid());

        // Verify days remaining is approximately 30
        $daysRemaining = $scoringLimit->getDaysRemaining();
        $this->assertTrue(
            $daysRemaining >= 29 && $daysRemaining <= 30,
            "Days remaining should be 29 or 30, got: $daysRemaining"
        );
    }

    /** @test */
    public function it_handles_multiple_scoring_requests_for_same_tckn()
    {
        // Arrange
        $tckn = '***********';
        $user = User::factory()->create(['tckn' => $tckn]);
        $scoringSource = ScoringSource::factory()->create();
        
        // Create two different scoring requests for same TCKN
        $scoringRequest1 = ScoringRequest::factory()->create([
            'tckn' => $tckn,
            'scoring_source_id' => $scoringSource->id,
            'requested_amount' => 10000
        ]);

        $scoringRequest2 = ScoringRequest::factory()->create([
            'tckn' => $tckn,
            'scoring_source_id' => $scoringSource->id,
            'requested_amount' => 15000
        ]);

        $service = new ScoringResultService();

        // Act - Process first request
        $result1 = $service->createResult(
            scoringRequest: $scoringRequest1,
            score: 650,
            isApproved: true,
            approvedAmount: 8000
        );

        // Act - Process second request
        $result2 = $service->createResult(
            scoringRequest: $scoringRequest2,
            score: 750,
            isApproved: true,
            approvedAmount: 12000
        );

        // Assert - Two different scoring limits exist for same TCKN
        $limits = ScoringLimit::where('tckn', $tckn)->get();
        $this->assertEquals(2, $limits->count());

        // Verify each limit is linked to correct request
        $limit1 = $limits->where('scoring_request_id', $scoringRequest1->id)->first();
        $this->assertNotNull($limit1);
        $this->assertEquals(650, $limit1->score);
        $this->assertEquals(8000, $limit1->approved_limit);

        $limit2 = $limits->where('scoring_request_id', $scoringRequest2->id)->first();
        $this->assertNotNull($limit2);
        $this->assertEquals(750, $limit2->score);
        $this->assertEquals(12000, $limit2->approved_limit);

        // Verify user's current limit is the latest one
        $currentLimit = $user->getCurrentScoringLimit();
        $this->assertNotNull($currentLimit);
        $this->assertEquals($limit2->id, $currentLimit->id);
    }

    /** @test */
    public function it_only_creates_scoring_limit_when_approved()
    {
        // Arrange
        $scoringSource = ScoringSource::factory()->create();
        
        // Create two scoring requests
        $approvedRequest = ScoringRequest::factory()->create([
            'tckn' => '***********',
            'scoring_source_id' => $scoringSource->id,
            'requested_amount' => 10000
        ]);

        $rejectedRequest = ScoringRequest::factory()->create([
            'tckn' => '98765432109',
            'scoring_source_id' => $scoringSource->id,
            'requested_amount' => 10000
        ]);

        $service = new ScoringResultService();

        // Act - Process approved request
        $approvedResult = $service->createResult(
            scoringRequest: $approvedRequest,
            score: 700,
            isApproved: true,
            approvedAmount: 8000
        );

        // Act - Process rejected request
        $rejectedResult = $service->createResult(
            scoringRequest: $rejectedRequest,
            score: 300,
            isApproved: false,
            approvedAmount: 0
        );

        // Assert - Only approved request has scoring limit
        $this->assertEquals(1, ScoringLimit::where('tckn', '***********')->count());
        $this->assertEquals(0, ScoringLimit::where('tckn', '98765432109')->count());

        // Both should have scoring results
        $this->assertEquals(1, ScoringResultNew::where('scoring_request_id', $approvedRequest->id)->count());
        $this->assertEquals(1, ScoringResultNew::where('scoring_request_id', $rejectedRequest->id)->count());
    }

    /** @test */
    public function it_handles_transaction_rollback_on_failure()
    {
        // Arrange
        $tckn = '***********';
        $scoringSource = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'tckn' => $tckn,
            'scoring_source_id' => $scoringSource->id,
            'requested_amount' => 10000
        ]);

        // Mock a failure in the transaction
        DB::shouldReceive('transaction')->once()->andThrow(new \Exception('Database error'));

        $service = new ScoringResultService();

        // Act & Assert
        $this->expectException(\Exception::class);

        try {
            $result = $service->createResult(
                scoringRequest: $scoringRequest,
                score: 750,
                isApproved: true,
                approvedAmount: 8000
            );
        } catch (\Exception $e) {
            // Verify no records were created due to rollback
            $this->assertEquals(0, ScoringResultNew::where('scoring_request_id', $scoringRequest->id)->count());
            $this->assertEquals(0, ScoringLimit::where('tckn', $tckn)->count());
            
            throw $e;
        }
    }

    /** @test */
    public function it_initializes_remaining_limit_equal_to_approved_limit()
    {
        // Arrange
        $tckn = '***********';
        $scoringSource = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'tckn' => $tckn,
            'scoring_source_id' => $scoringSource->id,
            'requested_amount' => 25000
        ]);

        $service = new ScoringResultService();
        $approvedAmount = 20000;

        // Act
        $result = $service->createResult(
            scoringRequest: $scoringRequest,
            score: 800,
            isApproved: true,
            approvedAmount: $approvedAmount
        );

        // Assert
        $scoringLimit = ScoringLimit::where('tckn', $tckn)->first();
        $this->assertNotNull($scoringLimit);
        $this->assertEquals($approvedAmount, $scoringLimit->approved_limit);
        $this->assertEquals($approvedAmount, $scoringLimit->remaining_limit);
        
        // Verify they are exactly equal
        $this->assertTrue(
            $scoringLimit->approved_limit === $scoringLimit->remaining_limit,
            "Remaining limit should be exactly equal to approved limit on creation"
        );
    }
}