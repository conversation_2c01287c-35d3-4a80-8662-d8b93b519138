<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;
use Database\Factories\UserFactory;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    /**
     * Setup test environment
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Environment ve DB kontrolü - log ile birlikte
        $currentEnv = app()->environment();
        $dbConnection = config('database.default');
        $dbName = config('database.connections.' . $dbConnection . '.database');
        
        // Test environment bilgilerini logla
        \Log::channel('testing')->info('Test Environment Check', [
            'app_env' => $currentEnv,
            'db_connection' => $dbConnection,
            'db_name' => $dbName,
            'test_class' => get_class($this),
            'php_sapi' => PHP_SAPI,
            'argv' => $_SERVER['argv'] ?? [],
            'env_file' => app()->environmentFile(),
            'loaded_from' => $_ENV['APP_ENV'] ?? 'not set',
        ]);
        
        // Güvenlik kontrolü - production veya local DB kullanılıyorsa durdur
        if ($currentEnv !== 'testing' || 
            ($dbConnection === 'mysql' && !str_contains($dbName, 'test') && $dbName !== ':memory:')) {
            throw new \RuntimeException(
                "GÜVENLIK UYARISI: Test ortamı düzgün yüklenmedi!\n" .
                "Environment: {$currentEnv} (testing olmalı)\n" .
                "DB Connection: {$dbConnection}\n" .
                "DB Name: {$dbName}\n" .
                "Gerçek veritabanınızın silinmesini önlemek için test durduruldu.\n" .
                "Lütfen testing.log dosyasını kontrol edin."
            );
        }

        // Test ortamında Redis connection'ını değiştir
        $this->configureTestRedis();

        // Test database'i hazırla
        $this->setUpTestDatabase();

        // User factory'yi simple mode'a al (role gerektirmesin)
        UserFactory::enableSimple();
    }

    /**
     * Test için Redis konfigürasyonunu ayarla
     */
    protected function configureTestRedis(): void
    {
        // Test ortamında sadece gerekli job'ların test Redis'i kullanmasını sağla
        // Default Redis connection'ı koruyarak production güvenliğini sağla

        // Redis connection'ını yeniden başlat
        Redis::purge('default');
        Redis::purge('testing');
    }

    /**
     * Test database'ini hazırla
     */
    protected function setUpTestDatabase(): void
    {
        // Skorlama sistemi için gerekli migration'ları çalıştır
        if (!Schema::hasTable('scoring_sources')) {
            try {
                // Sadece skorlama sistemi migration'larını çalıştır
                Artisan::call('migrate', [
                    '--path' => 'database/migrations',
                    '--force' => true,
                ]);
            } catch (\Exception $e) {
                // Migration hatası durumunda sessizce devam et
                // Test'ler bu durumda skip edilebilir
            }
        }
    }

    /**
     * Test sonrası Redis'i temizle
     */
    protected function tearDown(): void
    {
        // Test Redis database'ini temizle
        try {
            Redis::connection('testing')->flushdb();
        } catch (\Exception $e) {
            // Redis bağlantı hatası durumunda sessizce devam et
        }

        // User factory'yi normal mode'a döndür
        UserFactory::disableSimple();

        parent::tearDown();
    }
}
