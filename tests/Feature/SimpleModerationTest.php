<?php

namespace Tests\Feature;

use App\Jobs\SendScoringModerationEmailJob;
use App\Models\ScoringRequest;
use App\Models\ScoringSource;
use App\Services\ScoringResultService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SimpleModerationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_queues_moderation_job_when_required()
    {
        Queue::fake();
        
        // Enable moderation
        config(['scoring.moderation.send_notifications' => true]);
        config(['scoring.moderation.notification_emails' => ['<EMAIL>']]);
        
        // Create scoring request
        $source = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id,
            'additional_data' => ['product_value' => 600000]
        ]);
        
        // Create service
        $service = new ScoringResultService();
        
        // Call createResult with moderation required
        $service->createResult(
            scoringRequest: $scoringRequest,
            score: 350,
            isApproved: true,
            approvedAmount: 50000,
            evaluationData: [
                'requires_moderation' => true,
                'product_value' => 600000,
                'multiplier' => 1.0
            ]
        );
        
        // Assert job was queued
        Queue::assertPushed(SendScoringModerationEmailJob::class);
    }
}