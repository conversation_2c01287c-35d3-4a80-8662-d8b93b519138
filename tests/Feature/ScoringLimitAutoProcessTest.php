<?php

namespace Tests\Feature;

use App\Jobs\SendResultToSourceJob;
use App\Models\Note;
use App\Models\ScoringLimit;
use App\Models\ScoringRequest;
use App\Models\ScoringResultNew;
use App\Models\ScoringSource;
use App\Services\ScoringLimitService;
use App\States\ScoringRequest\ApprovedState;
use App\States\ScoringRequest\PendingState;
use App\States\ScoringRequest\RejectedState;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class ScoringLimitAutoProcessTest extends TestCase
{
    use RefreshDatabase;

    protected ScoringSource $scoringSource;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
        
        // Create active scoring source
        $this->scoringSource = ScoringSource::factory()->create([
            'is_active' => true,
            'name' => 'Test Source'
        ]);

        // Fake queue to prevent jobs from running
        Queue::fake();
    }

    /** @test */
    public function it_auto_approves_request_when_sufficient_limit_exists()
    {
        // Arrange
        $tckn = '12345678901';
        $requestedAmount = 5000;
        
        // Create a valid scoring limit with sufficient balance
        $scoringLimit = ScoringLimit::factory()->create([
            'tckn' => $tckn,
            'score' => 750,
            'approved_limit' => 10000,
            'remaining_limit' => 8000,
            'valid_until' => now()->addDays(15)
        ]);

        // Act - Send scoring request via webhook
        $response = $this->postJson('/api/scoring', [
            'scoring_source_id' => $this->scoringSource->id,
            'full_name' => 'Test User',
            'tckn' => $tckn,
            'email' => '<EMAIL>',
            'birth_date' => '1990-01-01',
            'requested_amount' => $requestedAmount,
            'requested_duration_months' => 12,
            'additional_data' => [
                'phone' => '5551234567'
            ]
        ]);

        // Assert response
        $response->assertStatus(201);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'ulid',
                'status',
                'created_at',
                'auto_processed'
            ]
        ]);
        $response->assertJson([
            'success' => true,
            'data' => [
                'auto_processed' => true
            ]
        ]);

        // Assert scoring request was created and approved
        $scoringRequest = ScoringRequest::where('tckn', $tckn)->first();
        $this->assertNotNull($scoringRequest);
        $this->assertInstanceOf(ApprovedState::class, $scoringRequest->status);
        
        // Assert additional_data contains auto-process info
        $additionalData = $scoringRequest->additional_data;
        $this->assertTrue($additionalData['auto_processed']);
        $this->assertEquals('limit_based_approval', $additionalData['auto_process_type']);
        $this->assertEquals($scoringLimit->id, $additionalData['used_limit_id']);
        $this->assertEquals(8000, $additionalData['limit_before']);
        $this->assertEquals(3000, $additionalData['limit_after']);

        // Assert scoring limit was updated
        $scoringLimit->refresh();
        $this->assertEquals(3000, $scoringLimit->remaining_limit);

        // Assert scoring result was created
        $scoringResult = ScoringResultNew::where('scoring_request_id', $scoringRequest->id)->first();
        $this->assertNotNull($scoringResult);
        $this->assertTrue($scoringResult->is_approved);
        $this->assertEquals($requestedAmount, $scoringResult->approved_amount);
        $this->assertEquals(750, $scoringResult->score);

        // Assert note was created
        $note = Note::where('notable_id', $scoringRequest->id)
            ->where('notable_type', ScoringRequest::class)
            ->first();
        $this->assertNotNull($note);
        $this->assertStringContainsString('otomatik onaylandı', $note->content);

        // Assert Redis job was NOT dispatched
        Queue::assertNotPushed(\App\Jobs\SendScoringToRedisJob::class);
        
        // Assert webhook job WAS dispatched
        Queue::assertPushed(SendResultToSourceJob::class, function ($job) use ($scoringRequest) {
            return $job->getScoringRequestId() === $scoringRequest->id;
        });
    }

    /** @test */
    public function it_auto_rejects_request_when_insufficient_limit_exists()
    {
        // Arrange
        $tckn = '12345678901';
        $requestedAmount = 5000;
        
        // Create a valid scoring limit with insufficient balance
        $scoringLimit = ScoringLimit::factory()->create([
            'tckn' => $tckn,
            'score' => 650,
            'approved_limit' => 10000,
            'remaining_limit' => 3000, // Less than requested
            'valid_until' => now()->addDays(15)
        ]);

        // Act - Send scoring request via webhook
        $response = $this->postJson('/api/scoring', [
            'scoring_source_id' => $this->scoringSource->id,
            'full_name' => 'Test User',
            'tckn' => $tckn,
            'email' => '<EMAIL>',
            'birth_date' => '1990-01-01',
            'requested_amount' => $requestedAmount,
            'requested_duration_months' => 12,
            'additional_data' => [
                'phone' => '5551234567'
            ]
        ]);

        // Assert response
        $response->assertStatus(201);
        $response->assertJson([
            'success' => true,
            'data' => [
                'auto_processed' => true
            ]
        ]);

        // Assert scoring request was created and rejected
        $scoringRequest = ScoringRequest::where('tckn', $tckn)->first();
        $this->assertNotNull($scoringRequest);
        $this->assertInstanceOf(RejectedState::class, $scoringRequest->status);
        
        // Assert additional_data contains auto-process info
        $additionalData = $scoringRequest->additional_data;
        $this->assertTrue($additionalData['auto_processed']);
        $this->assertEquals('insufficient_limit_rejection', $additionalData['auto_process_type']);
        $this->assertEquals($scoringLimit->id, $additionalData['checked_limit_id']);
        $this->assertEquals(3000, $additionalData['available_limit']);
        $this->assertEquals($requestedAmount, $additionalData['requested_amount']);

        // Assert scoring limit was NOT updated
        $scoringLimit->refresh();
        $this->assertEquals(3000, $scoringLimit->remaining_limit);

        // Assert scoring result was created
        $scoringResult = ScoringResultNew::where('scoring_request_id', $scoringRequest->id)->first();
        $this->assertNotNull($scoringResult);
        $this->assertFalse($scoringResult->is_approved);
        $this->assertEquals(0, $scoringResult->approved_amount);
        $this->assertEquals(650, $scoringResult->score);

        // Assert note was created
        $note = Note::where('notable_id', $scoringRequest->id)
            ->where('notable_type', ScoringRequest::class)
            ->first();
        $this->assertNotNull($note);
        $this->assertStringContainsString('otomatik reddedildi', $note->content);
        $this->assertStringContainsString('Yetersiz limit', $note->content);

        // Assert Redis job was NOT dispatched
        Queue::assertNotPushed(\App\Jobs\SendScoringToRedisJob::class);
        
        // Assert webhook job WAS dispatched
        Queue::assertPushed(SendResultToSourceJob::class, function ($job) use ($scoringRequest) {
            return $job->getScoringRequestId() === $scoringRequest->id;
        });
    }

    /** @test */
    public function it_sends_to_redis_when_no_valid_limit_exists()
    {
        // Arrange
        $tckn = '98765432109'; // TCKN with no limits
        $requestedAmount = 5000;

        // Act - Send scoring request via webhook
        $response = $this->postJson('/api/scoring', [
            'scoring_source_id' => $this->scoringSource->id,
            'full_name' => 'Test User',
            'tckn' => $tckn,
            'email' => '<EMAIL>',
            'birth_date' => '1990-01-01',
            'requested_amount' => $requestedAmount,
            'requested_duration_months' => 12,
            'additional_data' => [
                'phone' => '5551234567'
            ]
        ]);

        // Assert response
        $response->assertStatus(201);
        $response->assertJsonMissing([
            'data' => [
                'auto_processed' => true
            ]
        ]);

        // Assert scoring request was created with pending status
        $scoringRequest = ScoringRequest::where('tckn', $tckn)->first();
        $this->assertNotNull($scoringRequest);
        $this->assertInstanceOf(PendingState::class, $scoringRequest->status);
        
        // Assert additional_data does NOT contain auto-process info
        $additionalData = $scoringRequest->additional_data;
        $this->assertArrayNotHasKey('auto_processed', $additionalData);

        // Assert no scoring result was created
        $scoringResult = ScoringResultNew::where('scoring_request_id', $scoringRequest->id)->first();
        $this->assertNull($scoringResult);

        // Assert Redis job WAS dispatched
        Queue::assertPushed(\App\Jobs\SendScoringToRedisJob::class, function ($job) use ($scoringRequest) {
            return $job->scoringRequestId === $scoringRequest->id;
        });
    }

    /** @test */
    public function it_sends_to_redis_when_limit_is_expired()
    {
        // Arrange
        $tckn = '12345678901';
        $requestedAmount = 5000;
        
        // Create an expired scoring limit
        $scoringLimit = ScoringLimit::factory()->expired()->create([
            'tckn' => $tckn,
            'score' => 750,
            'approved_limit' => 10000,
            'remaining_limit' => 8000,
            'valid_until' => now()->subDays(1) // Expired yesterday
        ]);

        // Act - Send scoring request via webhook
        $response = $this->postJson('/api/scoring', [
            'scoring_source_id' => $this->scoringSource->id,
            'full_name' => 'Test User',
            'tckn' => $tckn,
            'email' => '<EMAIL>',
            'birth_date' => '1990-01-01',
            'requested_amount' => $requestedAmount,
            'requested_duration_months' => 12,
            'additional_data' => [
                'phone' => '5551234567'
            ]
        ]);

        // Assert response
        $response->assertStatus(201);
        $response->assertJsonMissing([
            'data' => [
                'auto_processed' => true
            ]
        ]);

        // Assert scoring request was created with pending status
        $scoringRequest = ScoringRequest::where('tckn', $tckn)->first();
        $this->assertNotNull($scoringRequest);
        $this->assertInstanceOf(PendingState::class, $scoringRequest->status);

        // Assert Redis job WAS dispatched
        Queue::assertPushed(\App\Jobs\SendScoringToRedisJob::class);
    }

    /** @test */
    public function it_auto_approves_when_requested_amount_equals_remaining_limit()
    {
        // Arrange
        $tckn = '12345678901';
        $requestedAmount = 5000;
        
        // Create a valid scoring limit with exact balance
        $scoringLimit = ScoringLimit::factory()->create([
            'tckn' => $tckn,
            'score' => 700,
            'approved_limit' => 10000,
            'remaining_limit' => $requestedAmount, // Exact amount
            'valid_until' => now()->addDays(15)
        ]);

        // Act - Send scoring request via webhook
        $response = $this->postJson('/api/scoring', [
            'scoring_source_id' => $this->scoringSource->id,
            'full_name' => 'Test User',
            'tckn' => $tckn,
            'email' => '<EMAIL>',
            'birth_date' => '1990-01-01',
            'requested_amount' => $requestedAmount,
            'requested_duration_months' => 12,
            'additional_data' => [
                'phone' => '5551234567'
            ]
        ]);

        // Assert response
        $response->assertStatus(201);
        $response->assertJson([
            'success' => true,
            'data' => [
                'auto_processed' => true
            ]
        ]);

        // Assert scoring request was approved
        $scoringRequest = ScoringRequest::where('tckn', $tckn)->first();
        $this->assertNotNull($scoringRequest);
        $this->assertInstanceOf(ApprovedState::class, $scoringRequest->status);

        // Assert scoring limit was fully used
        $scoringLimit->refresh();
        $this->assertEquals(0, $scoringLimit->remaining_limit);

        // Assert Redis job was NOT dispatched
        Queue::assertNotPushed(\App\Jobs\SendScoringToRedisJob::class);
        
        // Assert webhook job WAS dispatched
        Queue::assertPushed(SendResultToSourceJob::class, function ($job) use ($scoringRequest) {
            return $job->getScoringRequestId() === $scoringRequest->id;
        });
    }

    /** @test */
    public function scoring_limit_service_handles_database_errors_gracefully()
    {
        // Arrange
        $tckn = '12345678901';
        $scoringRequest = ScoringRequest::factory()->create([
            'tckn' => $tckn,
            'scoring_source_id' => $this->scoringSource->id,
            'requested_amount' => 5000
        ]);

        // Create a valid scoring limit
        $scoringLimit = ScoringLimit::factory()->create([
            'tckn' => $tckn,
            'score' => 750,
            'approved_limit' => 10000,
            'remaining_limit' => 8000,
            'valid_until' => now()->addDays(15)
        ]);

        // Mock a database error by making the limit invalid
        $scoringLimit->delete();

        $service = new ScoringLimitService();
        
        // Act & Assert - Should return false when no valid limit found
        $result = $service->checkAndProcessWithLimit($scoringRequest);
        $this->assertFalse($result);
    }

    /** @test */
    public function it_uses_latest_valid_limit_when_multiple_exist()
    {
        // Arrange
        $tckn = '12345678901';
        $requestedAmount = 5000;
        
        // Create multiple scoring limits for same TCKN
        $oldLimit = ScoringLimit::factory()->create([
            'tckn' => $tckn,
            'score' => 600,
            'approved_limit' => 5000,
            'remaining_limit' => 3000,
            'valid_until' => now()->addDays(5),
            'created_at' => now()->subDays(10)
        ]);

        $newestLimit = ScoringLimit::factory()->create([
            'tckn' => $tckn,
            'score' => 750,
            'approved_limit' => 15000,
            'remaining_limit' => 12000,
            'valid_until' => now()->addDays(20),
            'created_at' => now()
        ]);

        // Act - Send scoring request via webhook
        $response = $this->postJson('/api/scoring', [
            'scoring_source_id' => $this->scoringSource->id,
            'full_name' => 'Test User',
            'tckn' => $tckn,
            'email' => '<EMAIL>',
            'birth_date' => '1990-01-01',
            'requested_amount' => $requestedAmount,
            'requested_duration_months' => 12,
            'additional_data' => [
                'phone' => '5551234567'
            ]
        ]);

        // Assert
        $response->assertStatus(201);
        $response->assertJson([
            'success' => true,
            'data' => [
                'auto_processed' => true
            ]
        ]);

        // Assert the newest limit was used
        $scoringRequest = ScoringRequest::where('tckn', $tckn)->first();
        $additionalData = $scoringRequest->additional_data;
        $this->assertEquals($newestLimit->id, $additionalData['used_limit_id']);

        // Assert newest limit was updated
        $newestLimit->refresh();
        $this->assertEquals(7000, $newestLimit->remaining_limit);

        // Assert old limit was not touched
        $oldLimit->refresh();
        $this->assertEquals(3000, $oldLimit->remaining_limit);
    }

    /** @test */
    public function it_creates_correct_scoring_result_for_auto_approval()
    {
        // Arrange
        $tckn = '33333333333';
        $requestedAmount = 5000;
        
        $scoringLimit = ScoringLimit::factory()->create([
            'tckn' => $tckn,
            'score' => 820,
            'approved_limit' => 20000,
            'remaining_limit' => 15000,
            'valid_until' => now()->addDays(25)
        ]);

        // Act
        $response = $this->postJson('/api/scoring', [
            'scoring_source_id' => $this->scoringSource->id,
            'full_name' => 'Test User',
            'tckn' => $tckn,
            'email' => '<EMAIL>',
            'birth_date' => '1990-01-01',
            'requested_amount' => $requestedAmount,
            'requested_duration_months' => 12,
            'additional_data' => [
                'phone' => '5551234567'
            ]
        ]);

        // Assert
        $scoringRequest = ScoringRequest::where('tckn', $tckn)->first();
        $this->assertNotNull($scoringRequest);
        $scoringResult = ScoringResultNew::where('scoring_request_id', $scoringRequest->id)->first();
        
        $this->assertNotNull($scoringResult);
        $this->assertEquals(820, $scoringResult->score); // Score from limit
        $this->assertTrue($scoringResult->is_approved);
        $this->assertEquals($requestedAmount, $scoringResult->approved_amount);
        $this->assertNotNull($scoringResult->processed_at);
    }

    /** @test */
    public function it_creates_correct_scoring_result_for_auto_rejection()
    {
        // Arrange
        $tckn = '44444444444';
        $requestedAmount = 15000;
        
        $scoringLimit = ScoringLimit::factory()->create([
            'tckn' => $tckn,
            'score' => 550,
            'approved_limit' => 10000,
            'remaining_limit' => 5000,
            'valid_until' => now()->addDays(10)
        ]);

        // Act
        $response = $this->postJson('/api/scoring', [
            'scoring_source_id' => $this->scoringSource->id,
            'full_name' => 'Test User',
            'tckn' => $tckn,
            'email' => '<EMAIL>',
            'birth_date' => '1990-01-01',
            'requested_amount' => $requestedAmount,
            'requested_duration_months' => 12,
            'additional_data' => [
                'phone' => '5551234567'
            ]
        ]);

        // Assert
        $scoringRequest = ScoringRequest::where('tckn', $tckn)->first();
        $this->assertNotNull($scoringRequest);
        $scoringResult = ScoringResultNew::where('scoring_request_id', $scoringRequest->id)->first();
        
        $this->assertNotNull($scoringResult);
        $this->assertEquals(550, $scoringResult->score); // Score from limit
        $this->assertFalse($scoringResult->is_approved);
        $this->assertEquals(0, $scoringResult->approved_amount);
        $this->assertNotNull($scoringResult->processed_at);
    }
}