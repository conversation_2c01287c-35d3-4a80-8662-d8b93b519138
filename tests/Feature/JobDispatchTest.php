<?php

namespace Tests\Feature;

use App\Jobs\SendScoringModerationEmailJob;
use App\Models\ScoringRequest;
use App\Models\ScoringSource;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class JobDispatchTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_dispatch_moderation_job()
    {
        Queue::fake();
        
        $source = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id
        ]);
        
        // Direct dispatch
        SendScoringModerationEmailJob::dispatch($scoringRequest, ['test' => 'data']);
        
        Queue::assertPushed(SendScoringModerationEmailJob::class);
    }
    
    /** @test */
    public function it_can_dispatch_moderation_job_on_queue()
    {
        Queue::fake();
        
        $source = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id
        ]);
        
        // Dispatch on queue
        SendScoringModerationEmailJob::dispatch($scoringRequest, ['test' => 'data'])
            ->onQueue('emails');
        
        Queue::assertPushed(SendScoringModerationEmailJob::class, function ($job) {
            return $job->queue === 'emails';
        });
    }
}