<?php

namespace Tests\Feature;

use App\Jobs\SendScoringModerationEmailJob;
use App\Models\ScoringRequest;
use App\Models\ScoringSource;
use App\Models\ScoringResultNew;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class IsolatedModerationTest extends TestCase
{
    use RefreshDatabase;
    
    /** @test */
    public function test_full_createResult_with_bus_fake()
    {
        Bus::fake();
        
        config(['scoring.moderation.send_notifications' => true]);
        config(['scoring.moderation.notification_emails' => ['<EMAIL>']]);
        
        $source = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id,
            'additional_data' => ['product_value' => 600000]
        ]);
        
        $service = new \App\Services\ScoringResultService();
        
        $result = $service->createResult(
            scoringRequest: $scoringRequest,
            score: 350,
            isApproved: true,
            approvedAmount: 50000,
            evaluationData: [
                'requires_moderation' => true,
                'product_value' => 600000,
                'multiplier' => 1.0
            ]
        );
        
        $this->assertNotNull($result);
        
        Bus::assertDispatched(SendScoringModerationEmailJob::class);
    }

    /** @test */
    public function test_service_method_directly()
    {
        Bus::fake();
        
        config(['scoring.moderation.send_notifications' => true]);
        
        $source = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id
        ]);
        
        // Direct call to private method using reflection
        $service = new \App\Services\ScoringResultService();
        $method = new \ReflectionMethod($service, 'sendModerationNotification');
        $method->setAccessible(true);
        
        $method->invoke($service, $scoringRequest, [
            'requires_moderation' => true,
            'product_value' => 600000
        ]);
        
        Bus::assertDispatched(SendScoringModerationEmailJob::class);
    }
    
    /** @test */  
    public function test_createResult_without_transaction()
    {
        Bus::fake();
        
        config(['scoring.moderation.send_notifications' => true]);
        
        $source = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id
        ]);
        
        // Create result directly without using service
        $result = ScoringResultNew::create([
            'scoring_request_id' => $scoringRequest->id,
            'score' => 350,
            'is_approved' => true,
            'approved_amount' => 50000,
            'processed_at' => now()
        ]);
        
        // Dispatch job directly
        SendScoringModerationEmailJob::dispatch($scoringRequest, [
            'requires_moderation' => true,
            'product_value' => 600000
        ]);
        
        Bus::assertDispatched(SendScoringModerationEmailJob::class);
    }
    
    /** @test */
    public function test_job_dispatch_with_onQueue()
    {
        Bus::fake();
        
        $source = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id
        ]);
        
        // Dispatch with onQueue as in service
        SendScoringModerationEmailJob::dispatch($scoringRequest, ['test' => 'data'])
            ->onQueue('emails');
        
        Bus::assertDispatched(SendScoringModerationEmailJob::class);
    }
}