<?php

namespace Tests\Feature;

use App\Models\ScoringRequest;
use App\Models\ScoringSource;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;
use Tests\Traits\UsesTestRedis;

class ProcessOldFindexRequestsTest extends TestCase
{
    use RefreshDatabase, UsesTestRedis;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Test için gerekli tabloları oluştur
        $this->artisan('migrate');
        
        // Test Redis setup
        $this->setUpTestRedis();
        
        // Test için bir ScoringSource oluştur
        ScoringSource::create([
            'id' => 1,
            'name' => 'Test Source',
            'webhook_url' => 'https://example.com/webhook',
            'is_active' => true,
        ]);
    }

    protected function tearDown(): void
    {
        // Test sonrası Redis'i temizle
        $this->tearDownTestRedis();
        
        parent::tearDown();
    }

    /** @test */
    public function it_completes_successfully_when_no_data_in_redis()
    {
        // Redis'te veri olmadığından emin ol
        Redis::del('kb-findex-ba:customers');
        
        // Komutu çalıştır
        $this->artisan('scoring:process-old-findex-requests')
            ->expectsOutput('No customers found in Redis.')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_keeps_recent_findex_requests_in_redis()
    {
        // 3 günlük bir kayıt oluştur
        $customer = [
            'id' => 'test-ulid-1',
            'ad_soyad' => 'Test User',
            'tckn' => '12345678901',
            'gsm' => '5551234567',
            'email' => '<EMAIL>',
            'dogum_tarihi' => '1990-01-01',
            'request_received_at' => now()->subDays(3)->format('Y-m-d H:i:s'),
            'findex_requested_at' => now()->subDays(3)->format('Y-m-d H:i:s'),
            'completed_at' => null,
            'findex_journal_id' => null
        ];
        
        Redis::set('kb-findex-ba:customers', json_encode([$customer]));
        
        // Komutu çalıştır
        $this->artisan('scoring:process-old-findex-requests')
            ->expectsOutput('Found 1 customers in Redis.')
            ->expectsOutputToContain('Findex request is 3 days old. Keeping in Redis.')
            ->expectsOutput('Completed. Processed 0 old Findex requests.')
            ->assertExitCode(0);
        
        // Redis'te hala var olmalı
        $remainingCustomers = json_decode(Redis::get('kb-findex-ba:customers'), true);
        $this->assertCount(1, $remainingCustomers);
        $this->assertEquals('test-ulid-1', $remainingCustomers[0]['id']);
    }

    /** @test */
    public function it_keeps_not_yet_sent_to_findex_requests_in_redis()
    {
        // findex_requested_at null olan kayıt
        $customer = [
            'id' => 'test-ulid-2',
            'ad_soyad' => 'Test User 2',
            'tckn' => '12345678902',
            'gsm' => '5551234568',
            'email' => '<EMAIL>',
            'dogum_tarihi' => '1990-01-02',
            'request_received_at' => now()->subDays(10)->format('Y-m-d H:i:s'),
            'findex_requested_at' => null,
            'completed_at' => null,
            'findex_journal_id' => null
        ];
        
        Redis::set('kb-findex-ba:customers', json_encode([$customer]));
        
        // Komutu çalıştır
        $this->artisan('scoring:process-old-findex-requests')
            ->expectsOutput('Completed. Processed 0 old Findex requests.')
            ->assertExitCode(0);
        
        // Redis'te hala var olmalı
        $remainingCustomers = json_decode(Redis::get('kb-findex-ba:customers'), true);
        $this->assertCount(1, $remainingCustomers);
        $this->assertEquals('test-ulid-2', $remainingCustomers[0]['id']);
    }

    /** @test */
    public function it_processes_old_findex_requests_with_scoring_request()
    {
        // ScoringRequest oluştur
        $scoringRequest = ScoringRequest::create([
            'ulid' => 'test-ulid-3',
            'scoring_source_id' => 1,
            'full_name' => 'Test User 3',
            'tckn' => '12345678903',
            'email' => '<EMAIL>',
            'birth_date' => '1990-01-03',
            'requested_amount' => 1000,
            'requested_duration_months' => 12,
            'additional_data' => ['existing_key' => 'existing_value'],
        ]);
        
        // 16 günlük bir kayıt oluştur (15 günden eski olması gerekiyor)
        $customer = [
            'id' => 'test-ulid-3',
            'ad_soyad' => 'Test User 3',
            'tckn' => '12345678903',
            'gsm' => '5551234569',
            'email' => '<EMAIL>',
            'dogum_tarihi' => '1990-01-03',
            'request_received_at' => now()->subDays(16)->format('Y-m-d H:i:s'),
            'findex_requested_at' => now()->subDays(16)->format('Y-m-d H:i:s'),
            'completed_at' => null,
            'findex_journal_id' => null
        ];
        
        Redis::set('kb-findex-ba:customers', json_encode([$customer]));
        
        // Komutu çalıştır
        $this->artisan('scoring:process-old-findex-requests')
            ->expectsOutputToContain('Findex request is 16 days old.')
            ->expectsOutputToContain('Updated ScoringRequest ' . $scoringRequest->id . ' with Redis data.')
            ->expectsOutput('All customers processed. Redis key deleted.')
            ->expectsOutput('Completed. Processed 1 old Findex requests.')
            ->assertExitCode(0);
        
        // ScoringRequest güncellenmeli
        $scoringRequest->refresh();
        $this->assertArrayHasKey('redis_data', $scoringRequest->additional_data);
        $this->assertArrayHasKey('processed_from_redis_at', $scoringRequest->additional_data);
        $this->assertArrayHasKey('existing_key', $scoringRequest->additional_data);
        $this->assertEquals('existing_value', $scoringRequest->additional_data['existing_key']);
        $this->assertEquals($customer, $scoringRequest->additional_data['redis_data']);
        
        // Redis'te olmamalı
        $this->assertNull(Redis::get('kb-findex-ba:customers'));
    }

    /** @test */
    public function it_keeps_old_requests_without_scoring_request_in_redis()
    {
        // 16 günlük bir kayıt oluştur (ScoringRequest yok)
        $customer = [
            'id' => 'non-existent-ulid',
            'ad_soyad' => 'Test User 4',
            'tckn' => '12345678904',
            'gsm' => '5551234570',
            'email' => '<EMAIL>',
            'dogum_tarihi' => '1990-01-04',
            'request_received_at' => now()->subDays(16)->format('Y-m-d H:i:s'),
            'findex_requested_at' => now()->subDays(16)->format('Y-m-d H:i:s'),
            'completed_at' => null,
            'findex_journal_id' => null
        ];
        
        Redis::set('kb-findex-ba:customers', json_encode([$customer]));
        
        // Komutu çalıştır
        $this->artisan('scoring:process-old-findex-requests')
            ->expectsOutputToContain('ScoringRequest not found for ULID: non-existent-ulid. Keeping in Redis.')
            ->expectsOutput('Completed. Processed 0 old Findex requests.')
            ->assertExitCode(0);
        
        // Redis'te hala var olmalı
        $remainingCustomers = json_decode(Redis::get('kb-findex-ba:customers'), true);
        $this->assertCount(1, $remainingCustomers);
        $this->assertEquals('non-existent-ulid', $remainingCustomers[0]['id']);
    }

    /** @test */
    public function it_handles_mixed_scenarios_correctly()
    {
        // ScoringRequest'ler oluştur
        $scoringRequest1 = ScoringRequest::create([
            'ulid' => 'old-with-sr',
            'scoring_source_id' => 1,
            'full_name' => 'Old User With SR',
            'tckn' => '12345678905',
            'email' => '<EMAIL>',
            'birth_date' => '1990-01-05',
            'requested_amount' => 2000,
            'requested_duration_months' => 6,
        ]);
        
        // Karışık müşteri listesi
        $customers = [
            // 1. Yeni tarihli (3 gün) - kalmalı
            [
                'id' => 'recent-request',
                'ad_soyad' => 'Recent User',
                'tckn' => '12345678906',
                'gsm' => '5551234571',
                'email' => '<EMAIL>',
                'dogum_tarihi' => '1990-01-06',
                'request_received_at' => now()->subDays(3)->format('Y-m-d H:i:s'),
                'findex_requested_at' => now()->subDays(3)->format('Y-m-d H:i:s'),
                'completed_at' => null,
                'findex_journal_id' => null
            ],
            // 2. Henüz gönderilmemiş - kalmalı
            [
                'id' => 'not-sent',
                'ad_soyad' => 'Not Sent User',
                'tckn' => '12345678907',
                'gsm' => '5551234572',
                'email' => '<EMAIL>',
                'dogum_tarihi' => '1990-01-07',
                'request_received_at' => now()->subDays(10)->format('Y-m-d H:i:s'),
                'findex_requested_at' => null,
                'completed_at' => null,
                'findex_journal_id' => null
            ],
            // 3. Eski tarihli (16 gün), ScoringRequest var - işlenmeli
            [
                'id' => 'old-with-sr',
                'ad_soyad' => 'Old User With SR',
                'tckn' => '12345678905',
                'gsm' => '5551234573',
                'email' => '<EMAIL>',
                'dogum_tarihi' => '1990-01-05',
                'request_received_at' => now()->subDays(16)->format('Y-m-d H:i:s'),
                'findex_requested_at' => now()->subDays(16)->format('Y-m-d H:i:s'),
                'completed_at' => null,
                'findex_journal_id' => null
            ],
            // 4. Eski tarihli (17 gün), ScoringRequest yok - kalmalı
            [
                'id' => 'old-without-sr',
                'ad_soyad' => 'Old User Without SR',
                'tckn' => '12345678908',
                'gsm' => '5551234574',
                'email' => '<EMAIL>',
                'dogum_tarihi' => '1990-01-08',
                'request_received_at' => now()->subDays(17)->format('Y-m-d H:i:s'),
                'findex_requested_at' => now()->subDays(17)->format('Y-m-d H:i:s'),
                'completed_at' => null,
                'findex_journal_id' => null
            ],
        ];
        
        Redis::set('kb-findex-ba:customers', json_encode($customers));
        
        // Komutu çalıştır
        $this->artisan('scoring:process-old-findex-requests')
            ->expectsOutput('Found 4 customers in Redis.')
            ->expectsOutput('Completed. Processed 1 old Findex requests.')
            ->assertExitCode(0);
        
        // Redis'te 3 kayıt kalmalı
        $remainingCustomers = json_decode(Redis::get('kb-findex-ba:customers'), true);
        $this->assertCount(3, $remainingCustomers);
        
        // Kalan kayıtların ID'lerini kontrol et
        $remainingIds = array_column($remainingCustomers, 'id');
        $this->assertContains('recent-request', $remainingIds);
        $this->assertContains('not-sent', $remainingIds);
        $this->assertContains('old-without-sr', $remainingIds);
        $this->assertNotContains('old-with-sr', $remainingIds);
        
        // ScoringRequest güncellenmeli
        $scoringRequest1->refresh();
        $this->assertArrayHasKey('redis_data', $scoringRequest1->additional_data);
        $this->assertEquals('old-with-sr', $scoringRequest1->additional_data['redis_data']['id']);
    }
}