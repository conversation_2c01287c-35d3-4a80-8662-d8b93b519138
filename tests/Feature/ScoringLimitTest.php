<?php

namespace Tests\Feature;

use App\Models\ScoringLimit;
use App\Models\ScoringRequest;
use App\Models\ScoringResultNew;
use App\Models\ScoringSource;
use App\Models\User;
use App\Services\ScoringResultService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ScoringLimitTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
    }

    /** @test */
    public function it_creates_scoring_limit_when_scoring_result_is_approved()
    {
        // Arrange
        $tckn = '12345678901';
        $user = User::factory()->create(['tckn' => $tckn]);
        $scoringSource = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'tckn' => $tckn,
            'scoring_source_id' => $scoringSource->id,
            'requested_amount' => 10000
        ]);

        $service = new ScoringResultService();

        // Act
        $result = $service->createResult(
            scoringRequest: $scoringRequest,
            score: 750,
            isApproved: true,
            approvedAmount: 8000
        );

        // Assert
        $this->assertDatabaseHas('scoring_results_new', [
            'scoring_request_id' => $scoringRequest->id,
            'score' => 750,
            'is_approved' => true,
            'approved_amount' => 8000
        ]);

        $this->assertDatabaseHas('scoring_limits', [
            'tckn' => $tckn,
            'user_id' => $user->id,
            'scoring_request_id' => $scoringRequest->id,
            'score' => 750,
            'approved_limit' => 8000,
            'remaining_limit' => 8000
        ]);

        // Check validity period is 30 days
        $scoringLimit = ScoringLimit::where('tckn', $tckn)->first();
        $this->assertTrue($scoringLimit->isValid());
        $this->assertEquals(29, $scoringLimit->getDaysRemaining()); // Could be 29 or 30 depending on execution time
    }

    /** @test */
    public function it_does_not_create_scoring_limit_when_scoring_result_is_rejected()
    {
        // Arrange
        $tckn = '12345678901';
        $scoringSource = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'tckn' => $tckn,
            'scoring_source_id' => $scoringSource->id,
            'requested_amount' => 10000
        ]);

        $service = new ScoringResultService();

        // Act
        $result = $service->createResult(
            scoringRequest: $scoringRequest,
            score: 350,
            isApproved: false,
            approvedAmount: 0
        );

        // Assert
        $this->assertDatabaseHas('scoring_results_new', [
            'scoring_request_id' => $scoringRequest->id,
            'score' => 350,
            'is_approved' => false,
            'approved_amount' => 0
        ]);

        $this->assertDatabaseMissing('scoring_limits', [
            'tckn' => $tckn,
            'scoring_request_id' => $scoringRequest->id
        ]);
    }

    /** @test */
    public function it_updates_user_meta_with_current_scoring_limit_id()
    {
        // Arrange
        $tckn = '12345678901';
        $user = User::factory()->create(['tckn' => $tckn]);
        $scoringSource = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'tckn' => $tckn,
            'scoring_source_id' => $scoringSource->id,
            'requested_amount' => 10000
        ]);

        $service = new ScoringResultService();

        // Act
        $result = $service->createResult(
            scoringRequest: $scoringRequest,
            score: 750,
            isApproved: true,
            approvedAmount: 8000
        );

        // Assert
        $scoringLimit = ScoringLimit::where('tckn', $tckn)->first();
        $this->assertNotNull($scoringLimit);

        $meta = $user->meta()->where('key', 'current_scoring_limit_id')->first();
        $this->assertNotNull($meta);
        $this->assertEquals($scoringLimit->id, $meta->value);

        // Verify user can get current scoring limit through trait
        $currentLimit = $user->getCurrentScoringLimit();
        $this->assertNotNull($currentLimit);
        $this->assertEquals($scoringLimit->id, $currentLimit->id);
    }

    /** @test */
    public function it_creates_scoring_limit_without_user_if_user_does_not_exist()
    {
        // Arrange
        $tckn = '98765432109';
        $scoringSource = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'tckn' => $tckn,
            'scoring_source_id' => $scoringSource->id,
            'requested_amount' => 10000
        ]);

        $service = new ScoringResultService();

        // Act
        $result = $service->createResult(
            scoringRequest: $scoringRequest,
            score: 650,
            isApproved: true,
            approvedAmount: 5000
        );

        // Assert
        $this->assertDatabaseHas('scoring_limits', [
            'tckn' => $tckn,
            'user_id' => null,
            'scoring_request_id' => $scoringRequest->id,
            'score' => 650,
            'approved_limit' => 5000,
            'remaining_limit' => 5000
        ]);
    }

    /** @test */
    public function it_can_check_validity_of_scoring_limit()
    {
        // Arrange
        $validLimit = ScoringLimit::factory()->create([
            'valid_until' => now()->addDays(15)
        ]);
        
        $expiredLimit = ScoringLimit::factory()->expired()->create();

        // Assert
        $this->assertTrue($validLimit->isValid());
        $this->assertFalse($validLimit->isExpired());
        $this->assertEquals(14, $validLimit->getDaysRemaining()); // Could be 14 or 15

        $this->assertFalse($expiredLimit->isValid());
        $this->assertTrue($expiredLimit->isExpired());
        $this->assertEquals(0, $expiredLimit->getDaysRemaining());
    }

    /** @test */
    public function it_can_use_and_restore_limit()
    {
        // Arrange
        $scoringLimit = ScoringLimit::factory()->create([
            'approved_limit' => 10000,
            'remaining_limit' => 10000
        ]);

        // Act - Use some limit
        $result1 = $scoringLimit->useLimit(3000);
        
        // Assert
        $this->assertTrue($result1);
        $this->assertEquals(7000, $scoringLimit->fresh()->remaining_limit);

        // Act - Try to use more than available
        $result2 = $scoringLimit->fresh()->useLimit(8000);
        
        // Assert
        $this->assertFalse($result2);
        $this->assertEquals(7000, $scoringLimit->fresh()->remaining_limit);

        // Act - Restore some limit
        $result3 = $scoringLimit->fresh()->restoreLimit(2000);
        
        // Assert
        $this->assertTrue($result3);
        $this->assertEquals(9000, $scoringLimit->fresh()->remaining_limit);

        // Act - Try to restore beyond approved limit
        $result4 = $scoringLimit->fresh()->restoreLimit(5000);
        
        // Assert
        $this->assertTrue($result4);
        $this->assertEquals(10000, $scoringLimit->fresh()->remaining_limit); // Should cap at approved_limit
    }

    /** @test */
    public function it_can_get_latest_valid_limit_by_tckn()
    {
        // Arrange
        $tckn = '12345678901';
        
        // Create multiple limits for same TCKN
        $oldLimit = ScoringLimit::factory()->create([
            'tckn' => $tckn,
            'created_at' => now()->subDays(10)
        ]);

        $expiredLimit = ScoringLimit::factory()->expired()->create([
            'tckn' => $tckn,
            'created_at' => now()->subDays(5)
        ]);

        $currentLimit = ScoringLimit::factory()->create([
            'tckn' => $tckn,
            'created_at' => now()
        ]);

        // Act
        $latestValid = ScoringLimit::getLatestValidByTckn($tckn);

        // Assert
        $this->assertNotNull($latestValid);
        $this->assertEquals($currentLimit->id, $latestValid->id);
    }

    /** @test */
    public function trait_methods_work_correctly_on_user()
    {
        // Arrange
        $user = User::factory()->create(['tckn' => '12345678901']);
        
        // Initially no limits
        $this->assertFalse($user->hasValidScoringLimit());
        $this->assertEquals(0, $user->getAvailableLimit());
        $this->assertEquals(0, $user->getApprovedLimit());

        // Create a scoring limit for user
        $scoringLimit = ScoringLimit::factory()->forUser($user)->create([
            'approved_limit' => 15000,
            'remaining_limit' => 12000
        ]);

        // Set as current in meta
        $user->setCurrentScoringLimit($scoringLimit);

        // Assert
        $this->assertTrue($user->hasValidScoringLimit());
        $this->assertEquals(12000, $user->getAvailableLimit());
        $this->assertEquals(15000, $user->getApprovedLimit());

        // Test updating remaining limit
        $result = $user->updateRemainingLimit(2000); // Use 2000
        $this->assertTrue($result);
        $this->assertEquals(10000, $user->getAvailableLimit());

        $result = $user->updateRemainingLimit(-1000); // Restore 1000
        $this->assertTrue($result);
        $this->assertEquals(11000, $user->getAvailableLimit());
    }

    /** @test */
    public function trait_methods_work_correctly_on_scoring_request()
    {
        // Arrange
        $scoringRequest = ScoringRequest::factory()->create();
        
        // Initially no limits
        $this->assertFalse($scoringRequest->hasValidScoringLimit());
        
        // Create a scoring limit for request
        $scoringLimit = ScoringLimit::factory()->forScoringRequest($scoringRequest)->create([
            'approved_limit' => 20000,
            'remaining_limit' => 18000
        ]);

        // Assert
        $this->assertTrue($scoringRequest->hasValidScoringLimit());
        $this->assertEquals(18000, $scoringRequest->getAvailableLimit());
        $this->assertEquals(20000, $scoringRequest->getApprovedLimit());
        
        // Get latest limit
        $latest = $scoringRequest->getLatestScoringLimit();
        $this->assertNotNull($latest);
        $this->assertEquals($scoringLimit->id, $latest->id);
    }
}