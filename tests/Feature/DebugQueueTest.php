<?php

namespace Tests\Feature;

use App\Jobs\SendScoringModerationEmailJob;
use App\Models\ScoringRequest;
use App\Models\ScoringSource;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class DebugQueueTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function test_direct_job_dispatch()
    {
        Queue::fake();
        
        $source = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id
        ]);
        
        // Direct dispatch
        SendScoringModerationEmailJob::dispatch($scoringRequest, ['test' => 'data']);
        
        Queue::assertPushed(SendScoringModerationEmailJob::class);
    }
    
    /** @test */
    public function test_dispatch_with_queue_name()
    {
        Queue::fake();
        
        $source = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id
        ]);
        
        // Dispatch with queue name (as done in ScoringResultService)
        SendScoringModerationEmailJob::dispatch($scoringRequest, ['test' => 'data'])
            ->onQueue('emails');
        
        Queue::assertPushed(SendScoringModerationEmailJob::class);
    }
    
    /** @test */
    public function test_sendModerationNotification_method()
    {
        Queue::fake();
        
        config(['scoring.moderation.send_notifications' => true]);
        
        $source = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id
        ]);
        
        // Use reflection to call private method
        $service = new \App\Services\ScoringResultService();
        $method = new \ReflectionMethod($service, 'sendModerationNotification');
        $method->setAccessible(true);
        
        $method->invoke($service, $scoringRequest, [
            'requires_moderation' => true,
            'product_value' => 600000
        ]);
        
        Queue::assertPushed(SendScoringModerationEmailJob::class);
    }
}