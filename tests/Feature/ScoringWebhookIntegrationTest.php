<?php

namespace Tests\Feature;

use App\Events\ScoringCompleted;
use App\Jobs\SendResultToSourceJob;
use App\Jobs\SendScoringToRedisJob;
use App\Models\ScoringRequest;
use App\Models\ScoringSource;
use App\Models\ScoringResultNew;
use App\States\ScoringRequest\ApprovedState;
use App\States\ScoringRequest\PendingState;
use App\States\ScoringRequest\RejectedState;
use App\States\ScoringRequest\ScoredState;
use App\States\ScoringRequest\SentToRedisState;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;
use Tests\Traits\UsesTestRedis;

class ScoringWebhookIntegrationTest extends TestCase
{
    use RefreshDatabase, UsesTestRedis;

    protected function setUp(): void
    {
        parent::setUp();

        // Test için gerekli tabloları oluştur
        $this->artisan('migrate');

        // Test Redis setup
        $this->setUpTestRedis();
    }

    protected function tearDown(): void
    {
        // Test sonrası Redis'i temizle
        $this->tearDownTestRedis();

        parent::tearDown();
    }

    /** @test */
    public function it_handles_complete_scoring_workflow_with_approval()
    {
        // 1. Setup
        $source = ScoringSource::factory()->create([
            'name' => 'Test Kredi Kuruluşu',
            'webhook_url' => 'https://httpbin.org/post',
            'is_active' => true
        ]);

        Queue::fake();
        Event::fake();
        Http::fake(['*' => Http::response(['success' => true], 200)]);

        // 2. Webhook request gönder
        $requestData = [
            'scoring_source_id' => $source->id,
            'full_name' => 'Test Kullanıcı',
            'tckn' => '12345678901',
            'email' => '<EMAIL>',
            'birth_date' => '1990-01-01',
            'requested_amount' => 100000,
            'requested_duration_months' => 24,
            'additional_data' => [
                'phone' => '+905551234567',
                'address' => 'Test Adres'
            ]
        ];

        $response = $this->postJson('/api/scoring', $requestData);

        // 3. Webhook response kontrolü
        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => ['ulid', 'status']
            ]);

        $ulid = $response->json('data.ulid');
        $scoringRequest = ScoringRequest::where('ulid', $ulid)->first();

        $this->assertNotNull($scoringRequest);
        $this->assertInstanceOf(PendingState::class, $scoringRequest->status);

        // 4. Job dispatch kontrolü
        Queue::assertPushed(SendScoringToRedisJob::class, function ($job) use ($scoringRequest) {
            return $job->scoringRequestId === $scoringRequest->id;
        });

        // 5. Redis job'ını manuel çalıştır
        $redisJob = new SendScoringToRedisJob($scoringRequest->id);
        $redisJob->handle();

        // 6. Redis'te veri kontrolü
        $redisData = Redis::connection('testing')->get('kb-findex-ba:customers');
        $this->assertNotNull($redisData);

        $customersArray = json_decode($redisData, true);
        $this->assertCount(1, $customersArray);
        $this->assertEquals($ulid, $customersArray[0]['id']);

        // 7. State güncelleme kontrolü
        $scoringRequest->refresh();
        $this->assertNotNull($scoringRequest->redis_sent_at);
        $this->assertInstanceOf(SentToRedisState::class, $scoringRequest->status);

        // 8. Skorlama sonucu gönder (Findex'ten gelir)
        $scoringResultData = [
            'ulid' => $ulid,
            'score' => 425,
            'findex_journal_id' => 'TEST_JOURNAL_123',
            'processed_at' => now()->toISOString()
        ];

        $resultResponse = $this->postJson('/api/scoring/result', $scoringResultData);

        // 9. Skorlama sonucu response kontrolü
        $resultResponse->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'ulid' => $ulid,
                    'score' => 425,
                    'is_approved' => true
                ]
            ]);

        // 10. Database state kontrolü
        $scoringRequest->refresh();
        $this->assertEquals(ApprovedState::class, get_class($scoringRequest->status));

        $result = $scoringRequest->scoringResult;
        $this->assertNotNull($result);
        $this->assertEquals(425, $result->score);
        $this->assertTrue($result->is_approved);
        $this->assertEquals(100000, $result->approved_amount);

        // 11. Event tetikleme kontrolü
        Event::assertDispatched(ScoringCompleted::class, function ($event) use ($scoringRequest) {
            return $event->scoringRequest->id === $scoringRequest->id;
        });
    }

    /** @test */
    public function it_handles_complete_scoring_workflow_with_rejection()
    {
        // 1. Setup
        $source = ScoringSource::factory()->create();
        Queue::fake();
        Event::fake();

        // 2. Webhook request
        $requestData = [
            'scoring_source_id' => $source->id,
            'full_name' => 'Red Test Kullanıcı',
            'tckn' => '98765432109',
            'email' => '<EMAIL>',
            'birth_date' => '1985-06-15',
            'requested_amount' => 50000,
            'requested_duration_months' => 12,
            'additional_data' => ['phone' => '+905559876543']
        ];

        $response = $this->postJson('/api/scoring', $requestData);
        $ulid = $response->json('data.ulid');

        // 3. Redis job çalıştır
        $scoringRequest = ScoringRequest::where('ulid', $ulid)->first();
        $redisJob = new SendScoringToRedisJob($scoringRequest->id);
        $redisJob->handle();

        // 4. Düşük skor ile sonuç gönder
        $scoringResultData = [
            'ulid' => $ulid,
            'score' => 250, // Below 275 threshold - will be rejected
            'findex_journal_id' => 'TEST_JOURNAL_456',
            'processed_at' => now()->toISOString()
        ];

        $resultResponse = $this->postJson('/api/scoring/result', $scoringResultData);

        // 5. Red response kontrolü
        $resultResponse->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'ulid' => $ulid,
                    'score' => 250,
                    'is_approved' => false
                ]
            ]);

        // 6. Database kontrolü
        $scoringRequest->refresh();
        $this->assertEquals(RejectedState::class, get_class($scoringRequest->status));

        $result = $scoringRequest->scoringResult;
        $this->assertFalse($result->is_approved);
        $this->assertEquals(0, $result->approved_amount);
    }

    /** @test */
    public function it_handles_validation_errors_correctly()
    {
        $source = ScoringSource::factory()->create();

        // Missing required fields
        $invalidData = [
            'scoring_source_id' => $source->id,
            'full_name' => '', // Empty
            'tckn' => '123', // Too short
            'email' => 'invalid-email', // Invalid format
        ];

        $response = $this->postJson('/api/scoring', $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['full_name', 'tckn', 'email', 'birth_date', 'requested_amount', 'requested_duration_months']);
    }

    /** @test */
    public function it_handles_inactive_scoring_source()
    {
        $inactiveSource = ScoringSource::factory()->inactive()->create();

        $requestData = [
            'scoring_source_id' => $inactiveSource->id,
            'full_name' => 'Test User',
            'tckn' => '12345678901',
            'email' => '<EMAIL>',
            'birth_date' => '1990-01-01',
            'requested_amount' => 50000,
            'requested_duration_months' => 12,
        ];

        $response = $this->postJson('/api/scoring', $requestData);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Skorlama kaynağı aktif değil'
            ]);
    }

    /** @test */
    public function it_handles_duplicate_scoring_results()
    {
        // 1. Setup initial scoring
        $source = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id
        ]);

        // Create initial result
        ScoringResultNew::factory()->create([
            'scoring_request_id' => $scoringRequest->id,
            'score' => 400,
            'is_approved' => true
        ]);

        // 2. Try to send duplicate result
        $duplicateData = [
            'ulid' => $scoringRequest->ulid,
            'score' => 300,
            'findex_journal_id' => 'DUPLICATE_JOURNAL',
            'processed_at' => now()->toISOString()
        ];

        $response = $this->postJson('/api/scoring/result', $duplicateData);

        // 3. Should reject duplicate
        $response->assertStatus(409)
            ->assertJson([
                'success' => false,
                'message' => 'Bu talep zaten skorlanmış'
            ]);
    }

    /** @test */
    public function it_handles_manual_processing_skip_logic()
    {
        // 1. Create manually processed request
        $source = ScoringSource::factory()->create();
        $user = \App\Models\User::factory()->create();

        $scoringRequest = ScoringRequest::factory()->manuallyProcessed()->create([
            'scoring_source_id' => $source->id,
            'manual_processed_by' => $user->id
        ]);

        // 2. Try to send scoring result
        $scoringResultData = [
            'ulid' => $scoringRequest->ulid,
            'score' => 400,
            'findex_journal_id' => 'SHOULD_BE_SKIPPED',
            'processed_at' => now()->toISOString()
        ];

        $response = $this->postJson('/api/scoring/result', $scoringResultData);

        // 3. Should skip and return success
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Talep manuel işlenmiş, skorlama sonucu atlandı'
            ]);

        // 4. No scoring result should be created
        $this->assertNull($scoringRequest->fresh()->scoringResult);
    }

    /** @test */
    public function it_can_query_scoring_status()
    {
        // 1. Create scoring request
        $source = ScoringSource::factory()->create();
        $scoringRequest = ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id
        ]);

        // 2. Query status
        $response = $this->getJson("/api/scoring/{$scoringRequest->ulid}/status");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'ulid',
                    'status',
                    'full_name',
                    'requested_amount',
                    'created_at'
                ]
            ]);

        // 3. Add scoring result and query again
        ScoringResultNew::factory()->approved()->create([
            'scoring_request_id' => $scoringRequest->id
        ]);

        $responseWithResult = $this->getJson("/api/scoring/{$scoringRequest->ulid}/status");

        $responseWithResult->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'ulid',
                    'status',
                    'result' => [
                        'score',
                        'is_approved',
                        'approved_amount'
                    ]
                ]
            ]);
    }

    /** @test */
    public function it_handles_non_existent_ulid_for_scoring_result()
    {
        $invalidData = [
            'ulid' => '01HZHJ4X8G5N9Q3R2S7T6V1W8Y', // 26 karakter ULID format
            'score' => 400,
            'findex_journal_id' => 'TEST_JOURNAL',
            'processed_at' => now()->toISOString()
        ];

        $response = $this->postJson('/api/scoring/result', $invalidData);

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Skorlama talebi bulunamadı'
            ]);
    }

    /** @test */
    public function it_sends_webhook_to_source_after_scoring()
    {
        // 1. Setup with real HTTP mock
        Http::fake([
            'https://test-webhook.example.com/*' => Http::response(['received' => true], 200)
        ]);

        $source = ScoringSource::factory()->create([
            'webhook_url' => 'https://test-webhook.example.com/scoring-result'
        ]);

        $scoringRequest = ScoringRequest::factory()->create([
            'scoring_source_id' => $source->id
        ]);

        $scoringResult = ScoringResultNew::factory()->approved()->create([
            'scoring_request_id' => $scoringRequest->id
        ]);

        // State'i ApprovedState'e geçir (normal akışta bu olur)
        // PendingState -> SentToRedisState -> ScoredState -> ApprovedState
        $scoringRequest->status->transitionTo(SentToRedisState::class);
        $scoringRequest->status->transitionTo(ScoredState::class);
        $scoringRequest->status->transitionTo(ApprovedState::class);

        // 2. Webhook job çalıştır
        $webhookJob = new SendResultToSourceJob($scoringRequest->id);
        $webhookJob->handle();

        // 3. HTTP request kontrolü
        Http::assertSent(function ($request) use ($scoringRequest) {
            $payload = $request->data();

            return $request->url() === 'https://test-webhook.example.com/scoring-result' &&
                $payload['ulid'] === $scoringRequest->ulid &&
                $payload['status'] === 'approved' &&
                isset($payload['score']) &&
                isset($payload['approved_amount']);
        });

        // 4. Database güncelleme kontrolü
        $scoringResult->refresh();
        $this->assertNotNull($scoringResult->webhook_sent_at);
    }
}
