<?php

namespace Tests\Feature;

use App\Jobs\sendPaymentToParasut;
use App\Models\Order\Order;
use App\Models\OrderTransaction;
use App\Models\User;
use App\Services\Parasut\Parasut;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;
use Mockery;

class RateLimitIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock Parasut token
        Mockery::mock('alias:' . Parasut::class)
            ->shouldReceive('getToken')
            ->andReturn('test-integration-token');
    }

    public function test_rate_limit_flow_end_to_end()
    {
        // Arrange
        Queue::fake();
        
        $user = User::factory()->create();
        
        // Add parasut account id as meta
        $user->meta()->create([
            'key' => 'parasut_customer_pid',
            'value' => '999888'
        ]);
        
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'order_number' => 'RATE-TEST-001'
        ]);
        
        $orderTransaction = OrderTransaction::factory()->create([
            'order_id' => $order->id,
            'amount' => 5000,
            'due_date' => now()->addDays(10)
        ]);
        
        // First request - rate limit error
        Http::fake([
            'api.parasut.com/*' => Http::sequence()
                ->push([
                    'errors' => [
                        [
                            'title' => 'Too many requests',
                            'detail' => 'Try again in 5 seconds.'
                        ]
                    ]
                ], 429)
                ->push([
                    'data' => [
                        'id' => 'payment-success-123',
                        'type' => 'transactions'
                    ]
                ], 200)
        ]);
        
        // Dispatch job
        sendPaymentToParasut::dispatch($orderTransaction);
        
        // Assert job was dispatched
        Queue::assertPushed(sendPaymentToParasut::class, function ($job) use ($orderTransaction) {
            return $job->orderTransaction->id === $orderTransaction->id;
        });
        
        // Process the job (simulate first attempt)
        Queue::fake(); // Reset queue
        $job = new sendPaymentToParasut($orderTransaction);
        
        try {
            $job->handle();
        } catch (\Exception $e) {
            // Should not throw exception, job should be released
        }
        
        // Assert cache was set
        $this->assertNotNull(Cache::get('parasut_rate_limit_until'));
        
        // Simulate time passing
        Cache::forget('parasut_rate_limit_until');
        
        // Process job again (second attempt should succeed)
        $job->handle();
        
        // Assert payment was saved
        $this->assertTrue(
            $orderTransaction->meta()
                ->where('key', 'parasut_payment_sent')
                ->where('value', 'payment-success-123')
                ->exists()
        );
    }

    public function test_multiple_jobs_respect_rate_limit()
    {
        // Arrange
        Queue::fake();
        
        $user = User::factory()->create();
        
        // Add parasut account id as meta
        $user->meta()->create([
            'key' => 'parasut_customer_pid',
            'value' => '777666'
        ]);
        
        $transactions = [];
        for ($i = 1; $i <= 3; $i++) {
            $order = Order::factory()->create([
                'user_id' => $user->id,
                'order_number' => "MULTI-TEST-00{$i}"
            ]);
            
            $transactions[] = OrderTransaction::factory()->create([
                'order_id' => $order->id,
                'amount' => 1000 * $i,
                'due_date' => now()->addDays($i * 5)
            ]);
        }
        
        // Set rate limit
        Cache::put('parasut_rate_limit_until', now()->addSeconds(10)->timestamp, 60);
        
        // Dispatch all jobs
        foreach ($transactions as $transaction) {
            sendPaymentToParasut::dispatch($transaction);
        }
        
        // Assert all jobs were dispatched
        Queue::assertPushed(sendPaymentToParasut::class, 3);
        
        // Process jobs - all should be released due to rate limit
        Queue::fake(); // Reset
        foreach ($transactions as $transaction) {
            $job = new sendPaymentToParasut($transaction);
            
            // Mock release method to verify it's called
            $releaseCalled = false;
            $job = new class($transaction) extends sendPaymentToParasut {
                public $releaseCalled = false;
                public function release($delay = 0)
                {
                    $this->releaseCalled = true;
                    parent::release($delay);
                }
            };
            
            $job->handle();
            $this->assertTrue($job->releaseCalled);
        }
    }

    public function test_rate_limit_recovery_after_cache_expiry()
    {
        // Arrange
        $user = User::factory()->create();
        
        // Add parasut account id as meta
        $user->meta()->create([
            'key' => 'parasut_customer_pid',
            'value' => '555444'
        ]);
        
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'order_number' => 'RECOVERY-TEST-001'
        ]);
        
        $orderTransaction = OrderTransaction::factory()->create([
            'order_id' => $order->id,
            'amount' => 3500
        ]);
        
        // Set rate limit that will expire
        Cache::put('parasut_rate_limit_until', now()->addSeconds(2)->timestamp, 2);
        
        // Mock successful response
        Http::fake([
            'api.parasut.com/*' => Http::response([
                'data' => [
                    'id' => 'payment-after-limit',
                    'type' => 'transactions'
                ]
            ], 200)
        ]);
        
        // Wait for cache to expire
        sleep(3);
        
        // Process job
        $job = new sendPaymentToParasut($orderTransaction);
        $job->handle();
        
        // Assert payment was processed successfully
        $this->assertTrue(
            $orderTransaction->meta()
                ->where('key', 'parasut_payment_sent')
                ->exists()
        );
    }
    
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}