<?php

namespace Tests;

use Illuminate\Contracts\Console\Kernel;

trait CreatesApplication
{
    /**
     * Creates the application.
     *
     * @return \Illuminate\Foundation\Application
     */
    public function createApplication()
    {
        // Testing environment'ı force et - ÖNCE!
        putenv('APP_ENV=testing');
        $_ENV['APP_ENV'] = 'testing';
        $_SERVER['APP_ENV'] = 'testing';
        
        // DB ayarlarını da force et
        putenv('DB_CONNECTION=sqlite');
        $_ENV['DB_CONNECTION'] = 'sqlite';
        $_SERVER['DB_CONNECTION'] = 'sqlite';
        
        putenv('DB_DATABASE=:memory:');
        $_ENV['DB_DATABASE'] = ':memory:';
        $_SERVER['DB_DATABASE'] = ':memory:';
        
        $app = require __DIR__.'/../bootstrap/app.php';
        
        // .env.testing dosyasını bootstrap'tan ÖNCE yükle
        if (file_exists($app->basePath('.env.testing'))) {
            $dotenv = \Dotenv\Dotenv::createMutable($app->basePath(), '.env.testing');
            $dotenv->load();
            $dotenv->required(['APP_ENV', 'DB_CONNECTION', 'DB_DATABASE']);
        }

        $app->make(Kernel::class)->bootstrap();
        
        // Config'leri refresh et
        $app->make('config')->set('database.default', 'sqlite');
        $app->make('config')->set('database.connections.sqlite.database', ':memory:');
        
        // Environment'ın doğru yüklendiğini kontrol et
        if ($app->environment() !== 'testing') {
            throw new \RuntimeException(
                'Testing environment could not be loaded. Current env: ' . $app->environment()
            );
        }

        return $app;
    }
}
