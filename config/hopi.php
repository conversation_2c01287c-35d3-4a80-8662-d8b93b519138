<?php

return [
    'ep' => storage_path('app/wsdl/hopi.wsdl'),
    'username' => env('hopi_user', 'kiralabunu_pos'),
    'password' => env('hopi_pass', 'h0p1kira'),
];

//if (env('HOPI_STAGE') === 'production') {
//    return [
//        'ep' => storage_path('app/wsdl/hopi.wsdl'),
//        'username' => env('hopi_user', 'kiralabunu_pos'),
//        'password' => env('hopi_pass', 'h0p1kira'),
//    ];
//} else {
//    return [
//        'ep' => storage_path('app/wsdl/hopi-stage.wsdl'),
//        'username' => env('hopi_user', 'kiralamini_pos'),
//        'password' => env('hopi_pass', '123456'),
//    ];
//}
