<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'digitalocean' => [
        'token' => env('DIGITALOCEAN_TOKEN'),
        'cdn_id' => env('DIGITALOCEAN_CDN_ID'),
        'token_extended' => env('DIGITALOCEAN_TOKEN_EXTENDED'),
    ],

    'skorlabunu' => [
        'use_local_scoring' => env('SKORLABUNU_USE_LOCAL_SCORING', false),
        'api_url' => env('SKORLABUNU_API_URL', 'https://skorlabunu.com/api/upload'),
        'username' => env('SKORLABUNU_USERNAME', 'skorlabunu'),
        'password' => env('SKORLABUNU_PASSWORD', 'kiralabunu2025'),
    ],

];
