<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Scoring System Configuration
    |--------------------------------------------------------------------------
    |
    | Bu konfigürasyon dosyası skorlama sistemi için otomatik onay/red,
    | çarpan hesaplamaları ve moderasyon kurallarını içerir.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Otomatik Onay/Red için Aktif Source ID'ler
    |--------------------------------------------------------------------------
    |
    | Otomatik onay/red işlemlerinin çalışacağı source ID'lerin listesi.
    | '*' değeri tüm source'lar için aktif demektir.
    | Not: Skorlama limit hesaplaması her zaman yapılır, bu config sadece
    | otomatik onay/red süreçlerini kontrol eder.
    |
    */
    'enabled_sources' => env('SCORING_ENABLED_SOURCES', '*'), // * demek tüm source'lar için aktif demektir. 

    /*
    |--------------------------------------------------------------------------
    | Skor Eşik Değerleri
    |--------------------------------------------------------------------------
    |
    | Otomatik onay/red için kullanılan skor eşik değerleri
    |
    */
    'score_thresholds' => [
        'rejection' => env('SCORING_REJECTION_THRESHOLD', 275),  // Bu değerin altı doğrudan red
        'standard' => env('SCORING_STANDARD_THRESHOLD', 375),    // Bu değerin altı çarpan 1
    ],

    /*
    |--------------------------------------------------------------------------
    | Çarpan Değerleri
    |--------------------------------------------------------------------------
    |
    | Partner ve kiralama süresi bazlı çarpan değerleri
    |
    */
    'multipliers' => [
        'default' => env('SCORING_DEFAULT_MULTIPLIER', 1),

        // Kiralama sürelerine göre default çarpanlar
        'rental_periods' => [
            3 => env('SCORING_MULTIPLIER_3_MONTHS', 1),
            6 => env('SCORING_MULTIPLIER_6_MONTHS', 1),
            12 => env('SCORING_MULTIPLIER_12_MONTHS', 1),
            18 => env('SCORING_MULTIPLIER_18_MONTHS', 1),
            24 => env('SCORING_MULTIPLIER_24_MONTHS', 1),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Moderasyon Ayarları
    |--------------------------------------------------------------------------
    |
    | Moderasyon gerektiren durumlar ve bildirim ayarları
    |
    */
    'moderation' => [
        // Bu tutarın üzerindeki mal bedelleri için moderasyon gerekir
        'required_above_amount' => env('SCORING_MODERATION_AMOUNT', 500000),

        // Moderasyon bildirimi gönderilecek email adresleri
        'notification_emails' => explode(',', env('SCORING_MODERATION_EMAILS', '<EMAIL>')),

        // Moderasyon email gönderimi aktif mi?
        'send_notifications' => env('SCORING_MODERATION_NOTIFICATIONS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Hesaplama Parametreleri
    |--------------------------------------------------------------------------
    |
    | Limit hesaplama için kullanılan parametreler
    |
    */
    'calculation' => [
        // Maksimum kira bedeli hesaplaması için kullanılan ay sayısı
        'base_months' => env('SCORING_BASE_MONTHS', 18),
    ],

    /*
    |--------------------------------------------------------------------------
    | Özellik Kontrolü
    |--------------------------------------------------------------------------
    |
    | Sistemin aktif olup olmadığını kontrol eden ana switch
    |
    */
    'enabled' => env('SCORING_AUTO_EVALUATION_ENABLED', true),
];
