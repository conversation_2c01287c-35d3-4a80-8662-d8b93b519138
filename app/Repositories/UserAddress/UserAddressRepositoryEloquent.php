<?php

namespace App\Repositories\UserAddress;

use App\Models\City;
use App\Models\User;
use App\Models\UserAddress;
use App\Repositories\BaseRepository;
use App\Repositories\User\UserRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class UserAddressRepositoryEloquent extends BaseRepository implements UserAddressRepository
{
    protected User|null $user = null;

    /**
     * @inheritDoc
     */
    public function query(): Builder
    {
        return UserAddress::query();
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        //
    }

    /**
     * Set the user for the repository.
     *
     * @param User|int $user
     * @return self
     */
    public function setUser($user)
    {
        if (!$user) {
            return $this;
        }

        $this->user = !$user instanceof User ? app(UserRepository::class)->find($user) : $user;

        return $this->pushFilter(function ($query) {
            return $query->where('user_id', $this->user->getKey());
        });
    }

    /**
     * @inheritDoc
     */
    public function create(array $attributes)
    {
        if (!isset($attributes['user_id']) && $this->user) {
            $attributes['user_id'] = $this->user->getKey();
        }

        // User addresses select by dropdowns, so we need to convert the IDs to the actual values.
        $attributes['city'] = City::where('city_id', $attributes['city_id'])->first()->city;
        $attributes['county'] = City::where('county_id', $attributes['county_id'])->first()->county;
        $attributes['country'] = 'Türkiye';
        $attributes['is_default'] = true;


        return parent::create($attributes);
    }

    public function revokeDefaults()
    {
        if (!$this->user) {
            return;
        }

        DB::table('user_addresses')->where('user_id', $this->user->getKey())->update(['is_default' => 0]);
    }
}
