<?php

namespace App\Repositories\Cart;

use App\Contracts\CartItem;
use App\Models\Cart\Cart;
use App\Models\User;
use Observer\Repository\Contracts\Repository;

interface CartRepository extends Repository
{
    /**
     * Get the cart by the given user id.
     *
     * @return Cart
     */
    public function byUser(User|int $user): Cart;

    /**
     * Get by current user.
     *
     * @return Cart|null
     */
    public function byCurrentUser(): Cart|null;

    /**
     * Update by given user.
     *
     * @param array $attributes
     * @param User|null $user
     *
     * @return Cart
     */
    public function updateByUser(array $attributes, User|null $user = null): Cart;

    /**
     * Update by current user.
     *
     * @param array $attributes
     * @return Cart
     */
    public function updateByCurrentUser(array $attributes): Cart;

    /**
     * Add a product to cart.
     *
     * @param CartItem $item
     *
     * @return Cart
     */
    public function addProduct(CartItem $item, int $month, bool $isInsuranceRequested): Cart;

    /**
     * Remove a product to cart.
     *
     * @param CartItem $item
     *
     * @return Cart
     */
    public function removeProduct(CartItem $item, int $month): Cart;

    /**
     * Clear cart.
     *
     * @return Cart
     */
    public function clear(): Cart;

    public function updateCartItem(\App\Models\Cart\CartItem $item, int $month, bool $isInsuranceRequested): Cart;
}
