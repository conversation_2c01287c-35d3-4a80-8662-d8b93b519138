<?php

namespace App\Repositories\Cart;

use App\Actions\Cart\ClearCart;
use App\Actions\Cart\GuestAddProductToCart;
use App\Actions\Cart\RemoveProductToCart;
use App\Actions\Cart\UpdateUserCart;
use App\Contracts\CartItem;
use App\Models\Cart\Cart;
use App\Models\Guest;
use App\Models\User;
use App\Repositories\BaseRepository;
use Illuminate\Database\Eloquent\Builder;

class GuestCartRepositoryEloquent extends BaseRepository implements GuestCartRepository
{
    /**
     * @inheritDoc
     */
    public function query(): Builder
    {
        return Cart::query();
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        //
    }

    /**
     * Get the cart by the given user id.
     *
     * @return Cart
     */
    public function byUser(Guest|int $user): Cart
    {
        /** @var User $user */
        $user = is_int($user) ? Guest::findOrFail($user) : $user;

        return $user->currentCart();
    }

    /**
     * Get by current user.
     *
     * @return Cart|null
     */
    public function byCurrentUser(): Cart|null
    {
        if ($user = request()->user()) {
            return $this->byUser($user);
        }

        return null;
    }

    /**
     * Update by given user.
     *
     * @param array $attributes
     * @param User|null $user
     *
     * @return Cart
     */
    public function updateByUser(array $attributes, Guest|null $user = null): Cart
    {
        return UpdateUserCart::run($user, $attributes);
    }

    /**
     * Update by current user.
     *
     * @param array $attributes
     * @return Cart
     */
    public function updateByCurrentUser(array $attributes): Cart
    {
        return $this->updateByUser($attributes, auth()->user());
    }

    /**
     * Add a product to cart.
     *
     * @param CartItem $item
     *
     * @return Cart
     */
    public function addProduct(CartItem $item, int $month, bool $isInsuranceRequested): Cart
    {
        return GuestAddProductToCart::run(request()->user(), $item, $month, $isInsuranceRequested);
    }


    /**
     * Remove a product to cart.
     *
     * @param CartItem $item
     *
     * @return Cart
     */
    public function removeProduct(CartItem $item, int $month): Cart
    {
        return RemoveProductToCart::run(request()->user(), $item, $month);
    }


    /**
     * Clear cart.
     *
     * @return Cart
     */
    public function clear(): Cart
    {
        return ClearCart::run(auth()->user());
    }

    public function updateCartItem(\App\Models\Cart\CartItem $item, int $month, bool $isInsuranceRequested): Cart
    {
        $d = $item->update([
            'month' => $month,
            'is_insurance_requested' => $isInsuranceRequested,
        ]);
        return $this->byCurrentUser();
    }
}
