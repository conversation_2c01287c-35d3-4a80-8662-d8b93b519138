<?php

namespace App\Repositories\Order;

use App\Models\Cart\Cart;
use App\Models\Cart\CartItem;
use App\Models\Lunar\SubscriptionMonths;
use App\Models\Order\Order;
use App\Models\OrderTransaction;
use App\Models\User;
use App\PaymentPlan;
use App\Repositories\BaseRepository;
use App\Repositories\User\UserRepository;
use App\Support\Kiralabunu;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use RuntimeException;

/**
 * @mixin \App\Models\Order\Order
 */
class OrderRepositoryEloquent extends BaseRepository implements OrderRepository
{
    protected User|null $user = null;

    /**
     * @inheritDoc
     */
    public function query(): Builder
    {
        return Order::query();
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        //
    }

    /**
     * Build order by payment provider and cart.
     *
     * @param Cart $cart
     *
     * @return Order
     */
    public function build(Cart $cart, array $attributes): Order
    {
        if (!($shippingAddress = $cart->user->address)) {
            throw new RuntimeException('User has no shipping address');
        }
        /** @var Kiralabunu $kiralabunu */
        $kiralabunu = resolve(Kiralabunu::class);

        Log::info('OrderRepositoryEloquent::build()', [
            'user' => auth()->user(),
            'cart_user' => $cart->user,
            'cart->user_id' => $cart->user_id,
            'cart->user->id' => $cart->user->id,
        ]);

        // TODO: $attributes değerleri doldurulacak ve provider dan da gelebilir.
        //      Bunların testleride ayrıca yazılacak.
        $attributes = array_merge($attributes, [
            'user_agent' => $kiralabunu->getUserAgent(),
            'platform' => $kiralabunu->getPlatform(),
            'ip_address' => $kiralabunu->getUserIpAddress(),
        ]);

        $order = parent::create(array_merge([
            'user_id' => $cart->user->id,
            'coupon_id' => $cart->coupon_id,
            'warehouse_id' => null,

            'status' => 'App\States\Order\OrderReceived',
            'order_number' => env('APP_SHORT') . Str::of(Str::random(10))->upper(),

            'billing_address' => null, // @TODO: Fatura adresi attribute dan gelebilir veya bu bölüm kaldırılabilir. Konuşulup nasıl olacağı belirlenmeli.
            'shipping_address' => [
                'flat_no' => $shippingAddress->flat_no,
                'door_bell' => $shippingAddress->door_bell,
                'type' => $shippingAddress->type,
                'address' => $shippingAddress->address,
                'description' => $shippingAddress->description,
                'latitude' => $shippingAddress->latitude,
                'longitude' => $shippingAddress->longitude,
                'postalCode' => $shippingAddress->postal_code,
                'building' => $shippingAddress->building,
                'floor' => $shippingAddress->floor,
                'firstName' => $cart->user->first_name,
                'lastName' => $cart->user->last_name,
                'phoneNumber' => $cart->user->phone,
            ],

            'sub_total' => $cart->getSubTotalAttribute(),
            'tax_amount' => $cart->getTaxAmountAttribute(),
            'delivery_fee' => $cart->getDeliveryFeeAttribute(),
            'discount_amount' => $cart->getDiscountAmountAttribute(),
            'total' => $cart->getTotalAttribute() - $cart->getInsuranceTotalAttribute(),
        ], $attributes));

        $this->setItems($order, $cart->items);

        $this->createPaymentPlan($cart, $order, $attributes, $cart->getInsuranceTotalAttribute());

        return $order;
    }

    public function setItems(Order $order, Collection $items): void
    {
        $order->items()->createMany($items->map(function (CartItem $item) {

            return [
                'product_id' => $item->product->getKey(),
                'product_type' => $item->product->getMorphClass(),
                'quantity' => $item->quantity,
                'price' => $item->product->getPrice($item->month),
                'sub_total' => $item->getSubTotalAttribute(),
                'tax_included' => $item->product->hasTaxIncluded(),
                'tax_rate' => $item->product->getTaxRate(),
                'tax_amount' => $item->getTaxAmountAttribute(),
                'discount_type' => $item->product->getDiscountType(),
                'discount_value' => $item->product->getDiscountValue(),
                'discount_amount' => $item->getDiscountAmountAttribute(),
                'total' => $item->getTotalAttribute(),
                'has_insurance' => $item->is_insurance_requested,
                'insurance_price' => $item->getInsurancePrice(),
                'plan' => $item->month, //SubscriptionMonths::getSelectedMonth($item->month),
            ];
        }));
    }

    /**
     * Get the orders by the given user id.
     *
     * @return Order[]|null
     */
    public function byUser(User|int $user)
    {
        /** @var User $user */
        $user = is_int($user) ? User::findOrFail($user) : $user;

        return $user->orders();
    }

    /**
     * Set the user for the repository.
     *
     * @param User|int $user
     * @return self
     */
    public function setUser($user)
    {
        if (!$user) {
            return $this;
        }

        $this->user = !$user instanceof User ? app(UserRepository::class)->find($user) : $user;

        return $this->pushFilter(function ($query) {
            return $query->where('user_id', $this->user->getKey());
        });
    }

    private function createPaymentPlan($cart, $order, $attributes, $insurancePrice = 0)
    {
        //$lastUsedCC = $this->order->orderTransactions()->where('payment_status_id', 1)->get()->last()->card_  id; // Son kullanılan kredi kartı
        $pp = new PaymentPlan($order->created_at, $attributes['cc']->id ?? 1, $order->id);
        $pp->add(0, 1);

        // Siparişin ilk oluşması sırasında ödeme planı burada hazırlanıyor ve ödeme planının ilk taksidi tahsil ediliyor
        // Kampanyalar ve kampanyalardan kaynaklanan (Örn 1TL) ödeme planını etkileyisi adımlar buradan düzenlenmelidir
        $tagProducts = []; //\App\Models\Lunar\Tag::find(5)->products()->select(['products.id'])->get()->pluck('id');
        $campaignPrice = 0;
        $order->items->map(function ($item) use ($pp, $tagProducts, &$campaignPrice) {
            //dd($item->product->product_id, $tagProducts->contains($item->product->product_id), $tagProducts->intersect($item->product->product_id)->count());
            // intersect edip sonra count edip 0 dan fazla ise de işlem yapılabilir
            // Tag üzerinden çalışma iptal
            //            if ($tagProducts->contains($item->product->product_id) && $item->planObj->value > 5) {
            //            if ($item->planObj->value > 5) {
            //                $campaignPrice += 1 * $item->quantity;
            //            } else {
            //                $campaignPrice += $item->total;
            //            }

            $pp->add($item->total, $item->planObj->value);
        });

        if ($insurancePrice > 0) {
            $pp->add($insurancePrice, 1);
        }

        if ($campaignPrice > 0)
            $pp->setCampaignPriceForGivenMonths($campaignPrice, 1); // 1TL campaign

        $pp->save();

        // if coupon is used, update first payment plan for the coupon
        if ($cart->coupon_id) {
            $ot = OrderTransaction::where('order_id', $order->id)->first();
            for ($i = 1; $i <= SubscriptionMonths::getSelectedMonth($cart->coupon->effected_months); $i++) {
                // Check if there is not transaction record for the month
                if ($ot) {
                    $ot->amount = $order->total;
                    $ot->save();
                    $ot = $ot->nextTransection();
                }
            }
        }
    }

    public function createOrderFromApiData(User $user, array $orderData, array $itemsData, array $campaignData): Order
    {
        Log::info('Attempting to create order from API data', [
            'user_id' => $user->id,
            'orderData' => $orderData,
            'itemsData' => $itemsData,
            'campaignData' => $campaignData
        ]);

        try {
            /** @var Kiralabunu $kiralabunu */
            $kiralabunu = resolve(Kiralabunu::class);

            // 1. Sipariş verisini hazırla
            $orderAttributes = [
                'user_id' => $user->id,
                'order_number' => $orderData['order_number'],
                'status' => 'App\States\Order\OrderReceived', // Başlangıç durumu
                'user_agent' => $kiralabunu->getUserAgent(),
                'platform' => $kiralabunu->getPlatform(),
                'ip_address' => $kiralabunu->getUserIpAddress(),
            ];

            // 2. Adres bilgilerini hazırla
            $userInfo = $orderData['json_payload_user_info'] ?? [];

            // Adres bilgilerini varsa şekilde format
            if (isset($userInfo['address']) && is_array($userInfo['address'])) {
                $orderAttributes['shipping_address'] = [
                    'flat_no' => $userInfo['address']['flat_no'] ?? null,
                    'door_bell' => $userInfo['address']['door_bell'] ?? null,
                    'type' => $userInfo['address']['type'] ?? null,
                    'address' => $userInfo['address']['address'] ?? null,
                    'description' => $userInfo['address']['description'] ?? null,
                    'latitude' => $userInfo['address']['latitude'] ?? null,
                    'longitude' => $userInfo['address']['longitude'] ?? null,
                    'postalCode' => $userInfo['address']['postal_code'] ?? null,
                    'building' => $userInfo['address']['building'] ?? null,
                    'floor' => $userInfo['address']['floor'] ?? null,
                    'firstName' => $userInfo['first_name'] ?? $user->first_name,
                    'lastName' => $userInfo['last_name'] ?? $user->last_name,
                    'phoneNumber' => $userInfo['phone'] ?? $user->phone,
                ];
            }

            $orderAttributes['billing_address'] = null;

            // 3. İşlem ID ve tahsil edilen tutarı ekle
            if (isset($orderData['iyzico_transaction_id'])) {
                $orderAttributes['iyzico_transaction_id'] = $orderData['iyzico_transaction_id'];
            }

            if (isset($orderData['amount_collected']) && is_numeric($orderData['amount_collected'])) {
                $orderAttributes['paid_amount'] = (float)$orderData['amount_collected'];
            }

            // 4. Kupon bilgisi varsa ekle
            if (isset($campaignData['coupon_id']) && !empty($campaignData['coupon_id'])) {
                $orderAttributes['coupon_id'] = $campaignData['coupon_id'];
            }

            // 5. Sipariş kaydını oluştur
            /** @var Order $order */
            $order = $this->create($orderAttributes);

            // 6. Sipariş kalemlerini hesapla ve ekle
            $subTotal = 0;
            $taxAmount = 0;
            $total = 0;

            foreach ($itemsData as $itemData) {
                // Ürün tipini ve ID'sini al
                $productType = $itemData['product_type'];
                $productId = $itemData['product_id'];

                // Ürünü bul
                $product = app($productType)::find($productId);
                if (!$product) {
                    throw new \InvalidArgumentException("Ürün bulunamadı: ID {$productId}, Tip {$productType}");
                }

                // Sipariş kalemi verilerini hazırla
                $orderItem = [
                    'product_id' => $productId,
                    'product_type' => $productType,
                    'quantity' => (int)$itemData['quantity'],
                    'price' => (float)$itemData['price'], // Birim fiyat
                    'sub_total' => (float)$itemData['sub_total'], // KDV hariç toplam
                    'tax_included' => filter_var($itemData['tax_included'] ?? false, FILTER_VALIDATE_BOOLEAN),
                    'tax_rate' => (float)$itemData['tax_rate'],
                    'tax_amount' => (float)$itemData['tax_amount'],
                    'total' => (float)$itemData['total'], // KDV dahil toplam
                ];

                // Sigorta bilgisi ekle
                if (isset($itemData['has_insurance'])) {
                    $orderItem['has_insurance'] = filter_var($itemData['has_insurance'], FILTER_VALIDATE_BOOLEAN);
                    if ($orderItem['has_insurance'] && isset($itemData['insurance_price'])) {
                        $orderItem['insurance_price'] = (float)$itemData['insurance_price'];
                    }
                }

                // Kiralama planı/ay bilgisi ekle
                if (isset($itemData['plan'])) {
                    $orderItem['plan'] = $itemData['plan'];
                }

                // Sipariş kalemine ait indirim bilgisi
                if (isset($itemData['discount_type'])) {
                    $orderItem['discount_type'] = $itemData['discount_type'];
                }
                if (isset($itemData['discount_value'])) {
                    $orderItem['discount_value'] = (float)$itemData['discount_value'];
                }
                if (isset($itemData['discount_amount'])) {
                    $orderItem['discount_amount'] = (float)$itemData['discount_amount'];
                }

                // Sipariş kalemine ekle
                $order->items()->create($orderItem);

                // Hesaplamaları topla
                $subTotal += $orderItem['sub_total'];
                $taxAmount += $orderItem['tax_amount'];
                $total += $orderItem['total'];
            }

            // 7. Sipariş toplamlarını güncelle
            $order->sub_total = $subTotal;
            $order->tax_amount = $taxAmount;

            // 8. Kampanya/indirim tutarları
            $discountAmount = 0;
            if (isset($campaignData['campaignPrice']) && is_numeric($campaignData['campaignPrice'])) {
                $discountAmount = (float)$campaignData['campaignPrice'];
            }
            $order->discount_amount = $discountAmount;

            // 9. Sigorta tutarlarını ekle
            $insuranceTotal = 0;
            if (isset($campaignData['insurancePrice']) && is_numeric($campaignData['insurancePrice'])) {
                $insuranceTotal = (float)$campaignData['insurancePrice'];
            }

            // 10. Toplam tutarı hesapla
            $order->total = $total - $discountAmount;

            // 11. Değişiklikleri kaydet
            $order->save();

            // 12. Eğer ödeme bilgisi varsa, işlem kaydı oluştur
            if (!empty($order->iyzico_transaction_id) && !empty($order->paid_amount)) {
                try {
                    // OrderTransaction modeli yapınıza göre ayarlayın
                    $order->transactions()->create([
                        'provider_transaction_id' => $order->iyzico_transaction_id,
                        'amount' => $order->paid_amount,
                        'status' => 'completed',
                        'type' => 'payment',
                        'provider' => 'iyzico',
                        'user_id' => $user->id,
                    ]);
                } catch (\Exception $e) {
                    Log::error('OrderTransaction oluşturulurken hata: ' . $e->getMessage(), [
                        'order_id' => $order->id,
                        'exception' => $e
                    ]);
                    // İşlem kaydı oluşturulmazsa siparişi iptal etmeyelim,
                    // sadece hata loglanır, sipariş devam eder
                }
            }

            Log::info('Order successfully created from API data', [
                'order_id' => $order->id,
                'order_number' => $order->order_number
            ]);

            return $order;
        } catch (\Exception $e) {
            Log::error('createOrderFromApiData çalıştırılırken hata oluştu: ' . $e->getMessage(), [
                'exception' => $e,
                'user_id' => $user->id,
                'orderData' => $orderData
            ]);
            throw $e; // Hatayı yukarı fırlat, controller'da yakalanacak
        }
    }
}
