<?php

namespace App\Models;

use App\States\SupportRequest\SupportProductControl;
use App\States\SupportRequest\SupportState;
use App\States\SupportRequestCustomerService\CustomerServiceSupportState;
use HelpScout\Api\ApiClientFactory;
use HelpScout\Api\Conversations\ConversationRequest;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;

class SupportRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_type',
        'product_id',
        'user_id',
        'support_requests_type_id',
        'customer_message',
        'status',
        'cs_sp_status',
        'order_id',
        'helpscout_conversation_id',
        'helpscout_conversation_number',
        'is_product_received',
        'cargo_received_at',
        'receiver_user_id',
        'cargo_return_code',
    ];

    protected $appends = ['support_request_date', 'status_text'];

    protected $casts = [
        'status' => SupportState::class,
        'cs_sp_status' => CustomerServiceSupportState::class,
    ];

    public function product()
    {
        return $this->morphTo();
    }

    public function getProductStockItemAttribute()
    {
        $item = $this->order->orderItems()->where('product_id', $this->product_id)->first();
        if ($item) {
            return $item->itemStock;
        }

        // check redis cache for email notification send previous 24 hours
        $cacheKey = 'email_notification_send_previous_24_hours_product_id_' . $this->product_id . '_support_request_id_' . $this->id;
        $cacheValue = Cache::get($cacheKey);
        if (! $cacheValue) {
            Cache::put($cacheKey, true, 60 * 60 * 24);
            Mail::raw('Ürün bulunamadı: ' . $this->product_id, function ($message) {
                $message->to('<EMAIL>')->subject('Ürün bulunamadı Product#' . $this->product_id . ' SupportRequest#' . $this->id);
            });
        }

        return null;
    }

    public function supportRequestType()
    {
        return $this->belongsTo(SupportRequestType::class, 'support_requests_type_id');
    }

    public function supportRequestDate(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->created_at->format('d.m.Y H:i:s'),
        );
    }

    public function statusText(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->status->getLabel(),
        );
    }

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }

    public function supportProduct(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attribute) => $this->product?->product?->name,
        );
    }

    public function inventoryId(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->order->orderItems()->where('product_id', $this->product_id)->first()?->itemStock?->inventory->id,
        );
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function getHelpScoutThread(): Attribute
    {

        if ($this->helpscout_conversation_id == null) {
            return Attribute::make(
                get: fn($value, $attribute) => 'Destek kaydı HelpScout\'a aktarılmamıştır.',
            );
        }

        $client = ApiClientFactory::createClient();

        // Set Client credentials if using that grant type.  Using this approach will lazily fetch an access token on-demand
        // when needed.
        $client->useClientCredentials('f2XFwEbbXZoG5KW6f3F5VwNCKMBuz1u0', 'T5mTqLn0LfTmQFBP3eSjXcL9xkmnTan8');

        $request = (new ConversationRequest)
            ->withThreads();
        $threads = $client->conversations()->get($this->helpscout_conversation_id, $request)->getThreads();
        $res = collect();
        foreach ($threads as $thread) {
            $text = Str::of('');

            if ($thread->getType() == 'message') {
                $text = $text->append('Cevaplayan: ' . $thread->getCreatedByUser()->getFirstName() . ' ' . $thread->getCreatedByUser()->getLastName())
                    ->append('<br>')
                    ->append('Mesaj: ' . $thread->getCreatedAt()->format('d.m.Y H:i:s'))
                    ->append('<br>')
                    ->append($thread->getText());
            }

            if ($thread->getType() == 'customer') {
                $text = $text->append('Cevaplayan: ' . $thread->getCreatedByCustomer()->getFirstName() . ' ' . $thread->getCreatedByCustomer()->getLastName())
                    ->append('<br>')
                    ->append('Mesaj: ' . $thread->getCreatedAt()->format('d.m.Y H:i:s'))
                    ->append('<br>')
                    ->append($thread->getText());
            }

            $res->push($text);
        }

        return Attribute::make(
            get: fn($value, $attribute) => $res->implode('<hr class="py-3">'),
        );
    }

    public function getHelpScoutLink(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attribute) => 'https://secure.helpscout.net/conversation/' . $this->helpscout_conversation_id . '/' . $this->helpscout_conversation_number . '?folderId=4270060',
        );
    }

    public function saveReturnedProductInfos($delivered_at): void
    {
        $this->update([
            'is_product_received' => true,
            'cargo_received_at' => $delivered_at,
            'receiver_user_id' => auth()->id(),
            'status' => SupportProductControl::class,
        ]);
    }
}
