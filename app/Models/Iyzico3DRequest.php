<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Iyzico3DRequest extends Model
{
    use HasFactory;

    protected $table = 'iyzico3d_requests';

    protected $fillable = [
        'user_id',
        'token',
        'conversation_id',
        'payment_id',
        'status',
        'md_status',
        'conversation_data',
        'holder',
        'bin_number',
        'number',
        'month',
        'year',
        'card_type',
        'card_association',
        'card_family',
        'bank',
        'attribute_data',
    ];
}
