<?php

namespace App\Models;

use App\Exceptions\Transaction\TransactionVoidException;
use App\Facades\IyzipayFacade;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Transaction extends BaseModel
{
    protected $fillable = [
        'amount',
        'products',
        'refunds',
        'iyzipay_key',
        'voided_at',
        'order_id',
        'currency',
        'order_transaction_id',
    ];

    protected $casts = [
        'products' => 'array',
        'refunds' => 'array'
    ];

    protected $dates = [
        'voided_at'
    ];

    protected $appends = [
        'refunded_amount'
    ];

    public function billable(): BelongsTo
    {
        return $this->belongsTo(User::class, 'billable_id');
    }

    public function creditCard(): BelongsTo
    {
        return $this->belongsTo(CreditCard::class);
    }

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function void(): Transaction
    {
        if ($this->created_at < Carbon::today()->startOfDay()) {
            throw new TransactionVoidException('This transaction cannot be voided.');
        }

        return IyzipayFacade::void($this);
    }

    public function refund(): Transaction
    {
        return IyzipayFacade::void($this);
    }

    public function getRefundedAmountAttribute()
    {
        if ($this->refunds)
            return array_sum(array_column($this->refunds, 'amount'));

        return 0;
    }

    public function orderNo(): Attribute
    {
        return Attribute::make(
            get: fn() => Str::limit(
                Str::of(
                    Str::of(
                        json_decode($this->attributes['products'] ?? [])[0]?->product?->name
                    )
                        ->explode('/')
                        ->last()
                )
                    ->explode(' ')
                    ->first(),
                12,
                ''
            ) ?? '',
        );
    }
}
