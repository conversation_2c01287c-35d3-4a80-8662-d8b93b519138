<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Spatie\Sitemap\Contracts\Sitemapable;
use Spatie\Sitemap\Tags\Url;

class Brand extends Model implements Sitemapable
{
    use HasFactory;

    protected $connection = 'mysql-lunar';
    protected $fillable = ['id', 'name', 'slug', 'title', 'meta_description', 'meta_keywords', 'sub_category_note'];

    public function toSitemapTag(): Url|string|array
    {
        return Url::create(env('FRONTEND_URL') . '/marka/' . Str::of($this->name)->slug())
            ->setChangeFrequency('')
            ->setPriority(false);
    }
}
