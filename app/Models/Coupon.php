<?php

namespace App\Models;

use App\Enums\DiscountType;
use App\Mail\BulkCouponCreated;
use App\Models\Lunar\Collection;
use App\Models\Lunar\Product;
use App\Models\Order\Order;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class Coupon extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'code',
        'type',
        'description',
        'value',
        'limit',
        'min_cart_amount',
        'max_cart_amount',
        'published',
        'start_date',
        'due_date',
        'effected_months',
        'is_subscription_months_rules_enabled',
        'rule_operator',
        'subscription_months_id',
        'customer_usage_limit',
        'is_category_coupon',
        'is_new_user_coupon',
        'is_product_coupon',
        'is_disabled_on_discounted_products',
    ];

    protected $casts = [
        'published' => 'boolean',
        'type' => DiscountType::class . ':nullable',
        'start_date' => 'datetime',
        'due_date' => 'datetime',
        'used_total' => 'integer',
        'user_total' => 'integer',
        'is_disabled_on_discounted_products' => 'boolean',
    ];

    public static function createMassCoupon(string $coupon_code, int $quantity, string $description)
    {
        // check if coupon code exists
        $coupon = self::where('code', $coupon_code)->first();
        // if not exists, return error
        if (!$coupon) {
            return false;
        }

        // create bulk coupons
        for ($i = 0; $i < $quantity; $i++) {
            self::create([
                'code' => Str::random(8),
                'type' => $coupon->type,
                'description' => $description,
                'value' => $coupon->value,
                'limit' => $coupon->limit,
                'min_cart_amount' => $coupon->min_cart_amount,
                'max_cart_amount' => $coupon->max_cart_amount,
                'published' => $coupon->published,
                'start_date' => $coupon->start_date,
                'due_date' => $coupon->due_date,
                'effected_months' => $coupon->effected_months,
                'is_subscription_months_rules_enabled' => $coupon->is_subscription_months_rules_enabled,
                'rule_operator' => $coupon->rule_operator,
                'subscription_months_id' => $coupon->subscription_months_id,
                'customer_usage_limit' => $coupon->customer_usage_limit,
                'is_category_coupon' => $coupon->is_category_coupon,
                'is_new_user_coupon' => $coupon->is_new_user_coupon,
                'is_product_coupon' => $coupon->is_product_coupon,
                'is_disabled_on_discounted_products' => $coupon->is_disabled_on_discounted_products,
            ]);
        }

        // prepare coupon code csv
        $coupon_codes = self::where('description', $description)->get()->pluck('code')->toArray();
        $csv = implode(';', $coupon_codes);

        // dispatch email with coupon codes to use auth user email
        Mail::queue(new \App\Mail\BulkCouponCreatedNotification($csv, auth()->user()));
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function getDiscountFrom($price, $calculateDiscount = 0)
    {
        if ($calculateDiscount < 0) {
            return $calculateDiscount = 0;
        }

        return round(floatval($this->type->isRate() ? $price * $this->value / 100 : $this->value), 2);
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Collection::class, 'coupon_category', 'coupon_id', 'category_id');
    }

    public function products(): MorphToMany
    {
        return $this->morphedByMany(Product::class, 'couponable', 'coupon_restrictors', 'coupon_id', 'couponable_id', 'id', 'id');
    }

}
