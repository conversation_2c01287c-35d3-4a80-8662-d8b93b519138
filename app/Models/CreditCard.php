<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class CreditCard extends BaseModel implements Auditable
{
    use HasFactory;
    use SoftDeletes;
    use \OwenIt\Auditing\Auditable;

    protected $fillable = [
        'alias',
        'token',
        'iyzipay_key',
        'bank',
        'bin_number',
        'holder',
        'number',
        'month',
        'year',
        'card_type',
        'card_association',
        'card_family',
        'service_name',
        'service_env'
    ];

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'billable_id');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    public function getNextCard()
    {
        return $this->owner->creditCards()->where('id', '>', $this->id)->first();
        // return $this->owner->creditCards()->where('id', '<', $this->id)->orderBy('id', 'desc')->first();
    }
}
