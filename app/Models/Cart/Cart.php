<?php

namespace App\Models\Cart;

use App\Enums\DiscountType;
use App\Models\BaseModel;
use App\Models\Coupon;
use App\Models\Currency;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * @property EloquentCollection $items
 * @property User $user
 * @property Coupon $coupon
 */
class Cart extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'coupon_id',
    ];

    protected $appends = [
        'sub_total',
        'tax_amount',
        'discount_amount',
        'total',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function coupon()
    {
        return $this->belongsTo(Coupon::class)->withDefault([
            'type' => DiscountType::FIXED(),
            'value' => 0,
        ]);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function currency()
    {
        return $this->belongsTo(Currency::class)->withDefault([
            'code' => 'HUF',
        ]);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function items()
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * @return bool
     */
    public function isEmpty(): bool
    {
        return $this->items->isEmpty();
    }

    /**
     * Get the total discount amount.
     *
     * @return float
     */
    public function getDiscountAmountAttribute()
    {
        $itemsDiscount = 0; // round($this->items->sum('discount_amount'), 2); // Bu sistemde ürünlere indirim yok o yüzden bu satır 0 ile başlayacak
        $calculateDiscount = 0;

        // if is_subscription_months_rules_enabled is true, then we need to check the cart items months

//        if ($this->coupon->is_subscription_months_rules_enabled) {
//            $itemsDiscount = $this->items->filter(function ($item) {
//                return match ($this->coupon->rule_operator) {
//                    '=' => $item->month == $this->coupon->subscription_months_id,
//                    '<' => $item->month < $this->coupon->subscription_months_id,
//                    '>' => $item->month > $this->coupon->subscription_months_id,
//                    '>=' => $item->month >= $this->coupon->subscription_months_id,
//                    '<=' => $item->month <= $this->coupon->subscription_months_id,
//                };
//            })->sum('total');
//
//        } else {
//
//
//            // Şimdilik ek aynı anda geçerli olmayacak şekilde
//            if ($this->coupon->is_category_coupon == 1) {
//                $calculateDiscount = -1;
//                // Kuponun kategorileri
//                $couponCategories = $this->coupon->categories()->pluck('category_id');
//                // Sepetteki ürünlerin kategorileri
//                $cartCategories = $this->items->map(fn($x) => [$x->id => $x->product->product->collections->pluck('id')]);
//
//                // Kupon kategorileri ile sepet kategorileri kesişiyorsa
//                $suitables = $cartCategories->map(fn($x) => [key($x) => array_intersect(head($x)->toArray(), $couponCategories->toArray())])
//                    ->filter(fn($x) => !empty(head($x)));
//
//                if ($suitables->count() > 0) {
//                    $calculateDiscount = $this->items()->whereIn('id', array_values($suitables->map(fn($x) => key($x))->toArray()))->get()->sum('total');
//                }
//
//            } else {
//                $itemsDiscount = $this->items->sum('total');
//            }
//        }
//
//
//        $totalDiscount = $this->coupon->getDiscountFrom($itemsDiscount, $calculateDiscount);
        $ccd = new \App\Rules\CalculateCouponDiscount($this, $this->coupon);
        $ccd->calculateDiscount();
        $totalDiscount = $ccd->getDiscount();
        //$totalDiscount = $this->coupon->getDiscountFrom($this->getSubTotalAttribute() + $this->getTaxAmountAttribute());

        return round($totalDiscount > 0 ? -1 * $totalDiscount : 0, 2);
    }

    /**
     * Total sub total of all the items in the cart.
     *
     * @return float
     */
    public function getSubTotalAttribute()
    {
        return round($this->items->sum('sub_total'), 2);
    }

    /**
     * Total tax amount of the cart.
     *
     * @return float
     */
    public function getTaxAmountAttribute()
    {
        return round($this->items->sum('tax_amount'), 2);
    }

    public function getInsuranceTotalAttribute()
    {
        return round($this->items->map(fn($item) => $item->getInsurancePrice())->sum(), 2);
    }

    /**
     * Total amount of the cart.
     *
     * @return float
     */
    public function getTotalAttribute()
    {
        return round(
            $this->getSubTotalAttribute() +
            $this->getTaxAmountAttribute() +
            $this->getDiscountAmountAttribute() +
            $this->getInsuranceTotalAttribute(),
            2
        );
    }

    /**
     * Kullanıcının seçtiği teslimat adresine göre teslimat ücreti hesaplanacak
     * Şu an için böyle bir hizmet yok!
     *
     * @return float
     */
    public function getDeliveryFeeAttribute()
    {
        return 0;
    }

    /**
     * Get the payment amount.
     *
     * @return float
     */
    public function getAmount(): float
    {
        return $this->getTotalAttribute();
    }

    /**
     * Get the items to be paid.
     *
     * @return Collection<CartItem>
     */
    public function getItems(): Collection
    {
        return $this->items;
    }

    /**
     * Get the user who is paying.
     *
     * @return User
     */
    public function getUser(): User
    {
        return $this->user;
    }
}
