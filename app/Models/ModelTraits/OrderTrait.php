<?php

namespace App\Models\ModelTraits;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Carbon\Carbon;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Spatie\ModelStates\Exceptions\TransitionNotFound;
use App\States\Order\OrderEvaluation;
use App\States\Order\OrderUserCantBeReached;
use Illuminate\Support\Facades\Mail;
use App\States\Order\OrderDenied;
use App\States\Order\OrderApproved;
use App\Jobs\klavioRejectedOrder;
use App\Jobs\klavioApprovedOrder;
use Exception;
use App\Models\Transaction;
use App\Models\Meta;
use App\Facades\IyzipayFacade;
use App\Exceptions\Transaction\TransactionVoidException;
use Illuminate\Support\Facades\DB;
use App\PaymentPlan;
use App\Models\Lunar\SubscriptionMonths;
use Spatie\ModelStates\Exceptions\TransitionNotAllowed;
use App\States\Order\Investigation;
use App\Jobs\HopiNotifyCheckoutRequest;
use App\Models\Cart\Cart;
use App\Models\Cart\CartItem;
use App\Models\Lunar\ProductVariant;
use App\Models\Coupon;
use Illuminate\Support\Str;
use App\States\Order\OrderCancelled;
use App\Models\UserAddress;

trait OrderTrait
{

    public function shippingAddress()
    {
        return $this->hasOne(UserAddress::class, 'id', 'shipping_address_id');
    }

    public function billingAddress()
    {
        return $this->hasOne(UserAddress::class, 'id', 'billing_address_id');
    }

    public function statusText(): Attribute
    {
        // get order status by macthing state
        return Attribute::make(
            get: fn($value) => is_string($this->status) ? $this->status : $this->status->getLabel(),
        );
    }

    public function orderProductList(): Attribute
    {
        // get order status by macthing state
        return Attribute::make(
            get: fn($value, $attribute) => $this->orderItems->map(fn($item) => Str::limit($item->product?->product?->name, 50))->implode(', <br> '),
        );
    }

    public function orderProductListCommaSeperated(): Attribute
    {
        // get order status by macthing state
        return Attribute::make(
            get: fn($value, $attribute) => $this->orderItems->map(fn($item) => $item->product?->product?->name)->implode(', '),
        );
    }

    public function calculateNextDueDate(Carbon $currentDueDate): Carbon
    {
        $originalDay = $this->created_at->day;

        // Ocak ayı ve 29+ gün kontrolü
        if ($currentDueDate->month == 1 && $originalDay >= 29) {
            return $currentDueDate->copy()->setDay(28)->setMonth(2);
        }

        // Ay sonlarını kontrol et
        if ($originalDay >= 29) {

            // 30 günlük aylar kontrolü
            if ($originalDay == 31 && in_array($currentDueDate->month, [1, 3, 5, 7, 8, 10, 12])) {
                return  $currentDueDate->copy()->addDays(1)->endOfMonth();
            }

            // Diğer durumlar için orijinal günü kullan
            return $currentDueDate->copy()->addMonth()->setDay($originalDay);
        }

        return $currentDueDate->copy()->addMonth()->setDay($originalDay);
    }

    /**
     * Update order status to evaluation
     *
     * @param Model $record
     * @return Model
     */
    public function updateStatusToEvaluation(Model $record): Model
    {
        $record = $this->updateOrderStatus(['status' => OrderEvaluation::class], $record);

        Mail::queue(new \App\Mail\OnEvaluation($record->user, $record));

        return $record;
    }

    /**
     * Update order status to user cant be reached
     *
     * @param Model $record
     * @return Model
     */
    public function updateStatusToUserCantBeReached(Model $record): Model
    {
        $record = $this->updateOrderStatus(['status' => OrderUserCantBeReached::class], $record);

        $this->orderCancelledActions($record);
        $this->hopiReturnTransaction($record);
        Mail::queue(new \App\Mail\OrderStatus\UserCantBeReached($record->user, $record));

        return $record;
    }

    /**
     * Update order status to denied
     *
     * @param Model $record
     * @return Model
     */
    public function updateStatusToDenied(Model $record): Model
    {
        $record = $this->updateOrderStatus(['status' => OrderDenied::class], $record);

        $this->restoreStockAction($record);
        $this->orderCancelledActions($record);
        klavioRejectedOrder::dispatch($record->user, $record);
        $this->hopiReturnTransaction($record);
        Mail::queue(new \App\Mail\SuccessRefund($record->user, $record));

        return $record;
    }

    public function updateStatusToApproved(Model $record): Model
    {
        $record = $this->updateOrderStatus(['status' => OrderApproved::class], $record);

        $this->orderApprovedActions($record);
        $this->restoreNonApprovedStockAction($record);
        Mail::queue(new \App\Mail\SuccessOrder($record->user, $record));
        klavioApprovedOrder::dispatch($record->user, $record);
        // check if order is hopi order then dispatch HopiNotifyCheckoutRequest function
        $this->hopiNotifyCheckoutRequest($record);

        return $record;
    }

    public function updateStatusToInvestigation(Model $record): Model
    {
        $record = $this->updateOrderStatus(['status' => Investigation::class], $record);

        return $record;
    }

    public function updateStatusToCancelled(Model $record): Model
    {
        $record = $this->updateOrderStatus(['status' => OrderCancelled::class], $record);

        $this->restoreStockAction($record);
        $this->orderCancelledActions($record);
        klavioRejectedOrder::dispatch($record->user, $record);
        $this->hopiReturnTransaction($record);
        Mail::queue(new \App\Mail\RentalCancellation($record->user, $record));

        return $record;
    }

    private function updateOrderStatus(array $data, Model $record): Model
    {
        // check rate limit
        if ($record->status != $data['status']) {
            $rateLimit = RateLimiter::attempt('order-status-change:' . $record->id, $perMinute = 1, function () {});

            if (!$rateLimit) {
                Notification::make()
                    ->title('Dakikada bir sipariş durumu değiştirilebilir.')
                    ->danger()
                    ->send();

                return $record->fresh();
            } else {
                $this->transitionChangeCheck($record, $data);
            }
        }

        return $record;
    }

    private function transitionChangeCheck(Model $record, array $data)
    {
        try {
            $record->status->transitionTo($data['status']);
        } catch (TransitionNotFound $e) {
            Notification::make()
                ->title('Sipariş durumu bu şekilde değiştirilemez.')
                ->danger()
                ->send();

            $this->halt();

            return $record->fresh();
        }

        return $data;
    }

    private function hopiReturnTransaction($record)
    {
        $hopiOrder = \App\Models\Order\Order::find($record->id);
        $hopi_notify_checkout = $hopiOrder->meta()
            ->where("key", "hopi_notify_checkout")
            ->first();

        if ($hopi_notify_checkout?->value == 1) {
            \App\Jobs\HopiStartReturnTransactionRequest::dispatchSync(
                \App\Models\Order\Order::find($record->id)
            );

            \App\Jobs\HopiCompleteReturnTransactionRequest::dispatchSync(
                \App\Models\Order\Order::find($record->id)
            );
        }

        $hopi_requested_coin = $hopiOrder->meta()
            ->where("key", "hopi_requested_coin")
            ->first();

        if ($hopi_requested_coin?->value > 0)
            \App\Jobs\HopiRefundCoinRequest::dispatchSync(
                \App\Models\Order\Order::find($record->id)
            );
    }

    private function restoreStockAction(Model $record)
    {
        // On board üzerinden İptal ve iade edilen siparişin ürünleri onaylanıp onaylanmadığına bakılmaksızın stoklarına geri eklenir, çünkü tüm ürünlerin stokları sipariş anında düşürülür.
        $record->orderItems->filter(function ($item) {
            //return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1 && $item->product->purchasable == 'in_stock';
            return $item->product->purchasable == 'in_stock';
        })->each(function ($item) {
            $item->product->increment('stock', $item->quantity);
        });
    }

    private function orderCancelledActions(Model $record)
    {
        // early return if order is payed with tosla
        if ($this->isToslaPayedOrder($record)) {
            try {
                $this->cancelToslaPayedOrder($record);
                $record->orderTransactions()->delete(); // İptal edilen siparişin tüm ödemeleri silinir.
            } catch (Exception $e) {
                logger()->error($e);
                Notification::make()
                    ->title('Tosla iade hatası => ' . $e->getMessage())
                    ->danger()
                    ->send();
            }
        }

        // standard payment with iyzico
        $record->orderTransactions()->delete(); // İptal edilen siparişin tüm ödemeleri silinir.
        try {
            $t = Transaction::where('order_id', $record->id)->whereNull('refunds')->first();
            if ($t)
                $refund = IyzipayFacade::partitialRefund($t, $t->amount); // siparişin ilk kira tahsilatı iade edilecek
        } catch (TransactionVoidException $e) {
            logger($e);
            Notification::make()
                ->title('Iyzico iade hatası => ' . $e->getMessage())
                ->danger()
                ->send();
        }

        return $record->fresh();
    }

    private function isToslaPayedOrder(Model $record): bool
    {
        return Meta::where('metaable_id', $record->id)->where('metaable_type', \App\Models\Order\Order::class)->where('key', 'tosla_transaction_id')->count() > 0;
    }

    private function cancelToslaPayedOrder(Model $record)
    {
        $orderTransactionToRefund = $record->orderTransactions()
            ->where('payment_type', 'tosla')
            ->where('payment_status_id', 1) // 1 is paid
            ->where('card_id', -9999) // -9999 is tosla card id
            ->first();

        if ($orderTransactionToRefund) {
            $tosla = new \App\Services\Tosla\Tosla();
            $tosla->getAuth();
            $processId = Meta::where('metaable_id', $record->id)->where('metaable_type', \App\Models\Order\Order::class)->where('key', 'tosla_process_id')->first()->value;
            $refundResult = $tosla->refund($processId, $orderTransactionToRefund->amount, $record->user->phone, $record);

            // update order transaction payment status to refunded
            $orderTransactionToRefund->update([
                'payment_status_id' => 4, // 4 is refunded
            ]);
        }
    }

    private function orderApprovedActions(Model $record)
    {
        if (!$record->finance_approved_at)
            $record->finance_approved_at = now();

        $this->setContractExpiredAt($record);

        // Siparişin tüm ürünleri için ödeme kontrolü yapılmışsa siparişin ödeme kontrolü tamamlanmış sayılır.
        $it = $record->orderItems->filter(function ($item) {
            return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1;
        })->count();

        if ($it != $record->orderItems->count()) {

            $amountBeforeTheReevaluation = $record->orderTransactions->first()->amount;
            $total = $record->orderItems->filter(function ($item) {
                return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1;
            })->sum('total');

            $insuranceTotal = $record->orderItems->filter(function ($item) {
                return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1;
            })->sum('insurance_price');

            $discount = 0;
            if ($record->coupon_id)
                $discount = $this->calculateCouponDiscount($record);

            try {
                DB::transaction(function () use ($record, $total, &$data, $discount) {
                    $enFazlaTaksideSahipUrununAyi = SubscriptionMonths::find($record->orderItems->where('payment_control', true)->where('findex_control', true)->where('is_user_suitable_control', true)->max('plan'))->value;
                    $planBaslangicTarihi = $record->created_at;
                    $cardId = $record->orderTransactions->first()->card_id;

                    // Calculate new prices
                    $record->sub_total = $total / 120 * 100;
                    $record->tax_amount = $total / 120 * 20;
                    $record->total = $total;
                    $record->save();

                    $record->orderTransactions()->delete();

                    $pp = new PaymentPlan($record->created_at, $cardId, $record->id);
                    $pp->add($total, $enFazlaTaksideSahipUrununAyi);
                    $pp->save();

                    // if coupon is used, update first payment plan for the coupon
                    if ($record->coupon_id) {
                        $ot = $record->orderTransactions()->whereNull('deleted_at')->first();
                        for ($i = 1; $i <= SubscriptionMonths::getSelectedMonth($record->coupon->effected_months); $i++) {
                            // Check if there is not transaction record for the month
                            if ($ot) {
                                $ot->amount = $total + $discount;
                                $ot->note = 'Kupon indirimi ' . $ot->id . ' numaralı ödeme planına eklendi. Toplam indirim: ' . $discount . ' TL';
                                $ot->save();
                                $ot = $ot->nextTransection();
                            }
                        }
                    }
                });
            } catch (TransitionNotAllowed $e) {
                logger($e);
                Notification::make()
                    ->title($e->getMessage())
                    ->danger()
                    ->send();

                $this->halt();

                return $record->fresh();
            } catch (TransitionNotFound $e) {
                logger($e);
                Notification::make()
                    ->title($e->getMessage())
                    ->danger()
                    ->send();

                $this->halt();

                return $record->fresh();
            }

            // Aradaki farkın IYZICO ya aktarımı, üst db transection içeren bloktan çıkarıldı çünkü eğer iyzico dan iade onaylanmaz ise süreç sorun yaşamamalı dendi
            try {
                $t = Transaction::where('order_id', $record->id)->whereNull('refunds')->first();
                $refund = IyzipayFacade::partitialRefund($t, $amountBeforeTheReevaluation - ($total + $discount + $insuranceTotal));
            } catch (TransactionVoidException $e) {
                logger($e);
                Notification::make()
                    ->title('Iyzico iade hatası => ' . $e->getMessage())
                    ->danger()
                    ->send();

                Notification::make()
                    ->title('Yeni ödeme planı oluşturulmuştur.')
                    ->success()
                    ->duration(10000)
                    ->send();

                return $record->fresh();
            }

            Notification::make()
                ->title('Kısmi ödeme yapılmıştır. Yeni ödeme planı oluşturulmuş ve iade gerçekleştirilmiştir')
                ->success()
                ->duration(10000)
                ->send();
        }
    }

    private function setContractExpiredAt(Model $record)
    {
        $record->orderItems->filter(function ($item) {
            return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1;
        })->map(function ($item) use ($record) {
            $item->contract_expired_at = $record->created_at->addMonths($item->planObj->value);
            $item->legal_contract_expired_at = $item->contract_expired_at;
            $item->save();
        });
    }

    private function restoreNonApprovedStockAction(Model $record)
    {
        // On board üzerinden onaylanan edilen siparişin ürünlerinden eğer stok tabiki yapıldığı halde onaylanmayan oldu ise onun stoğunu da geri döndürüyoruz.
        $record->orderItems->filter(function ($item) {
            return $item->is_user_suitable_control == 0 && $item->product->purchasable == 'in_stock';
        })->each(function ($item) use ($record) {
            $item->product->increment('stock', $item->quantity);
        });
    }

    private function hopiNotifyCheckoutRequest(Model $record)
    {
        $hopiOrder = \App\Models\Order\Order::find($record->id);
        $birdId = $hopiOrder->meta()
            ->where("key", "hopi_bird_id")
            ->first();

        if ($birdId) {
            $requestedCoin = $hopiOrder->meta()
                ->where("key", "hopi_requested_coin")
                ->first();

            $selected_campaign = $hopiOrder->meta()
                ->where("key", "hopi_selected_campaign")
                ->first();

            HopiNotifyCheckoutRequest::dispatch((int)$birdId->value, $hopiOrder, $requestedCoin?->value ?? 0, $selected_campaign?->value ?? null);
            $hopiOrder->meta()->firstOrCreate(['key' => 'hopi_notify_checkout'], ['value' => true]);
        }
    }

    private function calculateCouponDiscount(Model $order)
    {
        $cart = new Cart();
        $orderItemsToCalculate = $order->orderItems->filter(function ($item) {
            return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1;
        });

        $orderItemsToCalculate->map(function ($item) use ($cart) {
            $cart->items->push(new CartItem([
                'product_type' => ProductVariant::class,
                'product_id' => $item->product_id,
                'quantity' => $item->quantity,
                'month' => $item->plan,
            ]));
        });

        $ccd = new \App\Rules\CalculateCouponDiscount($cart, Coupon::find($order->coupon_id));
        $ccd->calculateDiscount();
        $totalDiscount = $ccd->getDiscount();

        // if coupon is rated amount, then we need to grab eligible items' prices from order items
        if ($order->coupon->type == 'rate') {
            $getEligableCartItemsTotalPrice = 0;
            $orderItemsToCalculate->pluck('product_id')->intersect($ccd->getEligableCartItems()->pluck('product_id'))->map(function ($item) use (&$getEligableCartItemsTotalPrice, $orderItemsToCalculate) {
                $getEligableCartItemsTotalPrice += $orderItemsToCalculate->where('product_id', $item)->first()->total;
            });
            $totalDiscount = $getEligableCartItemsTotalPrice * ($order->coupon->value / 100);
        }

        return round($totalDiscount > 0 ? -1 * $totalDiscount : 0, 2);
    }
}
