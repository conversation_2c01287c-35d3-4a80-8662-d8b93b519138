<?php

namespace App\Models\Product;

use App\Models\BaseModel;
use App\Models\OrderItems;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Inventory;

class ProductStock extends BaseModel
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'product_id',
        'warehouse_id',
        'product_id',
        'product_variant_id',
        'production_date',
        'expire_date',
        'quantity',
        'cost_unit',
        'cost_total',
        'note',
    ];

    public function product()
    {
        return $this->belongsTo(\App\Models\Lunar\ProductVariant::class);
    }

    public function orderItem()
    {
        return $this->belongsTo(OrderItems::class, 'order_id');
    }

    public function inventory()
    {
        return $this->belongsTo(Inventory::class);
    }
}
