<?php

namespace App\Models\Product;

use App\Contracts\CartItem;
use App\Http\Resources\ProductResource;
use App\Models\BaseModel;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Currency;
use App\Models\Lunar\Url;
use App\Models\Meta;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;


/**
 * @property string|null $description
 *
 * @property Currency|null $currency
 */
class Product extends BaseModel implements HasMedia, CartItem
{
    use HasFactory;
    use SoftDeletes;
    use InteractsWithMedia;
    use HasSlug;

    protected $connection = 'mysql-lunar-non-prefix';
    protected $table = 'products';

//    protected $indexConfigurator = ProductIndexConfigurator::class;
//
//    protected $searchRules = [
//        ProductSearchRule::class,
//    ];

//    protected $mapping = [
//        'properties' => [
//            'name_en' => [
//                'type' => 'text',
//            ]
//        ],
//    ];

    protected $fillable = [
        'category_id',
        'brand_id',
        'unit_type_id',
        'currency_id',
        'provider_id',
        'name',
        'description',
        'barcode',
        'stock_amount',
        'stock_alert',
        'order',
        'on_sale',
        'featured',
        'price',
        'tax_rate',
        'tax_included',
    ];

    protected $casts = [
        'on_sale' => 'boolean',
        'featured' => 'boolean',
        'tax_included' => 'boolean',
        'price' => 'float',
        'tax_rate' => 'float',
        'stock_amount' => 'integer',
        'stock_alert' => 'integer',
        'order' => 'integer',
        'total_order' => 'integer',
        'total_view' => 'integer',
        'total_favorite' => 'integer',
    ];

    protected $with = ['currency'];

    // Relations
    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function variants()
    {
        return $this->hasMany(ProductVariant::class);
    }

    public function prices()
    {
        return $this->hasMany(ProductPrice::class);
    }

    public function stocks()
    {
        return $this->hasMany(ProductStock::class);
    }

    public function meta()
    {
        Relation::morphMap([
            \App\Models\Lunar\Product::class => self::class,
        ]);

        return $this->setConnection('mysql')->morphMany(Meta::class, 'metaable');
    }

    public function collections()
    {
        return $this->belongsToMany(
            \App\Models\Lunar\Collection::class,
            'collection_product'
        )->withPivot(['position'])->withTimestamps();
    }

    public function defaultUrl()
    {
        return $this->hasOne(
            Url::class,
            'element'
        )->whereDefault(true);
    }

    public function getDescription(): string|null
    {
        return $this->description;
    }

    public function getPrice(null|int $month): float
    {
        return (float)$this->price;
    }

    public function getStock(): int
    {
        return (int)$this->stock_amount;
    }

    public function getTaxRate(): float
    {
        return (float)$this->tax_rate;
    }

    public function hasTaxIncluded(): bool
    {
        return (bool)$this->tax_included;
    }

    public function getDiscount(): float
    {
        // @TODO: Kampanyadan oluşan indirim tutarını ve ürünün kendi indirim tutarı gerekli
        // @TODO: düzenlemeler yapılarak gönderilecek.
        return 0;
    }

    public function getDiscountType(): string|null
    {
        return null;
    }

    public function getDiscountValue(): float
    {
        return 0;
    }

    public function getResource(): JsonResource
    {
        return ProductResource::make($this);
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }

    public function coverImage(): ?string
    {
        return $this->setConnection('mysql-lunar-non-prefix')->hasMany(Media::class, 'model_id')
            ->where('collection_name', 'products')
            ->where('model_type', 'Lunar\Models\Product')->first();

        //return $this->getFirstMedia('products')->getUrl() ?? null;
    }

    public function firstCoverImage(): \Illuminate\Database\Eloquent\Casts\Attribute
    {
        return \Illuminate\Database\Eloquent\Casts\Attribute::make(
            get: fn($value) => env('APP_URL') . $this->coverImage(),
        );
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('featured')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/jpg', 'image/webp', 'image/svg+xml']);
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('mobile')
            ->performOnCollections('featured')
            ->width(800)
            ->height(600);
    }

    public function getName(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => json_decode($attributes['attribute_data'])->name->value->tr ?? json_decode($attributes['attribute_data'])->name->value->en,
        );
    }

    public function getIsActiveAttribute()
    {
        return $this->status == 'published';
    }

    public function getMultiplier1Attribute()
    {
        return $this->meta()->where('key', 'multiplier_1')->first()?->value;
    }

    public function getMultiplier3Attribute()
    {
        return $this->meta()->where('key', 'multiplier_3')->first()?->value;
    }

    public function getMultiplier6Attribute()
    {
        return $this->meta()->where('key', 'multiplier_6')->first()?->value;
    }

    public function getMultiplier12Attribute()
    {
        return $this->meta()->where('key', 'multiplier_12')->first()?->value;
    }

    public function getMultiplier18Attribute()
    {
        return $this->meta()->where('key', 'multiplier_18')->first()?->value;
    }

    public function getMultiplier24Attribute()
    {
        return $this->meta()->where('key', 'multiplier_24')->first()?->value;
    }

}
