<?php

namespace App\Models;

use App\States\B2BLead\B2BLeadState;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\ModelStates\HasStates;

class B2BLead extends Model implements Auditable
{
    use HasFactory;
    use \OwenIt\Auditing\Auditable;
    use HasStates;

    protected $table = 'b2b_leads';

    protected $fillable = [
        'name',
        'email',
        'gsm',
        'firm_name',
        'message',
        'responsible_staff_id',
        'status',
        'communication_channels',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'communication_channels' => 'array',
        'status' => B2BLeadState::class,
    ];

    public function responsibleStaff()
    {
        return $this->belongsTo(User::class, 'responsible_staff_id');
    }

    public function notes()
    {
        return $this->morphMany(Note::class, 'notable');
    }
}
