<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ScoringSource extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'webhook_url',
        'api_key',
        'is_active',
        'send_payment_sms_on_approval',
        'payment_result_webhook_url',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'send_payment_sms_on_approval' => 'boolean',
    ];

    /**
     * Aktif olan scoring source'ları getir
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Bu kaynaktan gelen skorlama talepleri
     *
     * @return HasMany
     */
    public function scoringRequests(): HasMany
    {
        return $this->hasMany(ScoringRequest::class);
    }
}
