<?php

namespace App\Models\Lunar;

use App\Casts\AsAttributeData;
use App\Contracts\CartItem;
use App\Models\BaseModel;
use App\Models\Meta;
use App\Models\ProductFeature;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\Sitemap\Contracts\Sitemapable;
use Spatie\Sitemap\Tags\Url;

class Product extends BaseModel implements HasMedia, CartItem, Sitemapable, Auditable
{
    use InteractsWithMedia;
    use \OwenIt\Auditing\Auditable;

    protected $connection = 'mysql-lunar-non-prefix';
    protected $table = 'products';

    protected $appends = [
        'firstPhotoUrl',
        'firstPhotoUrlCover'
    ];

    protected $guarded = [];

    public function toSitemapTag(): Url|string|array
    {
        return Url::create(env('FRONTEND_URL') . '/urun/' . $this->defaultUrl?->slug)
            ->setChangeFrequency('')
            ->setPriority(false);
    }

    protected $casts = [
        'attribute_data' => AsAttributeData::class,
    ];

    public function getDescription(): string|null
    {
        return $this->description;
    }

    public function getName(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => json_decode($attributes['attribute_data'])->name->value->tr ?? json_decode($attributes['attribute_data'])->name->value->en,
        );
    }

    public function getNameMultiline(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => Str::of($this->nameWithSource)->explode(' ')->split(3)->map(function ($x) {
                return $x->implode(' ') . '</br>';
            })->implode(' '),
        );
    }

    public function getDescriptionText(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => json_decode($attributes['attribute_data'])->description->value->tr ?? json_decode($attributes['attribute_data'])->description->value->en,
        );
    }

    public function getExcerptText(): Attribute
    {
        $decoded = json_decode($this->attributes['attribute_data']);
        return Attribute::make(
            get: fn($value, $attributes) => is_object($decoded) && property_exists($decoded, 'excerpt') ? $decoded->excerpt->value?->tr ?? json_encode($decoded->excerpt) : '',
            //get: fn($value, $attributes) => property_exists($decoded, 'excerpt') ? $decoded->excerpt->value?->tr ?? $decoded->excerpt->value?->en : null,
            //get: fn($value, $attributes) => json_decode($attributes['attribute_data'])?->excerpt?->value?->tr ?? json_decode($attributes['attribute_data'])?->excerpt?->value?->en,
        );
    }

    public function storegeMedia()
    {
        return $this->hasMany(MediaStorage::class);
    }

    public function collections()
    {
        return $this->belongsToMany(
            \App\Models\Lunar\Collection::class,
            'collection_product'
        )->withPivot(['position'])->withTimestamps();
    }

    public function mediaStorage()
    {
        return $this->storegeMedia();
    }

    public function getFirstPhoto(): Attribute
    {
        return Attribute::make(
            //get: fn($value, $attributes) => $this->storegeMedia()->first()?->url ?? 'https://kiralabunu.fra1.cdn.digitaloceanspaces.com/ip/' . $this->id . '-1.png'
            get: fn($value, $attributes) => $this->firstPhotoUrl,
        );
    }

    public function getFirstPhotoUrlAttribute()
    {
        return $this->coverImage()['thumb_webp'];
    }

    public function getFirstPhotoUrlCoverAttribute()
    {
        return $this->coverImage();
    }

    public function getIsActiveAttribute()
    {
        return $this->status == 'published';
    }

    public function getPrice(null|int $month): float
    {
        return 100;
        return (float)$this->price;
    }

    public function variants()
    {
        return $this->hasMany(ProductVariant::class);
    }

    public function defaultUrl()
    {
        return $this->hasOne(\App\Models\Lunar\Url::class, 'element_id', 'id');
        //        return $this->morphOne(
        //            Url::class,
        //            'element'
        //        )->whereDefault(true);
    }

    public function productFeatures()
    {
        return $this->hasMany(ProductFeature::class);
    }

    public function meta()
    {
        return $this->setConnection('mysql')->morphMany(Meta::class, 'metaable');
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    public function getTaxRate(): float
    {
        return (float)$this->tax_rate;
    }

    public function hasTaxIncluded(): bool
    {
        // TODO: Implement hasTaxIncluded() method.
    }

    public function getStock(): int
    {
        // TODO: Implement getStock() method.
    }

    public function getDiscount(): float
    {
        // TODO: Implement getDiscount() method.
    }

    public function getDiscountType(): string|null
    {
        // TODO: Implement getDiscountType() method.
    }

    public function getDiscountValue(): float
    {
        // TODO: Implement getDiscountValue() method.
    }

    public function coverImage(): ?array
    {
        $res = $this->setConnection('mysql-lunar-non-prefix')->hasMany(Media::class, 'model_id')
            ->where('collection_name', 'products')
            ->where('model_type', 'Lunar\Models\Product')->first();

        if (!$res) {
            return [
                "thumb_webp" => "",
                "zoom_webp" => "",
                "thumb" => "",
            ];
        }

        // get file name from removing the extension
        $fileName = Str::beforeLast($res->file_name, '.');

        return [
            "thumb_webp" => "https://kiralabunu.fra1.cdn.digitaloceanspaces.com/products/{$res->created_at->format('Y/m/d')}/conversions/{$fileName}-thumb_webp.webp",
            "zoom_webp" => "https://kiralabunu.fra1.cdn.digitaloceanspaces.com/products/{$res->created_at->format('Y/m/d')}/conversions/{$fileName}-zoom_webp.webp",
            "thumb" => "https://kiralabunu.fra1.cdn.digitaloceanspaces.com/products/{$res->created_at->format('Y/m/d')}/conversions/{$fileName}-zoom.png",
        ];
    }

    public function getResource(): JsonResource
    {
        // TODO: Implement getResource() method.
    }

    public function coupons(): MorphToMany
    {
        return $this->setConnection('mysql')->morphToMany(Product::class, 'couponable', 'coupon_restrictors', 'coupon_id', 'couponable_id', 'id', 'id');
    }

    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'taggables', 'taggable_id', 'tag_id');
    }

    public function images()
    {
        return $this->setConnection('mysql-lunar-non-prefix')->hasMany(Media::class, 'model_id')
            ->where('collection_name', 'products')
            ->where('model_type', 'Lunar\Models\Product');
    }

    // attribute for name with product source if exists
    public function nameWithSource(): Attribute
    {
        $source = '';

        if ($this->meta()->where('key', 'aynet_product_id')->exists()) {
            $source = '(Aynet)';
        }

        return Attribute::make(
            get: fn($value, $attributes) => $this->name . ' ' . $source,
        );
    }
}
