<?php

namespace App\Models\Lunar;

use App\Models\Lunar\Casts\Price as CastsPrice;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;


class Price extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;

    protected $connection = 'mysql-lunar';

    //    protected $primaryKey = 'prices.id';

    protected $appends = [
        'price',
        'nonObjectPrice'
    ];

    protected $fillable = ['price', 'compare_price', 'priceable_type', 'subscription_months_id', 'currency_id', 'deleted_at'];

    protected $casts = [
        'price' => CastsPrice::class,
        'compare_price' => CastsPrice::class,
    ];

    public function subscriptionMonths()
    {
        return $this->belongsTo(SubscriptionMonths::class);
    }

    public function product()
    {
        return $this->belongsTo(ProductVariant::class, 'priceable_id');
    }

    // Filament resource içerisinde hydration attribute accessor ve mutator tetikleyemediği için bu formatta yapılmak zorunda.
    public function getNonObjectPriceAttribute()
    {
        return $this->price->value / 100;
    }

    public function setNonObjectPriceAttribute($value)
    {
        $this->price = $value * 100;
    }
}
