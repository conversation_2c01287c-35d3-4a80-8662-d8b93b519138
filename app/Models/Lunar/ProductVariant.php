<?php

namespace App\Models\Lunar;

use App\Contracts\CartItem;
use App\Http\Resources\ProductVariantResource;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\Conversions\Conversion;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\MediaCollections\FileAdder;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class ProductVariant extends BaseModel implements HasMedia, CartItem, Auditable
{

    use \OwenIt\Auditing\Auditable;

    protected $connection = 'mysql-lunar-non-prefix';
    protected $table = 'product_variants';

    protected $guarded = [];

    public function prices()
    {
        return $this->hasMany(Price::class, 'priceable_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function options()
    {
        return $this->hasMany(ProductOptionValueProductVariant::class, 'variant_id', 'id');
    }

    public function getVaariant(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => $this->options()->get()->map(fn($option) => json_decode($option->productOptionValue->name)->tr ?? '')->implode(', '),
        );
    }

    public function getIsActiveAttribute()
    {
        return $this->deleted_at === null;
    }

    public function isStockManaged(): bool
    {
        return $this->purchasable === 'in_stock';
    }

    // çalışmıyor çünkü bu projede App\Models\Lunar\ProductVariant ama Lunar tarafında Lunar\Models\ProductVariant
//    public function prices()
//    {
//        return $this->morphMany(
//            Price::class,
//            'priceable',
//            'priceable_type',
//            'priceable_id',
//        );
//    }

    public function getDescription(): string|null
    {
        // TODO: Implement getDescription() method.
    }

    public function getPrice(int|null $month): float
    {
        $key = 'get_price_' . $this->id . '_' . $month;
        if (cache($key))
            return cache($key);

        $response = $this->prices()->where('subscription_months_id', $month)->first()->price->value / 100;
        cache([$key => $response], now()->addSeconds(3));
        return $response;
    }

    public function getMonth(): int
    {
        return 5;
        return (float)$this->price;
    }

    public function getTaxRate(): float
    {
        return 20;
        return (float)$this->product->tax_rate;
    }

    public function hasTaxIncluded(): bool
    {
        return true;
        return (bool)$this->product->tax_included;
    }

    public function getStock(): int
    {
        return (int)$this->stock_amount;
    }

    public function getDiscount(): float
    {
        return 0;
    }

    public function getDiscountType(): string|null
    {
        return null;
        // TODO: Implement getDiscountType() method.
    }

    public function getDiscountValue(): float
    {
        return 0;
    }

    public function getResource(): JsonResource
    {
        return ProductVariantResource::make($this);
    }

    public function media(): MorphMany
    {
        // TODO: Implement media() method.
    }

    public function addMedia(string|UploadedFile $file): FileAdder
    {
        // TODO: Implement addMedia() method.
    }

    public function copyMedia(string|UploadedFile $file): FileAdder
    {
        // TODO: Implement copyMedia() method.
    }

    public function hasMedia(string $collectionName = ''): bool
    {
        // TODO: Implement hasMedia() method.
    }

    public function getMedia(string $collectionName = 'default', callable|array $filters = []): Collection
    {
        // TODO: Implement getMedia() method.
    }

    public function clearMediaCollection(string $collectionName = 'default'): HasMedia
    {
        // TODO: Implement clearMediaCollection() method.
    }

    public function clearMediaCollectionExcept(string $collectionName = 'default', array|Collection $excludedMedia = []): HasMedia
    {
        // TODO: Implement clearMediaCollectionExcept() method.
    }

    public function shouldDeletePreservingMedia(): bool
    {
        // TODO: Implement shouldDeletePreservingMedia() method.
    }

    public function loadMedia(string $collectionName)
    {
        // TODO: Implement loadMedia() method.
    }

    public function addMediaConversion(string $name): Conversion
    {
        // TODO: Implement addMediaConversion() method.
    }

    public function registerMediaConversions(Media $media = null): void
    {
        // TODO: Implement registerMediaConversions() method.
    }

    public function registerMediaCollections(): void
    {
        // TODO: Implement registerMediaCollections() method.
    }

    public function registerAllMediaConversions(): void
    {
        // TODO: Implement registerAllMediaConversions() method.
    }
}
