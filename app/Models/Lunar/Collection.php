<?php

namespace App\Models\Lunar;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use App\Casts\AsAttributeData;

class Collection extends Model
{
    //    use SoftDeletes;

    protected $connection = 'mysql-lunar';

    protected $casts = [
        'attribute_data' => AsAttributeData::class,
    ];

    protected $guarded = [];

    public function getName(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => json_decode($attributes['attribute_data'])->name->value->tr ?? json_decode($attributes['attribute_data'])->name->value->en,
        );
    }

    public function defaultUrl()
    {
        return $this->hasOne(
            Url::class,
            'element_id'
        )->where('element_type', 'Lunar\Models\Collection')
            ->whereDefault(true);
    }
}
