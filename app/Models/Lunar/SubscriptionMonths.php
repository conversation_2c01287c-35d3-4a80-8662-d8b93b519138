<?php

namespace App\Models\Lunar;

use Illuminate\Database\Eloquent\Model;

class SubscriptionMonths extends Model
{
    protected $connection = 'mysql-lunar';

    public static function getSelectedMonth($month): int
    {
        return match ((string)$month) {
            '1' => 1,
            '2' => 3,
            '3' => 6,
            '4' => 12,
            '5' => 18,
            '6' => 2,
            '7' => 24,
            '8' => 36,
            default => 18,
        };
    }
}
