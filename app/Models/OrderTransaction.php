<?php

namespace App\Models;

use App\Actions\OrderTransaction\sendPaymentToParasut;
use App\States\OrderTransactionCustomerContact\CustomerContactState;
use App\StorableClasses\Plan;
use DateTimeInterface;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Mail;
use OwenIt\Auditing\Contracts\Auditable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderTransaction extends Model implements Auditable
{
    use HasFactory;
    use SoftDeletes;
    use \OwenIt\Auditing\Auditable;

    protected $guarded = [];

    protected $dates = [
        'due_date',
        'last_payment_check',
    ];

    protected $casts = [
        'customer_contact_status' => CustomerContactState::class,
    ];

    public function paymentStatus()
    {
        return $this->belongsTo(PaymentStatus::class, 'payment_status_id');
    }

    public function creditCard()
    {
        return $this->belongsTo(CreditCard::class, 'card_id');
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function sameOrderTransactions()
    {
        return $this->hasMany(OrderTransaction::class, 'order_id', 'order_id');
    }

    public function nextTransection()
    {
        return $this->sameOrderTransactions()->where('due_date', '>', $this->due_date)->first();
    }

    public function meta()
    {
        return $this->morphMany(Meta::class, 'metaable');
    }

    public function getInvoiceAttribute()
    {
        return $this->meta()->where('key', 'parasut_renting_invoice_id')->first()?->value;
    }

    public function getPaymentAttribute()
    {
        return $this->hasOne(Transaction::class)->first()?->iyzipay_key;
    }

    public function CustomerContactStatusLabel(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => $this->customer_contact_status->getLabel(),
        );
    }

    public function lastPayedRentsDate(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => $this->getAllSameOrderTransactions()->where('payment_status_id', 1)->where('amount', '>', 0)->sortByDesc('due_date')->first()?->due_date,
            //get: fn(mixed $value, array $attributes) => $this->sameOrderTransactions()->where('payment_status_id', 1)->where('amount', '>', 0)->orderBy('due_date', 'desc')->first()->due_date,
        );
    }


    public function delayedRentCountInMonths(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => $this->getAllSameOrderTransactions()->where('payment_status_id', 2)
                ->filter(function ($transaction) {
                    $paymentDueDays = $this->order->b2bOrder->payment_due_days ?? 0;
                    $adjustedDueDate = $transaction->due_date->copy()->addDays($paymentDueDays);

                    return $adjustedDueDate->lessThanOrEqualTo(now());
                })
                ->count(),
        );
    }

    public function totalDelayedAmount(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => $this->getAllSameOrderTransactions()->where('payment_status_id', 2)
                ->filter(function ($transaction) {
                    $paymentDueDays = $this->order->b2bOrder->payment_due_days ?? 0;
                    $adjustedDueDate = $transaction->due_date->copy()->addDays($paymentDueDays);

                    return $adjustedDueDate->lessThanOrEqualTo(now());
                })
                ->sum('amount'),
            //get: fn(mixed $value, array $attributes) => $this->sameOrderTransactions()->where('payment_status_id', 2)->where('amount', '>', 0)->where('due_date', '<', now())->sum('amount'),
        );
    }

    private function getAllSameOrderTransactions()
    {
        return cache()->remember('all_same_order_transactions_' . $this->order_id, 60 * 60 * 12, function () {
            return $this->sameOrderTransactions()->where('amount', '>', 0)->get();
        });
    }

    public function payWithAmount($amount, $cardID)
    {
        ///dd($amount, $cardID);
        // Ödeme Al

        $odenenBakiye = $amount;
        $aylikTutar = $this->amount;
        $kalanBakiye = $odenenBakiye;
        $odenecekSatir = $this;
        $user = $this->order->user;

        //        $plan = Subscription::where('billable_id', $user->id)->latest()->first()->plan;

        $plan = new Plan();
        $plan->name($user->fullName . "/" . $this->order->order_number . ' TK' . $this->id)
            ->price($amount)
            ->attributes($this->order->only(['id', 'user_id', 'coupon_id', 'order_number']));

        try {
            //$transaction = $user->pay(collect([$plan]), $this->creditCard, $plan->currency, 1, true);
            $cc = CreditCard::find($cardID);
            $transaction = $user->pay(collect([$plan]), $cc, $plan->currency, 1, true);
            $transaction->order_id = $this->order->id;
            $transaction->save();

            $pt = new PaymentTransaction();
            $pt->amount = $amount;
            $pt->order_transaction_id = $this->id;
            $pt->order_id = $this->order_id;
            $pt->payment_status_id = 1;
            $pt->card_id = $cardID;
            $pt->moderator = auth()->id(); // Manual Ödemeyi yapan kişi
            $pt->payment_type = 'IYZICO';
            $pt->bank_last_message = '';
            $pt->save();

            // Parasut ödeme gönderimi
            \App\Jobs\sendPaymentToParasut::dispatch($this);
        } catch (\Exception $e) {
            $odenecekSatir->bank_last_message = $e->getMessage();
            $odenecekSatir->last_payment_check = now();
            $odenecekSatir->save();

            $pt = new PaymentTransaction();
            $pt->amount = $amount;
            $pt->order_transaction_id = $this->id;
            $pt->order_id = $this->order_id;
            $pt->payment_status_id = 3;
            $pt->card_id = $cardID;
            $pt->moderator = auth()->id(); // Manual Ödemeyi yapan kişi
            $pt->payment_type = 'IYZICO';
            $pt->bank_last_message = $e->getMessage();
            $pt->save();

            Notification::make()
                ->title($e->getMessage())
                ->danger()
                ->send();

            return;
            //throw new \Exception($e->getMessage());
        }

        //$res = $user->pay(collect([$plan]));

        do {

            if ($kalanBakiye >= $aylikTutar) {
                // Ayı kapat
                $kalanBakiye = $kalanBakiye - $aylikTutar;

                // Ödeme Alındıktan sonra satır kapa ve yeni satır ekle
                $odenecekSatir->payment_status_id = 1; // Ödendi
                $odenecekSatir->amount = $aylikTutar;
                $odenecekSatir->moderator = auth()->id();
                $odenecekSatir->payment_type = 'IYZICO';
                $odenecekSatir->bank_last_message = '';
                $odenecekSatir->last_payment_check = now();
                $odenecekSatir->note = 'Son 4 hanesi ' . $cc->number . ' olan karttan iyzico üzerinden ödeme alındı.';
                $odenecekSatir->save();

                $odenecekSatir = $odenecekSatir->nextTransection();
            } else {
                // Kalan bakiye ayı kapamıyor o yüzden şimdi kısmi ödeme
                $odenecekSatir->payment_status_id = 1; // Ödendi
                $odenecekSatir->amount = $kalanBakiye;
                $odenecekSatir->moderator = auth()->id();
                $odenecekSatir->payment_type = 'IYZICO';
                $odenecekSatir->bank_last_message = '';
                $odenecekSatir->last_payment_check = now();
                $odenecekSatir->note = 'Son 4 hanesi ' . $cc->number . ' olan karttan iyzico üzerinden ödeme alındı.';
                $odenecekSatir->save();

                // Ödenememiş kısım için yeni bir satır tanımla
                $this->create([
                    'due_date' => $odenecekSatir->due_date,
                    'order_id' => $this->order_id,
                    'card_id' => $this->card_id,
                    'payment_status_id' => 2, // Ödenmedi
                    'amount' => $aylikTutar - $kalanBakiye,
                ]);

                $kalanBakiye = 0;
            }
        } while ($kalanBakiye > 0);

        //        // Ödeme Al
        //        $user = $this->order->user;
        ////        $plan = new Plan();
        ////        $plan->name($user->fullName . "/" . $order->id)
        ////            ->price($order->total)
        ////            ->attributes($order->toArray());
        //
        ////        $user->subscribe($plan);
        //        $plan = Subscription::where('billable_id', $user->id)->latest()->first()->plan;
        //        $plan->price = $amount;
        //        $res = $user->pay(collect([$plan]));
        //
        //        $taksitBakiyesi = $this->amount;
        //        // Ödeme Alındıktan sonra satır kapa ve yeni satır ekle
        //        $this->payment_status_id = 1; // Ödendi
        //        $this->amount = $amount;
        //        $this->moderator = auth()->id();
        //        $this->payment_type = 'IYZICO';
        //        $this->bank_last_message = '';
        //        $this->last_payment_check = now();
        //        $this->save();
        //
        //        $this->create([
        //            'due_date' => $this->due_date,
        //            'order_id' => $this->order_id,
        //            'card_id' => $this->card_id,
        //            'payment_status_id' => 2, // Ödenmedi
        //            'amount' => $taksitBakiyesi - $amount,
        //        ]);
        //
        //        // Tüm ödeme listelerindeki ekrana yönlendir
        //        return redirect()->route('filament.resources.all-order-transactions.view', ['record' => $this->id]);
    }

    public function registerBankPayment($amount, ?string $filename)
    {
        // Ödeme Al

        $odenenBakiye = $amount;
        $aylikTutar = $this->amount;
        $kalanBakiye = $odenenBakiye;
        $odenecekSatir = $this;

        do {

            if ($kalanBakiye >= $aylikTutar) {
                // Ayı kapat
                $kalanBakiye = $kalanBakiye - $aylikTutar;

                // Ödeme Alındıktan sonra satır kapa ve yeni satır ekle
                $odenecekSatir->payment_status_id = 1; // Ödendi
                $odenecekSatir->amount = $aylikTutar;
                $odenecekSatir->moderator = auth()->id();
                $odenecekSatir->payment_type = 'Havale';
                $odenecekSatir->filename = $filename;
                $odenecekSatir->bank_last_message = '';
                $odenecekSatir->save();

                $odenecekSatir = $odenecekSatir->nextTransection();
            } else {
                // Kalan bakiye ayı kapamıyor o yüzden şimdi kısmi ödeme
                $odenecekSatir->payment_status_id = 1; // Ödendi
                $odenecekSatir->amount = $kalanBakiye;
                $odenecekSatir->moderator = auth()->id();
                $odenecekSatir->payment_type = 'Havale';
                $odenecekSatir->filename = $filename;
                $odenecekSatir->bank_last_message = '';
                $odenecekSatir->save();

                // Ödenememiş kısım için yeni bir satır tanımla
                $this->create([
                    'due_date' => $odenecekSatir->due_date,
                    'order_id' => $this->order_id,
                    'card_id' => $this->card_id,
                    'payment_status_id' => 2, // Ödenmedi
                    'amount' => $aylikTutar - $kalanBakiye,
                ]);

                $kalanBakiye = 0;
            }
        } while ($kalanBakiye > 0);

        // Tüm ödeme listelerindeki ekrana yönlendir
        return redirect()->route('filament.resources.all-order-transactions.view', ['record' => $this->id]);
    }

    public function addExtraFees($cancellation_fee = 0, $missing_piece_fee = 0)
    {
        $this->create([
            'due_date' => now(),
            'order_id' => $this->order_id,
            'card_id' => $this->card_id,
            'payment_status_id' => $cancellation_fee > 0 ? 5 : 1, // Ödenmedi
            'amount' => $cancellation_fee,
        ]);

        if ($missing_piece_fee > 0) {
            $this->create([
                'due_date' => now(),
                'order_id' => $this->order_id,
                'card_id' => $this->card_id,
                'payment_status_id' => 5, // Ödenmedi
                'amount' => $missing_piece_fee,
            ]);
        }
    }

    public function removeBankPayment()
    {
        $this->update([
            'payment_status_id' => 2, // Ödenmedi
            'filename' => null,
            'payment_type' => null,
        ]);
    }

    public function sendOfficalDelayEmail()
    {
        Mail::driver('hukuk')->queue(new \App\Mail\OfficalDelayEmail($this));
        $this::update(['offical_delay_emailed_at' => now()]);
    }

    public function getPaidStatusId(): int
    {
        return match ($this->payment_status_id) {
            2 => 1, // Tahsil Edildi
            6 => 7, // Sigorta / Ödendi
        };
    }

    protected function serializeDate(DateTimeInterface $date)
    {
        return \Carbon\Carbon::instance($date)->toISOString(true);
    }

    public function sendPaymentToParasut()
    {
        // call invokable action
        (new sendPaymentToParasut())($this);
    }

    public function saveParasutPaymentId($paymentId)
    {
        $this->meta()->create([
            'key' => 'parasut_payment_sent',
            'value' => $paymentId
        ]);
    }
}
