<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class ScoringLimit extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tckn',
        'user_id',
        'scoring_request_id',
        'score',
        'approved_limit',
        'remaining_limit',
        'valid_until',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'score' => 'integer',
        'approved_limit' => 'decimal:2',
        'remaining_limit' => 'decimal:2',
        'valid_until' => 'datetime',
    ];

    /**
     * User relationship
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scoring request relationship
     *
     * @return BelongsTo
     */
    public function scoringRequest(): BelongsTo
    {
        return $this->belongsTo(ScoringRequest::class);
    }

    /**
     * Scope for valid limits (not expired)
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeValid(Builder $query): Builder
    {
        return $query->where('valid_until', '>', now());
    }

    /**
     * Scope for expired limits
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeExpired(Builder $query): Builder
    {
        return $query->where('valid_until', '<=', now());
    }

    /**
     * Check if the limit is still valid
     *
     * @return bool
     */
    public function isValid(): bool
    {
        return $this->valid_until > now();
    }

    /**
     * Check if the limit is expired
     *
     * @return bool
     */
    public function isExpired(): bool
    {
        return !$this->isValid();
    }

    /**
     * Get days remaining until expiry
     *
     * @return int
     */
    public function getDaysRemaining(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return now()->diffInDays($this->valid_until);
    }

    /**
     * Check if limit has available balance
     *
     * @param float $amount
     * @return bool
     */
    public function hasAvailableBalance(float $amount): bool
    {
        return $this->isValid() && $this->remaining_limit >= $amount;
    }

    /**
     * Use part of the limit
     *
     * @param float $amount
     * @return bool
     */
    public function useLimit(float $amount): bool
    {
        if (!$this->hasAvailableBalance($amount)) {
            return false;
        }

        $this->remaining_limit -= $amount;
        return $this->save();
    }

    /**
     * Restore part of the limit
     *
     * @param float $amount
     * @return bool
     */
    public function restoreLimit(float $amount): bool
    {
        $this->remaining_limit = min($this->remaining_limit + $amount, $this->approved_limit);
        return $this->save();
    }

    /**
     * Get the latest valid limit for a TCKN
     *
     * @param string $tckn
     * @return ScoringLimit|null
     */
    public static function getLatestValidByTckn(string $tckn): ?ScoringLimit
    {
        return static::where('tckn', $tckn)
            ->valid()
            ->orderBy('created_at', 'desc')
            ->first();
    }

    /**
     * Create a new scoring limit from scoring result
     *
     * @param ScoringRequest $scoringRequest
     * @param float $approvedLimit
     * @param int $score
     * @param User|null $user
     * @return ScoringLimit
     */
    public static function createFromScoringResult(
        ScoringRequest $scoringRequest,
        float $approvedLimit,
        int $score,
        ?User $user = null
    ): ScoringLimit {
        return static::create([
            'tckn' => $scoringRequest->tckn,
            'user_id' => $user?->id,
            'scoring_request_id' => $scoringRequest->id,
            'score' => $score,
            'approved_limit' => $approvedLimit,
            'remaining_limit' => $approvedLimit,
            'valid_until' => now()->addDays(30),
        ]);
    }
}