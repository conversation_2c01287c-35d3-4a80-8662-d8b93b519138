<?php

namespace App\Models;

use App\States\ScoringRequest\PendingState;
use App\States\ScoringRequest\ScoringRequestState;
use App\Traits\HasScoringLimits;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Str;
use Spatie\ModelStates\HasStates;

class ScoringRequest extends Model
{
    use HasFactory, HasStates, HasScoringLimits;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'ulid',
        'scoring_source_id',
        'full_name',
        'tckn',
        'email',
        'birth_date',
        'requested_amount',
        'requested_duration_months',
        'additional_data',
        'status',
        'redis_sent_at',
        'manual_processed_by',
        'manual_processed_at',
        'manual_approved_amount',
        'findex_pdf_path',
        'findex_evaluation_table',
        'skorlabunu_tracking_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'birth_date' => 'date',
        'requested_amount' => 'decimal:2',
        'manual_approved_amount' => 'decimal:2',
        'additional_data' => 'array',
        'redis_sent_at' => 'datetime',
        'manual_processed_at' => 'datetime',
        'status' => ScoringRequestState::class,
    ];

    /**
     * Boot method for generating ULID
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->ulid)) {
                // Laravel'in kendi ULID helper'ını kullan
                $model->ulid = (string) Str::ulid();
            }
        });
    }

    /**
     * Skorlama kaynağı ilişkisi
     *
     * @return BelongsTo
     */
    public function scoringSource(): BelongsTo
    {
        return $this->belongsTo(ScoringSource::class);
    }

    /**
     * Skorlama sonucu ilişkisi
     *
     * @return HasOne
     */
    public function scoringResult(): HasOne
    {
        return $this->hasOne(ScoringResultNew::class);
    }

    /**
     * Ödeme SMS linkleri ilişkisi
     *
     * @return HasMany
     */
    public function paymentSmsLinks(): HasMany
    {
        return $this->hasMany(PaymentSmsLink::class);
    }

    /**
     * Manuel işleyen kullanıcı ilişkisi
     *
     * @return BelongsTo
     */
    public function manualProcessor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manual_processed_by');
    }

    /**
     * Manuel işlenmiş mi kontrol et
     *
     * @return bool
     */
    public function isManuallyProcessed(): bool
    {
        return !is_null($this->manual_processed_at);
    }

    /**
     * Notlar ilişkisi
     *
     * @return MorphMany
     */
    public function notes(): MorphMany
    {
        return $this->morphMany(Note::class, 'notable');
    }

    /**
     * Telefon numarasını temizle (Türkiye formatı)
     *
     * @return string
     */
    public function getCleanPhoneNumber(): string
    {
        // additional_data içinden telefon numarasını al (yoksa email'den tahmin et)
        $phone = $this->additional_data['phone'] ?? null;

        if (!$phone) {
            return '';
        }

        // Sadece rakamları al
        $cleanPhone = preg_replace('/\D/', '', $phone);

        // Türkiye formatı için düzenleme
        if (strlen($cleanPhone) === 11 && substr($cleanPhone, 0, 1) === '0') {
            // 05551234567 -> +905551234567
            $cleanPhone = '+9' . $cleanPhone;
        } elseif (strlen($cleanPhone) === 10 && substr($cleanPhone, 0, 1) === '5') {
            // 5551234567 -> +905551234567
            $cleanPhone = '+90' . $cleanPhone;
        } elseif (strlen($cleanPhone) === 12 && substr($cleanPhone, 0, 2) === '90') {
            // 905551234567 -> +905551234567
            $cleanPhone = '+' . $cleanPhone;
        } elseif (strlen($cleanPhone) === 13 && substr($cleanPhone, 0, 3) === '+90') {
            // Already correct format
            return $cleanPhone;
        }

        return $cleanPhone;
    }

    /**
     * Skorlama kaynağı açıklamalı adı
     *
     * @return Attribute
     */
    protected function scoringSourceWithDetails(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (!$this->scoringSource) {
                    return '-';
                }
                
                $sourceName = $this->scoringSource->name;
                
                // scoring_source_id 1 olduğunda partner bilgilerini ekle
                if ($this->scoring_source_id == 1 && $this->additional_data) {
                    $additionalData = $this->additional_data;
                    
                    if (isset($additionalData['partner_data'])) {
                        $partnerData = $additionalData['partner_data'];
                        $partnerName = $partnerData['name'] ?? '';
                        $partnerPhone = $partnerData['phone'] ?? '';
                        
                        if ($partnerName || $partnerPhone) {
                            $sourceName .= ' / ' . $partnerName;
                            if ($partnerPhone) {
                                $sourceName .= ' / ' . $partnerPhone;
                            }
                        }
                    }
                }
                
                return $sourceName;
            }
        );
    }

    /**
     * Skorlama sonucu ilişkisi (morph)
     *
     * @return MorphMany
     */
    public function scoringResults(): MorphMany
    {
        return $this->morphMany(ScoringResult::class, 'scorable');
    }
}
