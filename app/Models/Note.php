<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Note extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'content',
        'file',
        'editor_id',
        'notable_id',
        'notable_type'
    ];

    public function notable()
    {
        return $this->morphTo();
    }

    public function editor()
    {
        return $this->belongsTo(User::class, 'editor_id');
    }
}
