<?php

namespace App\Models;

use App\Jobs\sendOrderReturnCargoCodeSMSJob;
use App\Mail\EndOfRentAggrementMail;
use App\Models\Lunar\ProductVariant;
use App\Models\ModelTraits\OrderTrait;
use App\Models\Order\OrderItem;
use App\Services\Cargo\YurticiKargoReturn;
use App\States\Order\OrderCompleted;
use App\States\Order\OrderDocumentWaiting;
use App\States\Order\OrderState;
use Exception;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\ModelStates\HasStates;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\States\Order\OrderCancelled;
use App\States\Order\OrderEvaluation;

class Order extends Model implements Auditable
{
    use HasFactory;
    use SoftDeletes;
    use HasStates;
    use \OwenIt\Auditing\Auditable;
    use OrderTrait;

    protected $casts = [
        'status' => OrderState::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'finance_approved_at' => 'datetime',
        'contract_date' => 'date',
        //        'shipping_address' => AddressesCast::class,
    ];

    protected $fillable = ['id', 'user_id', 'status', 'finance_approved_at', 'sub_total', 'tax_amount', 'total', 'shipping_address_id', 'billing_address_id', 'billing_address', 'shipping_address', 'order_number', 'created_at', 'affiliate', 'contract_code', 'contract_date', 'will_ownership_transfer'];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function orderTransaction()
    {
        return $this->belongsTo(OrderTransaction::class, 'id', 'order_id');
    }

    public function orderTransactions()
    {
        return $this->hasMany(OrderTransaction::class, 'order_id');
    }

    public function orderTransactionsReport()
    {
        return $this->hasMany(OrderTransactionsReport::class, 'order_id');
    }

    public function paymentTransaction()
    {
        return $this->belongsTo(Transaction::class, 'id', 'order_id');
    }

    public function allPaymentTransactions()
    {
        return $this->hasMany(PaymentTransaction::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class, 'order_id', 'id');
    }

    public function userOrders()
    {
        return $this->hasMany(\App\Models\Order::class, 'user_id', 'user_id')->whereNot('id', $this->id);
    }

    public function coupon()
    {
        return $this->belongsTo(Coupon::class, 'coupon_id', 'id');
    }

    public function operationalNotes(): HasMany
    {
        return $this->hasMany(OperationalNote::class);
    }

    public function paymentNotes(): MorphMany
    {
        return $this->morphMany(PaymentNote::class, 'notable');
    }

    public function totalRentPay(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => $this->orderTransactions()->where('payment_status_id', 1)->sum('amount'),
        );
    }

    public function orderId(): Attribute
    {
        return Attribute::make(
            get: fn($value) => 'KB-' . Str::padLeft($this->id, 7, 0),
        );
    }

    public function orderIdClean(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->id,
        );
    }

    public function userPhone(): Attribute
    {
        $no = Str::of($this->user->phone)->remove('(')->remove(')')->remove(' ')->remove('-');
        if ($no->startsWith(5))
            $no = $no->prepend('+90');
        if ($no->startsWith(0))
            $no = $no->prepend('+9');

        return Attribute::make(
            get: fn($value) => $no->toString(),
        );
    }

    // TODO: Tabloyu ekle ve relation üzerindne veriyi getir
    public function paymentMethod(): Attribute
    {
        return Attribute::make(
            get: fn($value) => 'IYZICO',
        );
    }

    public function notes()
    {
        return $this->morphMany(Note::class, 'notable');
    }

    public function meta()
    {
        return $this->morphMany(Meta::class, 'metaable');
    }

    public function processOrderAsCancelled($cancellation_fee, $missing_piece_fee)
    {
        // Siparişi iptal olarak işaretle
        $this->status->transitionTo(OrderCompleted::class);
        // Sipariş Ödeme Planını İptal Et
        $this->orderTransactions()->whereIn('payment_status_id', [2, 6])->where('due_date', '>=', now())->delete(); // Bekliyor, Sigorta / Ödenmedi durumları iptal edilir
        // Sipariş Ek Ücretlerini Ödeme Planına Ekle
        $this->orderTransactions()->withTrashed()->first()->addExtraFees($cancellation_fee, $missing_piece_fee);

        //        ARA DEPO SÜREÇLERİ İLE İADE ALINMAYA BAŞLANDIĞI İÇİN BU SÜREÇ KALDIRILDI
        //        // Sipariş Ürünlerini Ana Depoya Ekle
        //        $this->orderItems()->activeRentingOrderItems()->get()->map(fn($item) => $item->addProductToMainStock());
        //        // Ürün stok takipli ise stok adedini arttır, sipariş ürünlerinin Sözleşme sonlanma tarihlerini güncellemeden önce yapılmalı
        //        $this->returnProductStocks();

        // Sipariş Ürünlerinin Sözleşme Sonlanma Tarihlerini Güncelle
        $this->orderItems()->activeRentingOrderItems()->update(['legal_contract_expired_at' => now(), 'contract_expired_at' => now(), 'cancelled_at' => now()]);

        return true;
        //        return redirect()->route('filament.resources.all-orders.view', ['record' => $this->id]);
    }

    public function sendReturnCargoCode(ProductVariant $productVariant)
    {
        // Get Cargo Return Code
        $yr = new YurticiKargoReturn([
            'username' => '1048N815146906G',
            'password' => 'Kk51nUrK2wpBi08a',
            'test' => false //TEST MODE true / false
        ]);
        $cargo_return_code = $yr->createReturnRequest();

        $this->notes()->create([
            'content' => 'İade Kodu Mail Atıldı ' . $productVariant->name . ' : ' . $cargo_return_code,
        ]);

        // Send Mail
        Mail::queue(new \App\Mail\ReturnRequestOk($this->user, $this, $cargo_return_code));
        sendOrderReturnCargoCodeSMSJob::dispatchSync($this->user, $this, $cargo_return_code);

        return $cargo_return_code;
    }

    public function getLastDueDate()
    {
        return $this->orderTransactions()->orderBy('due_date', 'desc')->first()?->due_date;
    }

    public function approvedAndResumedRentingProducts(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->orderItems->where('is_user_suitable_control', true)->whereNull('cancelled_at'),
        );
    }

    /* Bu metod sendLegalAgrementComesToEndEmail gönderilmesi için hazırlandı ama
     * sonradan gerekli olan rakamın belirlenmesi için farklı bir yöntem geliştirildiği için farklı
     * bir metod kullanıldı. Bu metodun deprecated edildi
     * @deprecated
     * */
    public function approvedAndResumedRentingProductsMinPricesTotal(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->approvedAndResumedRentingProducts->map(function ($item) {

                if (!$item->product) {
                    throw new Exception('ürün yok, tanımsız ürün ID => ' . $item->id);
                }

                return $item->product->prices->min('price')->value;
            })->sum() / 100,
        );
    }

    public function approvedAndResumedRentingProductsCurrentLegalContractPriceTotal(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->approvedAndResumedRentingProducts->map(function ($item) {
                if (!$item->lastestLegalContract()) {
                    throw new Exception('Ürün sözleşme uzatımı yok => ' . $item->id);
                }
                return $item->lastestLegalContract()->amount;
            })->sum(),
        );
    }

    public function sendLegalAgrementComesToEndEmail()
    {
        try {
            // Mail queue ile gönderildiği için ekrana hata vermesi için sorun çıkarabilecek olan kodları try içerisine aldım
            $this->approvedAndResumedRentingProductsCurrentLegalContractPriceTotal;
            // Hata yok ise mail kuyruğuna ekle
            Mail::queue(new EndOfRentAggrementMail($this->user, $this));
            Notification::make()
                ->title('Mail Gönderildi')
                ->success()
                ->persistent()
                ->send();
        } catch (Exception $e) {
            Notification::make()
                ->title('Mail Gönderilemedi')
                ->body($e->getMessage())
                ->danger()
                ->seconds(30)
                ->send();
        }
    }

    public function returnProductStocks()
    {
        $this->orderItems()->activeRentingOrderItems()->get()->map(function ($item) {
            $item->returnProductStock();
        });
    }

    public function setStatusAsDocumentWaitingAndSendEmail(array $documents)
    {
        $requestedDocuments = Document::whereIn('id', $documents)->get()->pluck('name');

        // Save status
        $this->status = OrderDocumentWaiting::class;
        $this->save();

        // Send Email
        Mail::queue(new \App\Mail\PendingDocument($this->user, $this, $requestedDocuments->toArray()));

        // Save Note
        $this->notes()->create([
            'content' => 'Belge Talep Edildi: ' . implode(', ', $requestedDocuments->toArray()),
        ]);
    }

    public function updateOrderDateAndOrderTransactionPlans(Carbon $newDate): void
    {
        DB::beginTransaction();
        try {
            // Sipariş tarihini güncelle
            $this->created_at = $newDate;
            $this->save();

            // Ödeme planını güncelle
            $transactions = $this->orderTransactions()
                ->orderBy('due_date')
                ->get();

            if ($transactions->isNotEmpty()) {
                $dueDate = $newDate;

                foreach ($transactions as $transaction) {
                    $transaction->due_date = $this->calculateNextDueDate($dueDate);
                    $transaction->save();
                    $dueDate = $transaction->due_date;
                }
            }

            DB::commit();
        } catch (Exception $e) {
            logger()->error($e->getMessage() . ' ' . $e->getFile() . ' ' . $e->getLine());
            DB::rollBack();
            throw $e;
        }
    }

    public function b2bOrder()
    {
        return $this->hasOne(B2BOrder::class);
    }

    // sum of non iade order transactions
    public function nonRefundedOrderTransactionsTotal(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->orderTransactions()->where('payment_status_id', '!=', 4)->sum('amount'),
        );
    }
}
