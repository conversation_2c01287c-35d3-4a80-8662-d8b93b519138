<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class ProductStock extends Model implements Auditable
{
    use HasFactory;
    use SoftDeletes;
    use \OwenIt\Auditing\Auditable;

    protected $guarded = [];

    public function product()
    {
        return $this->morphTo();
    }

    public function moderator()
    {
        return $this->belongsTo(User::class, 'moderator_id');
    }

    public function addProductsToMainStock()
    {
        $this->inventory_id = 1;
        $this->entried_at = now();
        $this->order_id = null;
        $this->save();
    }

    public function addProductsToInterInventoryStock()
    {
        $this->inventory_id = config('app.intermediate_inventory_id'); // Inter Inventory
        $this->entried_at = now();
        $this->save();
    }

    public function inventory()
    {
        return $this->belongsTo(Inventory::class);
    }

    public function orderItem()
    {
        return $this->belongsTo(OrderItems::class, 'order_id');
    }

    public function meta()
    {
        return $this->morphMany(Meta::class, 'metaable');
    }

    public function changeProductWithVariant($data)
    {
        $this->update([
            'product_id' => $data['product_id'],
        ]);

        return $this;
    }
}
