<?php

namespace App\Models\Order;

use App\Casts\Castable\AddressCastable;
use App\Models\BaseModel;
use App\Models\Coupon;
use App\Models\Meta;
use App\Models\ModelTraits\OrderTrait;
use App\Models\Note;
use App\Models\OperationalNote;
use App\Models\OrderTransaction;
use App\Models\OrderTransactionTemp;
use App\Models\PaymentNote;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use OwenIt\Auditing\Contracts\Auditable;
use Throwable;

/**
 * @property Collection<OrderItem> $items
 * @property OrderNote $latestAdminCustomerNote
 * @property User $user
 */
class Order extends BaseModel implements Auditable
{
    use HasFactory;
    use SoftDeletes;
    use \OwenIt\Auditing\Auditable;
    use OrderTrait;

    protected $fillable = [
        'user_id',
        'warehouse_id',
        'coupon_id',

        'payment_mode',
        'payment_method',
        'payment_name',
        'payment_response',
        'payment_transaction_id',

        'user_agent',
        'device',

        'status',
        'billing_address',
        'shipping_address',

        'sub_total',
        'tax_amount',
        'tips',
        'discount_amount',
        'total',

        'customer_note',
        'note',

        'cancel_reason',
        'cancelled_at',
        'delivered_at',
        'order_number',
    ];

    protected $casts = [
        'billing_address' => AddressCastable::class . ':billing,nullable',
        'shipping_address' => AddressCastable::class . ':shipping',

        'payment_response' => 'json',

        //'status' => OrderStatus::class,

        'cancelled_at' => 'datetime',
        'delivered_at' => 'datetime',

        'sub_total' => 'float',
        'tax_amount' => 'float',
        'discount_amount' => 'float',
        'total' => 'float',
    ];

    /**
     * The relations to eager load on every query.
     *
     * @var array
     */
    protected $with = ['items.product'];

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function orderItems(): HasMany
    {
        return $this->items();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }

    public function cancelReason(): BelongsTo
    {
        return $this->belongsTo(OrderCancelReason::class);
    }

    public function notes(): MorphMany
    {
        return $this->morphMany(Note::class, 'notable');
    }

    public function operationalNotes(): HasMany
    {
        return $this->hasMany(OperationalNote::class);
    }

    public function paymentNotes(): MorphMany
    {
        return $this->morphMany(PaymentNote::class, 'notable');
    }

    public function orderTransactions(): HasMany
    {
        return $this->hasMany(OrderTransaction::class, 'order_id');
    }

    public function orderTransactionsTemp(): HasMany
    {
        return $this->hasMany(OrderTransactionTemp::class, 'order_id');
    }

    public function meta()
    {
        return $this->morphMany(Meta::class, 'metaable');
    }

    public function completedOrderSurveyMail(): HasOne
    {
        return $this->hasOne(\App\Models\CompletedOrderSurveyMail::class);
    }

    public function latestAdminCustomerNote(): HasOne
    {
        return $this->hasOne(OrderNote::class)->latestOfMany()->withDefault()->where('can_see_customer', true);
    }

    public function scopeCreatedBetween(Builder $builder, $start, $end = null): Builder
    {
        try {
            $startAt = Carbon::parse($start);
            $endAt = $end ? Carbon::parse($end) : clone $startAt;

            if ($endAt->lt($startAt)) {
                $temp = $startAt;
                $startAt = $endAt;
                $endAt = $temp;
            }

            $startAt = $startAt->startOfDay();
            $endAt = $endAt->endOfDay();

            return $builder->whereBetween('orders.created_at', [$startAt, $endAt]);
        } catch (Throwable $th) {
            // Return empty results.
            return $builder->where('orders.id', '=', -1);
        }
    }

    public function migrateTempOrderTransactionsToOrderTransactions($orderItemID): void
    {
        $lastDueDate = $this->orderTransactionsTemp()->orderBy('due_date', 'desc')->first(['due_date'])->due_date;
        $amount = 0;

        // Copy record to order_transactions
        $this->orderTransactionsTemp()->where('is_copied', false)->get()->each(function ($item) use (&$lastDueDate, &$amount) {
            $item->order->orderTransactions()->create([
                'order_id' => $item->order_id,
                'due_date' => $item->due_date,
                'amount' => $item->amount,
                'card_id' => $item->card_id,
                'payment_status_id' => 2,
            ]);
            $lastDueDate = $item->due_date;
            $amount = $item->amount;
        });

        $orderItem = OrderItem::find($orderItemID);

        $this->addLegalContract($orderItem, $lastDueDate, $amount);

        // Update contract_expired_at and is_extended_to_full_contract fields on order_items
        $orderItem->update([
            'contract_expired_at' => Carbon::parse($lastDueDate)->addMonth(),
            'legal_contract_expired_at' => Carbon::parse($lastDueDate)->addMonth(),
            'is_extended_to_full_contract' => true,
        ]);

        $this->addInsuranceLines($orderItemID);
    }

    public function addInsuranceLines($orderItemID)
    {
        // For now only requested order item will be added to insurance lines if it has insurance aggrement
        $orderItem = OrderItem::find($orderItemID);
        if ($orderItem->has_insurance) {
            foreach (self::calculateMonthsForInsuranceAddition($orderItem->plan) as $item) {
                $this->orderTransactions()->create([
                    'order_id' => $orderItem->order_id,
                    'due_date' => $orderItem->order->created_at->clone()->addMonths($item - 1),
                    'amount' => $orderItem->insurance_price,
                    'card_id' => $this->user->defaultCreditCard->first()->id ?? 0,
                    'payment_status_id' => 6, // Sigorta / Ödenmedi
                ]);
            }
        }

    }

    /**
     * @throws \Exception
     */
    public static function calculateMonthsForInsuranceAddition($subscriptionMonth): array
    {
        return match ($subscriptionMonth) {
            1 => [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18],
            2 => [4, 7, 10, 13, 16],
            3 => [7, 13],
            4 => [13],
            5 => [],
            default => throw new \Exception('Invalid subscription month'),
        };
    }

    private function addLegalContract($orderItem, $lastDueDate, $amount): void
    {
        $oldLegalContract = $orderItem->legalContracts()->orderBy('id', 'desc')->first();
        $oldLegalContractEndDate = $oldLegalContract->new_contact_date ?? $orderItem->legal_contract_expired_at;
        $orderItem->legalContracts()->create([
            'old_contact_date' => $oldLegalContractEndDate,
            'new_contact_date' => $lastDueDate->clone()->addMonths(1),
            'is_auto_extened' => true,
            'amount' => $amount,
            'moderator_id' => auth()->id(),
            'moderator_comment' => 'Sözleşmesi uzatılacaklar ekranından uzatıldı.',
        ]);
    }

    public function migrateTempOrderTransactionsToOrderTransactionsWithAllOrderItems($orderId): void
    {
        $lastDueDate = $this->orderTransactionsTemp()->orderBy('due_date', 'desc')->first(['due_date'])->due_date;
        // Copy record to order_transactions
        $this->orderTransactionsTemp()->where('is_copied', false)->get()->each(function ($item) use (&$lastDueDate) {
            $item->order->orderTransactions()->create([
                'order_id' => $item->order_id,
                'due_date' => $item->due_date,
                'amount' => $item->amount,
                'card_id' => $item->card_id,
                'payment_status_id' => 2,
            ]);
            $lastDueDate = $item->due_date;
        });

        $orderItems = OrderItem::where('order_id', $orderId)
            ->where('is_user_suitable_control', true)
            ->whereNull('cancelled_at')
            ->get();

        $orderItems->every(function ($item) use ($lastDueDate) {
            $this->addLegalContract($item, $lastDueDate, $item->total);
            $this->addInsuranceLines($item->id);
            return true;
        });

        // Update contract_expired_at and is_extended_to_full_contract fields on order_items
        $orderItems->toQuery()
            ->update([
                'contract_expired_at' => Carbon::parse($lastDueDate)->addMonth(),
                'legal_contract_expired_at' => Carbon::parse($lastDueDate)->addMonth(),
                'is_extended_to_full_contract' => true,
            ]);
    }

    public function updatePaymentPlan(OrderItem $item, Carbon $oldLegalContractEndDate, array $data): void
    {
        //dd($item->toArray(), $oldLegalContractEndDate, $data);
        // loop for data['duration']

        for ($i = 0; $i < $data['duration'] + 1; $i++) {
            $plan = $this->orderTransactions()
                ->whereBetween('due_date', [$oldLegalContractEndDate->clone()->addMonth($i)->startOfMonth(), $oldLegalContractEndDate->clone()->addMonth($i)->endOfMonth()])
                ->where('payment_status_id', 2)
                ->first();

            if ($plan) {
                $plan->increment('amount', $data['amount']);
                if ($plan->amount == 0) {
                    $plan->delete();
                }
            } else {
                $newPlan = $this->orderTransactions()->create([
                    'order_id' => $item->order_id,
                    'due_date' => $oldLegalContractEndDate->clone()->addMonth($i),
                    'amount' => $data['amount'],
                    'card_id' => $this->user->defaultCreditCard->first()->id ?? 0,
                    'payment_status_id' => 2,
                ]);
            }

            // delete insurance lines after today
            if ($item->has_insurance) {
                $planInsurance = $this->orderTransactions()
                    ->whereBetween('due_date', [$oldLegalContractEndDate->clone()->addMonth($i)->startOfMonth(), $oldLegalContractEndDate->clone()->addMonth($i)->endOfMonth()])
                    ->where('payment_status_id', 6) // Sigorta / Ödenmedi
                    ->first();

                if ($planInsurance) {
                    $planInsurance->increment('amount', $item->insurance_price / $data['amount']);
                    if ($planInsurance->amount == 0) {
                        $plan->delete();
                    }
                }
            }
        }
    }
}
