<?php

namespace App\Models\Order;

use App\Jobs\sendProductReturnedSMSJob;
use App\Jobs\sendProductSentTechnicalServiceSMSJob;
use App\Models\BaseModel;
use App\Models\LegalContract;
use App\Models\Lunar\ProductVariant;
use App\Models\Lunar\SubscriptionMonths;
use App\Models\Meta;
use App\Models\OperationalNote;
use App\Models\Order as Order2;
use App\Models\Product\Product;
use App\Models\Product\ProductOption;
use App\Models\ProductStock;
use App\Models\User;
use App\States\Order\OrderCompleted;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\Blink\Blink;

/**
 * @property Product|ProductOption|null $product
 */
class OrderItem extends BaseModel implements Auditable
{
    use HasFactory;
    use SoftDeletes;
    use \OwenIt\Auditing\Auditable;

    protected $fillable = [
        'order_id',
        'product_type',
        'product_id',
        'quantity',
        'price',
        'sub_total',
        'tax_included',
        'tax_rate',
        'tax_amount',
        'discount_type',
        'discount_value',
        'discount_amount',
        'total',
        'is_purchased',
        'plan',
        'cargo_at',
        'invoice_number',
        'product_stock_id',
        'is_supplied',
        'contract_expired_at',
        'is_extended_to_full_contract',
        'legal_contract_expired_at',
        'cancelled_at',
        'is_user_suitable_control',
        'waybill_batch',
        'has_insurance',
        'insurance_price',
        'tracking_url'
    ];

    protected $dates = [
        'cargo_at',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'price' => 'float',
        'sub_total' => 'float',
        'insurance_price' => 'float',
        'tax_included' => 'boolean',
        'has_insurance' => 'boolean',
        'is_extended_to_full_contract' => 'boolean',
        'tax_rate' => 'float',
        'tax_amount' => 'float',
        'discount_value' => 'float',
        'discount_amount' => 'float',
        'total' => 'float',
        'legal_contract_expired_at' => 'datetime',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    //    public function orderTransactionsTemp(): HasMany
    //    {
    //        return $this->order->orderTransactionsTemp();
    //    }

    public function orderTransactionsTempWithCreate(): HasMany
    {
        if (Blink::global()->get('orderTransactionsTemp', true)) {
            // Delete old records
            $this->order->orderTransactionsTemp()->delete();

            // If only one record exists, then copy it
            if ($this->order->orderItems()->where('order_items.is_user_suitable_control', true)->whereNull('cancelled_at')->count() < 2) {
                //                dd('tek ürünlü sipariş test');
                // Copy current recordss
                $this->order->orderTransactionsTemp()->createMany($this->order->orderTransactions()->orderBy('due_date')->get(['order_id', 'due_date', 'amount', 'card_id', 'payment_status_id'])->toArray());

                // Then create new records
                $firstMonth = $this->order->orderTransactionsTemp()->orderBy('due_date', 'asc')->first()->due_date->startOfMonth();
                $lastMonth = $this->order->orderTransactionsTemp()->orderBy('due_date', 'desc')->where('amount', '>', 1)->first()->due_date->startOfMonth();
                $currentCount = floor($firstMonth->floatDiffInMonths($lastMonth)) + 1;

                // Deprecated: Kısmi ödeme alımlarından dolayı oluşan aynı aya birden fazla kayıtlar dolayısı ile
                $requiredCount = 18 - $currentCount;
                $recordWillBeCopied = $this->order->orderTransactions()->orderBy('due_date', 'desc')->whereIn('payment_status_id', [1, 2])->first(['order_id', 'due_date', 'amount', 'card_id'])->toArray();

                // eğer ürün 1 aylık ve sigorta var ise önerilerde sigorta eklenmeyecek
                $orderItem = $this->order->orderItems()->where('order_items.is_user_suitable_control', true)->whereNull('cancelled_at')->first();
                if ($orderItem->plan == 1 && $orderItem->has_insurance) {
                    $recordWillBeCopied['amount'] = $recordWillBeCopied['amount'] - $orderItem->insurance_price;
                }

                for ($i = 1; $i <= $requiredCount; $i++) {
                    $date = Carbon::parse($recordWillBeCopied['due_date']);
                    $recordWillBeCopied['due_date'] = $this->order->calculateNextDueDate($date);
                    $recordWillBeCopied['is_copied'] = false;
                    $this->order->orderTransactionsTemp()->create($recordWillBeCopied);
                }
            }
            // Çok ürünlü siparişlerde
            else {

                // Get approved order items order by contract_expired_at, then get this and below items
                $approvedOrderItems = $this->order->orderItems()->whereNull('deleted_at')->where('is_user_suitable_control', 1)->orderBy('contract_expired_at')->get();
                $approvedOrderItemsAreNotExtended = $approvedOrderItems->where('is_extended_to_full_contract', false);
                $approvedOrderItemsAreExtended = $approvedOrderItems->where('is_extended_to_full_contract', true);

                if ($approvedOrderItemsAreNotExtended->count() == $approvedOrderItems->count()) {
                    // If all approved order items are not extended, then copy all records
                    $this->order->orderTransactionsTemp()->createMany($this->order->orderTransactions()->orderBy('due_date')->get(['order_id', 'due_date', 'amount', 'card_id', 'payment_status_id'])->toArray());
                } elseif ($approvedOrderItemsAreExtended->count() == $approvedOrderItems->count()) {
                    // If all approved order items are extended, then copy only extended records
                    $this->order->orderTransactionsTemp()->createMany($this->order->orderTransactions()->orderBy('due_date')->get(['order_id', 'due_date', 'amount', 'card_id', 'payment_status_id'])->toArray());
                } else {
                    //$this->order->orderTransactionsTemp()->createMany($this->order->orderTransactions()->where('due_date', '>=', $approvedOrderItems->where('is_extended_to_full_contract', 0)->first()->contract_expired_at)->orderBy('due_date')->get(['order_id', 'due_date', 'amount', 'card_id'])->toArray());
                }

                $firstMonth = $this->order->orderTransactionsTemp()->orderBy('due_date', 'asc')->first()->due_date->startOfMonth();
                $lastMonth = $this->order->orderTransactionsTemp()->orderBy('due_date', 'desc')->where('amount', '>', 1)->first()->due_date->startOfMonth();
                $currentCount = floor($firstMonth->floatDiffInMonths($lastMonth)) + 1;

                $requiredCount = 18 - $currentCount;
                $amount = $approvedOrderItemsAreNotExtended->map(function ($item) {
                    return ($item->lastestLegalContract()->amount ?? $item->price) * $item->quantity;
                })->sum();
                $recordWillBeCopied = $this->order->orderTransactions()->orderBy('due_date', 'desc')->first(['order_id', 'due_date', 'amount', 'card_id'])->toArray();

                for ($i = 1; $i <= $requiredCount; $i++) {
                    $date = Carbon::parse($recordWillBeCopied['due_date']);
                    $recordWillBeCopied['amount'] = $amount;
                    $recordWillBeCopied['due_date'] = $this->order->calculateNextDueDate($date);
                    $recordWillBeCopied['is_copied'] = false;
                    $this->order->orderTransactionsTemp()->create($recordWillBeCopied);
                }
            }

            Blink::global()->put('orderTransactionsTemp', false);
        }

        // Return new records
        return $this->order->orderTransactionsTemp();
    }

    public function order2()
    {
        return $this->belongsTo(Order2::class, 'order_id', 'id');
    }

    public function planObj()
    {
        return $this->belongsTo(SubscriptionMonths::class, 'plan', 'id');
    }

    public function product()
    {
        return $this->morphTo();
    }

    public function shipmentStatus(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->cargo_at != null ? 'Kargolandı' : '-',
        );
    }

    public function saveIsUserSuitableControl($status)
    {
        $this->is_user_suitable_control = $status;
        $this->save();
    }

    public function itemStock()
    {
        return $this->belongsTo(ProductStock::class, 'product_stock_id', 'id');
    }

    public function addProductToMainStock()
    {
        if ($this->itemStock) {
            $this->itemStock?->addProductsToMainStock();
        } else {
            // <EMAIL>
            User::find(32)->notify(new \App\Notifications\ProductInventoryReturnErrorNotification($this));
        }
    }

    public function addProductToInterInventoryStock()
    {
        if ($this->itemStock) {
            $this->itemStock?->addProductsToInterInventoryStock();
        } else {
            // <EMAIL>
            User::find(32)->notify(new \App\Notifications\ProductInventoryReturnErrorNotification($this));
        }
    }

    public function processOrderAsCancelled($cancellation_fee, $missing_piece_fee, $amount_to_be_terminated = 1)
    {
        // Eğer Sözleşme Bitiş Tarihi geçmiş ise iptal etme işlemi yerine notification gönder
        if ($this->legal_contract_expired_at == null || ($this->legal_contract_expired_at && $this->legal_contract_expired_at->isPast())) {
            Notification::make()
                ->title('Sözleşme Bitiş Tarihi Geçmiş')
                ->body('Sözleşme bitiş tarihi geçmiş bir tarih olduğundan iptal edilemedi, Lütfen IT\'den destek talep ediniz.')
                ->danger()
                ->persistent()
                ->send();
            return false;
        }

        // Mantık Değişikliği o yüzden comment
        // Sipariş Ödeme Planını İptal Et
        //        $this->order->orderTransactions()->where('payment_status_id', 2)->delete();

        // Mantık Değişikliği o yüzden comment
        // Kalan ürünlerin ödeme planını güncelle
        //        $this->addRemainingOrderItemsToPaymentPlan();

        $oldLegalContract = $this->legalContracts()->orderBy('id', 'desc')->first();
        $oldLegalContractEndDate = $oldLegalContract->new_contact_date ?? $this->legal_contract_expired_at;

        $remainingMonths = $oldLegalContractEndDate->diffInMonths(now());
        $amount = $oldLegalContract->amount ?? $this->price; // Eğer sözleşme yok ise ürün fiyatı üzerinden hesapla
        if ($this->quantity > 1) {
            $amount = $this->price * $amount_to_be_terminated;
        }

        $this->order->updatePaymentPlan($this, now(), [
            'duration' => $remainingMonths, // Ekleme tarafında seçenekler 0 indis ile başladığı için -1
            'amount' => -1 * $amount,
        ]);

        //        $legalContract = $this->legalContracts()->create([
        //            'old_contact_date' => $oldLegalContractEndDate,
        //            'new_contact_date' => $oldLegalContractEndDate->clone()->addMonths($data['duration'] + 1),
        //            'is_auto_extened' => false,
        //            'amount' => $data['amount'],
        //            'moderator_id' => auth()->id(),
        //            'moderator_comment' => $data['moderator_comment'],
        //        ]);

        if ($this->quantity == $amount_to_be_terminated) {
            $this->update([
                'legal_contract_expired_at' => now(),
                'contract_expired_at' => now(),
                'cancelled_at' => now(),
            ]);
        } else {
            $this->decrement('quantity', $amount_to_be_terminated);
        }

        // Sipariş Ek Ücretlerini Ödeme Planına Ekle
        //        $this->order->orderTransactions()->withTrashed()->first()->addExtraFees($cancellation_fee, $missing_piece_fee);

        //        if ($this->quantity == $amount_to_be_terminated) {
        //            // Sipariş Ürünlerini Ana Depoya Ekle
        //            $this->addProductToMainStock();
        //        }

        //        $this->returnProductStock($amount_to_be_terminated);

        // Eğer siparişin tek tüm ürünleri iptal ise Siparişi iptal olarak işaretle
        if ($this->checkIfAllItemsAreCancelled())
            $this->order2->status->transitionTo(OrderCompleted::class);

        return redirect()->back();
    }

    //    private function addRemainingOrderItemsToPaymentPlan()
    //    {
    //        $lastUsedCC = $this->order->orderTransactions()->where('payment_status_id', 1)->get()->last()->card_id; // Son kullanılan kredi kartı
    //        $pp = new PaymentPlan($this->order->created_at, $lastUsedCC, $this->order->id);
    //        $pp->add(0, 1);
    //        $this->order->items()->where('id', '<>', $this->id)->where('is_user_suitable_control', 1)->get()->map(function ($item) use ($pp) {
    //            $pp->add($item->total, $item->planObj->value);
    //        });
    //        $pp->saveWithoutTouchingPayedItems();
    //    }

    private function checkIfAllItemsAreCancelled(): bool
    {
        return $this->order->items()->where('is_user_suitable_control', 1)->whereNull('cancelled_at')->count() == 0;
        //        return $this->order->items->filter(function ($item) {
        //                return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1;
        //            })->count() == $this->order->items->where('is_supplied', 1)->count();
    }

    public function legalContracts(): HasMany
    {
        return $this->hasMany(LegalContract::class, 'order_item_id');
    }

    public function lastestLegalContract(): null|LegalContract
    {
        return $this->legalContracts()->latest()->first();
    }

    public function addNewLegalContract($data)
    {
        $oldLegalContract = $this->legalContracts()->orderBy('id', 'desc')->first();
        $oldLegalContractEndDate = $oldLegalContract->new_contact_date ?? $this->legal_contract_expired_at;
        $this->order->updatePaymentPlan($this, $oldLegalContractEndDate, $data);

        $legalContract = $this->legalContracts()->create([
            'old_contact_date' => $oldLegalContractEndDate,
            'new_contact_date' => $oldLegalContractEndDate->clone()->addMonths($data['duration'] + 1),
            'is_auto_extened' => false,
            'amount' => $data['amount'],
            'moderator_id' => auth()->id(),
            'moderator_comment' => $data['moderator_comment'],
        ]);

        $this->update([
            'legal_contract_expired_at' => $legalContract->new_contact_date,
            'contract_expired_at' => $legalContract->new_contact_date,
        ]);
    }

    public function updateAmountAndUnitPrice($data): self
    {
        $this->update([
            'quantity' => $data['quantity'],
            'price' => $data['price'],
            'sub_total' => $data['quantity'] * ($data['price'] - $data['price'] * $this->tax_rate / (100 + $this->tax_rate)),
            'tax_amount' => $data['quantity'] * $data['price'] * $this->tax_rate / (100 + $this->tax_rate),
            'total' => $data['quantity'] * $data['price'],
        ]);

        return $this;
    }

    public function changeProductWithVariant($data): self
    {
        // Eğer ürün stok yönetimi yapılıyorsa eski ürüb stokları arttır
        if ($this->product->isStockManaged()) {
            $this->product->increment('stock', $this->quantity);
        }

        // Eğer ürün stok yönetimi yapılıyorsa yeni ürün stokları azalt
        $variant = ProductVariant::find($data['product_id']);
        if ($variant->isStockManaged()) {
            $variant->decrement('stock', $this->quantity);
        }

        $this->update([
            'product_id' => $data['product_id'],
        ]);

        return $this;
    }

    public function scopeActiveRentingOrderItems($query)
    {
        return $query->where('is_user_suitable_control', 1)->whereNull('cancelled_at');
    }

    public function meta()
    {
        return $this->morphMany(Meta::class, 'metaable');
    }

    public function saveOperationalNotes($data)
    {
        $on = OperationalNote::create([
            'note' => $data['note'],
            'editor_id' => auth()->id(),
            'order_item_id' => $this->id,
            'order_id' => $this->order_id,
        ]);

        if (count($data['photos']) == 0) {
            $on->images()->create(
                [
                    'image_url' => '#',
                ]
            );
        }
        foreach ($data['photos'] as $photo) {
            $on->images()->create([
                'image_url' => $photo,
            ]);
        }
    }

    public function returnProductStock($amount_to_be_terminated = 1)
    {
        if ($this->product->purchasable == 'in_stock') {
            $this->product->increment('stock', $amount_to_be_terminated);
        }
    }

    public function sendProductReturnSmsAndEmail()
    {
        sendProductReturnedSMSJob::dispatchSync($this->order->user, $this);
        Mail::queue(new \App\Mail\ReturnedProductReview($this->order->user, $this->order));
    }

    public function sendProductSentTechnicalServiceSmsAndEmail()
    {
        sendProductSentTechnicalServiceSMSJob::dispatchSync($this->order->user, $this);
        Mail::queue(new \App\Mail\sendProductSentTechnicalServiceEMailNotification($this->order->user, $this->order));
    }

    // private function calculateNextDueDate(Carbon $currentDueDate): Carbon
    // {
    //     $originalDay = $this->order->created_at->day;

    //     // Ocak ayı ve 29+ gün kontrolü
    //     if ($currentDueDate->month == 1 && $originalDay >= 29) {
    //         return $currentDueDate->copy()->setDay(28)->setMonth(2);
    //     }

    //     // Ay sonlarını kontrol et
    //     if ($originalDay >= 29) {

    //         // 30 günlük aylar kontrolü
    //         if ($originalDay == 31 && in_array($currentDueDate->month, [1, 3, 5, 7, 8, 10, 12])) {
    //             return  $currentDueDate->copy()->addDays(1)->endOfMonth();
    //         }

    //         // Diğer durumlar için orijinal günü kullan
    //         return $currentDueDate->copy()->addMonth()->setDay($originalDay);
    //     }

    //     return $currentDueDate->copy()->addMonth()->setDay($originalDay);
    // }
}
