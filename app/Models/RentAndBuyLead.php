<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RentAndBuyLead extends Model
{
    use HasFactory;

    protected $table = 'rent_and_buy_leads';

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'tckn',
        'product_id',
        'status',
        'notes'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function product()
    {
        return $this->belongsTo(\App\Models\Lunar\Product::class);
    }

    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Get the orders associated with the lead through the email relation
     */
    public function orders()
    {
        return $this->hasManyThrough(
            \App\Models\Order::class,
            \App\Models\User::class,
            'email', // Foreign key on users table
            'user_id', // Foreign key on orders table
            'email', // Local key on rent_and_buy_leads table
            'id' // Local key on users table
        );
    }
}
