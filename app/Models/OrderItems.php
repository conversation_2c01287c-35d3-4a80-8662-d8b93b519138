<?php

namespace App\Models;

use App\Models\Lunar\SubscriptionMonths;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;
use App\States\Order\OrderProductNotSuitable;

class OrderItems extends Model implements Auditable
{
    use HasFactory;
    use SoftDeletes;
    use \OwenIt\Auditing\Auditable;

    protected $fillable = [
        'is_purchased',
        'product_stock_id',
        'is_supplied',
        'invoice_number',
        'cargo_at',
        'order_id',
        'product_type',
        'product_id',
        'quantity',
        'plan',
        'price',
        'sub_total',
        'tax_included',
        'tax_rate',
        'tax_amount',
        'total',
        'delivered_at',
        'tracking_url',
        'cargo_receiver_name',
        'cancelled_at',
        'waybill_batch',
    ];

    public function product()
    {
        return $this->morphTo();
    }

    public function order()
    {
        return $this->belongsto(Order::class);
    }

    public function saveIsPurchased($status, $purchase_price)
    {
        if ($status) {
            $ps = ProductStock::updateOrCreate(
                [
                    'product_id' => $this->product_id,
                    'product_type' => $this->product_type,
                    'order_id' => $this->id,
                ],
                [
                    'purchase_price' => $purchase_price,
                ]
            );
        }

        $this->is_purchased = $status;
        $this->product_stock_id = $ps->id ?? null;
        $this->save();
    }

    public function isExistOnKbInventory(): Attribute
    {
        return Attribute::make(
            get: fn($value) => ProductStock::where('product_id', $this->product_id)
                ->where('product_type', $this->product_type)
                ->where('is_out_of_order', 0)
                ->where('is_reserved', 0)
                ->where('is_fixture', 0)
                //->whereIn('inventory_id', [1, config('app.intermediate_inventory_id')]) // Ara Depodan sevk Sinan Bey talep ile kaldırıldı
                ->whereIn('inventory_id', [1])
                ->exists(),
        );
    }

    public function planObj()
    {
        return $this->belongsTo(SubscriptionMonths::class, 'plan', 'id');
    }

    public function itemStock()
    {
        return $this->belongsTo(ProductStock::class, 'product_stock_id', 'id');
    }

    public function legalContracts(): HasMany
    {
        return $this->hasMany(LegalContract::class, 'order_item_id');
    }

    public function lastestLegalContract(): LegalContract
    {
        return $this->legalContracts()->latest()->first();
    }

    public function meta()
    {
        return $this->morphMany(Meta::class, 'metaable');
    }

    public function sendTheOrderBackToOnboard($note)
    {
        // transition to order evaluation
        $this->order->status->transitionTo(OrderProductNotSuitable::class);

        // create meta for order back to onboard note
        $this->order->meta()->firstOrCreate(['key' => 'order_back_to_onboard_for_item'], ['value' => $this->id]);

        // create meta for order item note
        $this->meta()->firstOrCreate(['key' => 'order_item_back_to_onboard_note'], ['value' => $note]);
    }
}
