<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentSmsLink extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'payment_sms_links';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'scoring_request_id',
        'phone_number',
        'token',
        'expires_at',
        'sent_at',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expires_at' => 'datetime',
        'sent_at' => 'datetime',
    ];

    /**
     * Skorlama talebi ilişkisi
     *
     * @return BelongsTo
     */
    public function scoringRequest(): BelongsTo
    {
        return $this->belongsTo(ScoringRequest::class);
    }

    /**
     * SMS gönderilmiş mi kontrol et
     *
     * @return bool
     */
    public function isSent(): bool
    {
        return !is_null($this->sent_at);
    }

    /**
     * Link süresinin dolmuş olup olmadığını kontrol et
     *
     * @return bool
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Link aktif mi kontrol et (gönderilmiş ve süresi dolmamış)
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->isSent() && !$this->isExpired() && $this->status !== 'paid';
    }

    /**
     * Ödeme tamamlanmış mı kontrol et
     *
     * @return bool
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }
}
