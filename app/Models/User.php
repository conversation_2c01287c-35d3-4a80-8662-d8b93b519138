<?php

namespace App\Models;

use App\Contracts\PayableContract;
use App\Enums\Device;
use App\Models\Auth\Role;
use App\Models\Cart\Cart;
use App\StorableClasses\Address;
use App\StorableClasses\BillFields;
use App\Traits\Payable;
use App\Traits\HasScoringLimits;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasName;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Database\Eloquent\Casts\Attribute as AttributeCast;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Laravel\Passport\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use OwenIt\Auditing\Contracts\Auditable;
use App\Jobs\sendContractToParasut;

class User extends Authenticatable implements FilamentUser, MustVerifyEmail, HasLocalePreference, HasName, PayableContract, Auditable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes, HasRoles;
    use Payable;
    use \OwenIt\Auditing\Auditable;
    use HasScoringLimits;

    public function canAccessFilament(): bool
    {
        return $this->blocked_at == null && $this->deleted_at == null && str_ends_with($this->email, '@kiralabunu.com');
        //            && $this->hasVerifiedEmail()
    }

    public function canImpersonate()
    {
        return true;
    }

    protected static $cachedCart;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'date_of_birth',
        'password',
        'email_verified_at',
        'phone_verified_at',
        'tckn_verified_at',
        'blocked_at',
        'registration_ip',
        'locale',
        'device',
        'findex_credit',
        'total_limit',
        'available_limit',
        'findex_document',
        'tckn',
        'notes',
        'gender',
        'is_company',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
        'registration_ip',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'string',
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'date_of_birth' => 'date',
        'blocked_at' => 'datetime',
        'device' => Device::class . ':nullable',
        'is_company' => 'boolean',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'full_name',
        'is_verified',
        'birth_date'
        //        'bill_fields'
    ];

    public function getFilamentName(): string
    {
        return $this->fullName;
    }

    /**
     * The relations to eager load on every query.
     *
     * @var array
     */
    protected $with = ['latestOrder'];

    public function addresses()
    {
        return $this->hasMany(UserAddress::class);
    }

    public function address()
    {
        return $this->hasOne(UserAddress::class)->latest()->where('user_addresses.is_default', true);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function ordersByTCKNAndGsm()
    {
        return $this->hasMany(Order::class)
            ->when($this->tckn != '' && $this->tckn != null, function ($query) {
                $query->orWhereHas('user', function ($query) {
                    $query->where('tckn', $this->tckn);
                });
            })
            ->when($this->phone != '' && $this->phone != null, function ($query) {
                $query->orWhereHas('user', function ($query) {
                    $query->where('phone', $this->phone);
                });
            });
    }

    public function latestOrder()
    {
        return $this->hasOne(Order::class)->latestOfMany();
    }

    // public function orderReviews()
    // {
    //     return $this->hasManyThrough(OrderReview::class, Order::class);
    // }

    public function coupons()
    {
        return $this->belongsToMany(Coupon::class);
    }

    public function favorites()
    {
        return $this->hasMany(Favorite::class);
    }

    /**
     * Get the user's cart.
     *
     * @return Cart|HasOne<Cart>
     */
    public function cart()
    {
        return $this->hasOne(Cart::class);
    }

    public function creditCardsRelation()
    {
        return $this->hasMany(CreditCard::class, 'billable_id');
    }

    public function defaultCreditCard()
    {
        return $this->creditCardsRelation()->orderBy('id', 'desc');
    }

    public function currentCart(): Cart
    {
        $cart = $this->cart()->firstOrCreate(['user_id' => (string)$this->id]);

        // Load the cart's items with product relation.
        // Also product options are loaded with the product relation.
        $cart->loadMissing('items.product');

        //        $cart->items->filter(function ($item) {
        //            return $item->product->getStock() < 1;
        //        })->each(function ($item) {
        //            // @TODO: Bildirim gönderilecek
        //            $item->delete();
        //        });

        return $cart;
    }

    /**
     * Get the user's preferred locale.
     *
     * @return string
     */
    public function preferredLocale()
    {
        return $this->locale;
    }

    public function getFullNameAttribute()
    {
        return "{$this->first_name} {$this->last_name}";
    }

    public function getFullNameWithPhoneAttribute()
    {
        return "{$this->first_name} {$this->last_name} {$this->phone}";
    }

    public function getBillFieldsAttribute()
    {
        return new BillFields([
            'firstName' => $this->first_name,
            'lastName' => $this->last_name,
            'email' => $this->email,
            'shipping_address' => [],
            'billing_address' => [],
            'mobileNumber' => $this->phone ?? '000',
            'identity_number' => $this->tckn ?? '000',
            'shipping_address' => new Address([
                'city' => $this->address?->city ?? 'x',
                'country' => $this->address?->country ?? 'x',
                'address' => $this->address?->address ?? 'x'
            ]),
            'billing_address' => new Address([
                'city' => $this->address?->city ?? 'x',
                'country' => $this->address?->country ?? 'x',
                'address' => $this->address?->address ?? 'x'
            ]),
        ]);
    }

    public function getIsVerifiedAttribute()
    {
        return $this->hasVerifiedEmail() || $this->hasVerifiedPhone();
    }

    public function hasVerifiedPhone()
    {
        return !is_null($this->phone_verified_at);
    }

    public function isAdmin()
    {
        return $this->hasRole(Role::ADMIN);
    }

    public function isCustomer()
    {
        return $this->hasRole(Role::CUSTOMER);
    }

    public function viewPanel()
    {
        return $this->hasRole(Role::ADMIN);
    }

    public function getIsBlockedAttribute()
    {
        return !empty($this->blocked_at);
    }

    public function parasutAccountId(): AttributeCast
    {
        return AttributeCast::make(
            get: fn() => $this->meta()->where('key', 'parasut_customer_pid')->first()?->value,
        );
    }

    protected function blockedAt(): AttributeCast
    {
        return AttributeCast::make(
            get: fn($value) => !($value == null),
            set: fn($value) => $value ? now() : null,
        );
    }

    public function getBirthDateAttribute()
    {
        return $this->date_of_birth?->format('Y-m-d') ?? null;
    }

    public function moveGuestCartToActiveCart()
    {
        // misafir müşteri sepeti boş ise o zaman işlem yapma
        $cart = request()->user()->cart;

        // if user has no cart, then create one
        $loggedInUserCart = $this->cart;
        if ($loggedInUserCart == null) {
            $loggedInUserCart = $this->cart()->create([
                'user_id' => (string)$this->id
            ]);
        }

        // if user has no cart, then pass the process
        if ($loggedInUserCart != null && $cart)
            $cart->items()->update(['cart_id' => $loggedInUserCart->id]);
    }


    public function meta()
    {
        return $this->morphMany(Meta::class, 'metaable');
    }

    /**
     * Kullanıcının skorlama sonuçları
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function scoringResults()
    {
        return $this->morphMany(ScoringResult::class, 'scorable');
    }

    public function resetPassword(array $data)
    {
        if ($data['password'] !== $data['password_confirmation']) {
            throw new \Exception('Şifreler uyuşmuyor');
        }
        $this->password = bcrypt($data['password']);
        $this->is_password_migrated = true;
        $this->save();

        $this->meta()->create([
            'key' => 'mhz_password_reset',
            'value' => auth()->id(),
        ]);
    }

    //    protected function fullName() : AttributeCast
    //    {
    //        return  AttributeCast::make(
    //            //get: fn ($value) => $value->first_name . ' ' . $value->last_name,
    //        );
    //    }

    //    public function payTransaction(OrderTransaction $orderTransaction)
    //    {
    //        dd($orderTransaction->id);
    //    }

    public function cleanPhoneNumber(): string
    {
        $no = Str::of($this->phone)->remove('(')->remove(')')->remove('+')->remove(' ')->remove('-');
        if ($no->startsWith(5))
            $no = $no->prepend('+90');

        if ($no->startsWith(0))
            $no = $no->prepend('+9');
        return $no;
    }

    public function getCleanPhoneNumberAttribute(): string
    {
        return $this->cleanPhoneNumber();
    }

    public function createParasutAccount(Order $order): void
    {
        sendContractToParasut::dispatchSync($order);
    }
}
