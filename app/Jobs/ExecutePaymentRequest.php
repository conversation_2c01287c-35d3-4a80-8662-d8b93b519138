<?php

namespace App\Jobs;

use App\Models\CreditCard;
use App\Models\OrderTransaction;
use App\Models\PaymentTransaction;
use App\StorableClasses\Plan;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ExecutePaymentRequest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private OrderTransaction $orderTransaction;
    private ?CreditCard $creditCard;

    public int $tries = 5;
    public int|float $backoff = 5 * 60; // 5 minutes wait if failed, then try again

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(OrderTransaction $orderTransaction)
    {
        $this->orderTransaction = $orderTransaction;
        $this->creditCard = $this->orderTransaction->order->user->creditCards()->orderBy('id')->first();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //        $ots = OrderTransaction::where('payment_status_id', 2)
        //            ->whereIn('order_id', [134, 135])
        //            ->get();
        //
        //        foreach ($ots as $ot) {
        //            foreach ($ot->order->user->creditCards as $cc) {
        //                $this->payTransaction($ot, $cc);
        //            }
        //        }
        // $this->postSlack([
        //     "text" => "### Ödeme Çekimi Çoklu KK Denemesi Başladı ###"
        // ]);

        // Ödeme alınmışsa işlem yapma // 1: Tahsil Edildi, 7: Sigorta / Ödendi
        if (in_array($this->orderTransaction->payment_status_id, [1, 7])) {
            payment_logger('Payment already received. OrderTransaction: ' . $this->orderTransaction->id);
            return;
        }

        // Özellikle kurumsal müşterilerin hesaplarında KK tanımlı olmadığından job fail oluyor. Bu durumda işlem yapma
        if (!$this->creditCard) {
            payment_logger('Credit card not found. OrderTransaction: ' . $this->orderTransaction->id);
            return;
        }

        do {
            // $this->postSlack([
            //     "text" => "Ödeme Çekimi Çoklu KK Denemesi " . $this->orderTransaction->id . " - " . $this->creditCard->id . " - " . $this->creditCard->number
            // ]);
            payment_logger('Payment request started. OrderTransaction: ' . $this->orderTransaction->id . ' CreditCard: ' . $this->creditCard->id . ' ' . $this->creditCard->number);
            try {
                $this->payTransaction($this->orderTransaction, $this->creditCard);
                $this->creditCard = null; // Ödeme başarılı ise döngüden çık
            } catch (Exception $e) {
                // $this->postSlack([
                //     "text" => "Ödeme Çekimi Çoklu KK Denemesi " . $this->orderTransaction->id . " - " . $this->creditCard->id . " - " . $this->creditCard->number . " Hata: " . $e->getMessage()
                // ]);
                payment_logger('Payment request failed. OrderTransaction: ' . $this->orderTransaction->id . ' CreditCard: ' . $this->creditCard->id . ' ' . $this->creditCard->number . ' Error: ' . $e->getMessage());
                $this->creditCard = $this->creditCard->getNextCard();
            }
        } while ($this->creditCard);
    }

    public function payTransaction(OrderTransaction $orderTransaction, CreditCard $creditCard)
    {
        // $this->postSlack([
        //     "text" => "IYZICO Ödeme Çekimi Başladı"
        // ]);
        payment_logger('One of OrderTransaction: ' . $orderTransaction->id . ' CreditCard: ' . $creditCard->id . ' ' . $creditCard->number);

        $order = $orderTransaction->order;
        $plan = new Plan();
        $plan->name($order->user->fullName . "/" . $order->order_number . ' TK' . $orderTransaction->id)
            ->price($orderTransaction->amount)
            ->attributes($order->only(['id', 'user_id', 'coupon_id', 'order_number']));

        try {
            // Ödeme işlemi
            $transaction = $this->orderTransaction->order->user->pay(collect([$plan]), $creditCard, $plan->currency, 1, true);
            $transaction->order_id = $order->id;
            $transaction->save();

            // Ödeme durumu güncelleme
            $orderTransaction->payment_status_id = $orderTransaction->getPaidStatusId(); // Ödendi
            $orderTransaction->last_payment_check = now();
            $orderTransaction->save();
            $orderTransaction->bank_last_message = '';
            $orderTransaction->note = 'Son 4 hanesi ' . $creditCard->number . ' olan karttan iyzico üzerinden ödeme alındı.';
            $orderTransaction->card_id = $creditCard->id;
            $orderTransaction->save();

            $pt = new PaymentTransaction();
            $pt->amount = $orderTransaction->amount;
            $pt->order_id = $orderTransaction->order_id;
            $pt->order_transaction_id = $orderTransaction->id;
            $pt->payment_status_id = $orderTransaction->payment_status_id; // Same as orderTransaction
            $pt->card_id = $creditCard->id;
            $pt->payment_type = 'IYZICO';
            $pt->bank_last_message = '';
            $pt->save();

            // Parasut ödeme gönderimi
            sendPaymentToParasut::dispatch($orderTransaction);
        } catch (Exception $e) {
            $orderTransaction->bank_last_message = $e->getMessage();
            $orderTransaction->last_payment_check = now();
            $orderTransaction->save();

            $pt = new PaymentTransaction();
            $pt->amount = $orderTransaction->amount;
            $pt->order_id = $orderTransaction->order_id;
            $pt->order_transaction_id = $orderTransaction->id;
            $pt->payment_status_id = 3;
            $pt->card_id = $creditCard->id;;
            $pt->payment_type = 'IYZICO';
            $pt->bank_last_message = $e->getMessage();
            $pt->save();

            throw new Exception($e->getMessage());
        }
    }

    private function postSlack($data)
    {
        return;
        $text = $data['text'];
        // Slack Kapital Payment CHANNEL
        $res = \Illuminate\Support\Facades\Http::post('*********************************************************************************', ['text' => $text])->body();

        if ($res == 'ok') {
            //$this->info('Slack mesajı başarıyla gönderildi.');
        } else {
            //$this->error('Slack mesajı gönderilemedi.');
        }
    }
}
