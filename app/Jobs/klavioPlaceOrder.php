<?php

namespace App\Jobs;

use App\Models\Order\Order;
use App\Models\User;
use EonVisualMedia\LaravelKlaviyo\Klaviyo;
use EonVisualMedia\LaravelKlaviyo\TrackEvent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class klavioPlaceOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 5;
    public int|float $backoff = 5 * 60; // 5 minutes wait if failed, then try again
    private User $user;
    private Order $order;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(User $user, Order $order)
    {
        $this->user = $user;
        $this->order = $order;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $orderItems = [];
        $this->order->items->map(function ($orderItem) use (&$orderItems) {
            $v = $orderItem->product;
            $p = $v->product;
            $t = [
                "ProductID" => $p->id,
                "SKU" => $v->sku,
                "ProductName" => $p->getName,
                "Quantity" => $orderItem->quantity,
                "ItemPrice" => $orderItem->price,
                "RowTotal" => $orderItem->total,
                "ProductURL" => env('FRONTEND_URL') . '/' . $p->defaultUrl->slug,
                "ImageURL" => $p->firstPhotoUrl,
                //"Categories" => ["Fiction", "Children"],
                "Brand" => $p->brand->name
            ];
            $orderItems[] = $t;
        });

        Klaviyo::track(TrackEvent::make(
            'Placed Order',
            [
                '$event_id' => $this->order->id . '_' . time(),
                '$value' => $this->order->total,
                'OrderId' => $this->order->order_number,
                'Categories' => ['Cat1'],
                'ItemNames' => $this->order->items->map(fn($x) => $x->product->product->getName)->toArray(),
                'Brands' => $this->order->items->map(fn($x) => $x->product->product->brand->name)->toArray(),
                'Items' => $orderItems
            ],
            [
                '$email' => $this->user->email,
                '$first_name' => $this->user->first_name,
                '$last_name' => $this->user->last_name,
            ],
            $this->order->created_at
        ));
    }

    private function oldValueJson()
    {
//        {
//            "ItemNames": [
//            "Apple AirPods 3. Nesil"
//        ],
//    "ProductNames": [
//            "Apple AirPods 3. Nesil Bluetooth Kulak İçi Kulaklık Ve Şarj Kutusu"
//        ],
//    "IsDiscounted": false,
//    "UsedCoupon": false,
//    "ItemCategories": [
//            "Kulaklık",
//            "Giyilebilir Cihaz",
//            "Aksesuar"
//        ],
//    "$currency_code": "TRY",
//    "$event_id": "142759",
//    "$value": 180,
//    "$extra": {
//            "OrderId": 142759,
//        "OrderNumber": "142759",
//        "TotalShipping": 0,
//        "TotalTax": 27.46,
//        "TotalDiscount": 0,
//        "Items": [
//            {
//                "ProductId": 67549,
//                "SKU": "3124-1",
//                "Quantity": 1,
//                "LineTotalTax": 27.46,
//                "LineSubTotal": 152.54,
//                "LineTotal": 152.54,
//                "Name": "Apple AirPods 3. Nesil",
//                "Variant": {
//                "VariantId": 67553,
//                    "SKU": "3124-1",
//                    "Attributes": [
//                        {
//                            "Name": "Minimum Kiralama Süresi",
//                            "Value": "12+ ay"
//                        }
//                    ],
//                    "Image": {
//                    "URL": "https://kiralabunu.com/wp-content/uploads/2021/11/AirPods-3.nesil-3-1.jpg"
//                    }
//                },
//                "Price": 152.54,
//                "ProductName": "Apple AirPods 3. Nesil Bluetooth Kulak İçi Kulaklık Ve Şarj Kutusu",
//                "Categories": [
//                "Kulaklık",
//                "Aksesuar",
//                "Giyilebilir Cihaz"
//            ],
//                "Metadata": [
//                    {
//                        "Name": "pa_minimum-kiralama-suresi",
//                        "Value": "12-ay"
//                    },
//                    {
//                        "Name": "pa_renk",
//                        "Value": "beyaz"
//                    }
//                ],
//                "$extra": {
//                "URL": "https://kiralabunu.com/urunler/giyilebilir-cihaz/apple-airpods-3-nesil",
//                    "Images": [
//                        {
//                            "URL": "https://kiralabunu.com/wp-content/uploads/2021/11/AirPods-3.nesil-3-1.jpg"
//                        },
//                        {
//                            "URL": "https://kiralabunu.com/wp-content/uploads/2021/11/AirPods-3.nesil-2-1.jpg"
//                        },
//                        {
//                            "URL": "https://kiralabunu.com/wp-content/uploads/2021/11/AirPods-3.nesil-1-1.jpg"
//                        }
//                    ]
//                },
//                "AttributeMinimum kiralama süresi": [
//                "1+ ay",
//                "3+ ay",
//                "6+ ay",
//                "12+ ay",
//                "18+ ay"
//            ],
//                "AttributeRenk": [
//                "Beyaz"
//            ],
//                "AttributeMarka": [
//                "Apple"
//            ]
//            }
//        ],
//        "currency": "TRY",
//        "BillingAddress": {
//                "FirstName": "Fatma",
//            "LastName": "Söylemez",
//            "Address1": "Borazanlar Mh Turgut Özal Bulvarı No:96 Akkaya Apartmani Kat:3 Daire:11",
//            "Address2": "",
//            "City": "Bolu Merkez /",
//            "State": "TR14",
//            "Country": "TR",
//            "PostCode": ""
//        },
//        "ShippingAddress": {
//                "FirstName": "Fatma",
//            "LastName": "Söylemez",
//            "Address1": "Borazanlar Mh Turgut Özal Bulvarı No:96 Akkaya Apartmani Kat:3 Daire:11",
//            "Address2": "",
//            "City": "Bolu Merkez /",
//            "State": "TR14",
//            "Country": "TR",
//            "PostCode": ""
//        }
//    }
//}
    }
}
