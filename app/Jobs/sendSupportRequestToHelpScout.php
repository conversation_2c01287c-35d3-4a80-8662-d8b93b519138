<?php

namespace App\Jobs;

use App\Models\SupportRequest;
use HelpScout\Api\ApiClientFactory;
use HelpScout\Api\Conversations\Conversation;
use HelpScout\Api\Conversations\Threads\CustomerThread;
use HelpScout\Api\Customers\Customer;
use HelpScout\Api\Tags\Tag;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class sendSupportRequestToHelpScout implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 5;
    public int|float $backoff = 5 * 60; // 5 minutes wait if failed, then try again

    private SupportRequest $supportRequest;

    /**
     * Create a new job instance.
     *
     * @param SupportRequest $supportRequest
     */
    public function __construct(SupportRequest $supportRequest)
    {
        $this->supportRequest = $supportRequest;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // if required to get tokens use this url
        // https://secure.helpscout.net/authentication/authorizeClientApplication?client_id=f2XFwEbbXZoG5KW6f3F5VwNCKMBuz1u0&state=T5mTqLn0LfTmQFBP3eSjXcL9xkmnTan8#
        $client = ApiClientFactory::createClient();

        // Set Client credentials if using that grant type.  Using this approach will lazily fetch an access token on-demand
        // when needed.
        $client->useClientCredentials('f2XFwEbbXZoG5KW6f3F5VwNCKMBuz1u0', 'T5mTqLn0LfTmQFBP3eSjXcL9xkmnTan8');

        $user = $this->supportRequest->user;

        $customer = new Customer();
        $customer->addEmail($user->email);
        $customer->setFirstName($user->first_name);
        $customer->setLastName($user->last_name);

        $thread = new CustomerThread();
        $thread->setCustomer($customer);
        $thread->setText("Sipariş No: {$this->supportRequest->order->order_number}<br/>  Destek Tipi: {$this->supportRequest->supportRequestType->name}<br/> Müşteri Notu: {$this->supportRequest->customer_message}");

        $conversation = new Conversation();
        $conversation->setSubject('KB Site Destek Talebi #' . $this->supportRequest->id);
        $conversation->setStatus('active');
        $conversation->setType('email');
        $conversation->setMailboxId(236132);
        $conversation->setCustomer($customer);
        $conversation->setThreads(new \HelpScout\Api\Entity\Collection([
            $thread,
        ]));

        // You can optionally add tags
        $tag = new Tag();
        $tag->setName('kb-destek-talebi');
        $conversation->addTag($tag);

        $tag = new Tag();
        $tag->setName(Str::of($this->supportRequest->supportRequestType->name)->kebab()->toString());
        $conversation->addTag($tag);

        try {
            $conversationId = $client->conversations()->create($conversation);
            $hsResponse = $client->conversations()->get($conversationId);
            $this->supportRequest->update([
                'helpscout_conversation_id' => $conversationId,
                'helpscout_conversation_number' => $hsResponse->getNumber(),
            ]);
        } catch (ValidationErrorException $e) {
            //var_dump($e->getError()->getErrors());
            var_dump($e->getError());
        }
    }
}
