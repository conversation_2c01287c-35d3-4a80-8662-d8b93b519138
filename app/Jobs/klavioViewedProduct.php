<?php

namespace App\Jobs;

use App\Models\Lunar\Product;
use App\Models\User;
use EonVisualMedia\LaravelKlaviyo\Klaviyo;
use EonVisualMedia\LaravelKlaviyo\TrackEvent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class klavioViewedProduct implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 5;
    public int|float $backoff = 5 * 60; // 5 minutes wait if failed, then try again
    private User $user;
    private Product $product;
    private \Illuminate\Support\Carbon $timestamp;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(User $user, Product $product)
    {
        $this->user = $user;
        $this->product = $product;
        $this->timestamp = now();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Klaviyo::track(TrackEvent::make(
            'Viewed Product',
            [
                'CurrencySymbol' => '₺',
                'Currency' => 'TRY',
                'Title' => $this->product->name,
                'ItemId' => $this->product->id,
                'Categories' => $this->product->collections->map(fn($y) => json_decode($y->attribute_data)->name->value->tr)->flatten()->unique()->toArray(),
                'ImageUrl' => $this->product->coverImage()['thumb_webp'],
                'Url' => config('app.app_frontend_url') . '/urun/' . $this->product->defaultUrl->slug,
                'Metadata' => [
                    'Price' => $this->product->variants->whereNull('deleted_at')->first()->prices->whereNull('deleted_at')->filter(fn($x) => $x->subscription_months_id == $x->whereNull('deleted_at')->where('priceable_id', $x->product->id)->where('price', '>', 0)->max('subscription_months_id'))->first()->price->value / 100,
                ],
                '$is_session_activity' => true,
                '_ip' => request()->getClientIp(),
                '$event_id' => time(),
            ],
            [
                '$email' => $this->user->email,
                '$first_name' => $this->user->first_name,
                '$last_name' => $this->user->last_name,
            ],
            $this->timestamp
        ));
    }
}
