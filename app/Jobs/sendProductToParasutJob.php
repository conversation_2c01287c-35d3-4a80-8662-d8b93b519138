<?php

namespace App\Jobs;

use App\Models\ProductStock;
use App\Services\Parasut\Parasut;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class sendProductToParasutJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    public $tries = 5;
    public int|float $backoff = 5 * 60; // 5 minutes wait if failed, then try again

    private ?string $token;
    private ProductStock $productStock;
    private string $appCode;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(ProductStock $productStock)
    {
        $this->token = Parasut::getToken();
        $this->productStock = $productStock;
        $this->appCode = config('app.app_short_name') . 'MP';
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if ($this->batch()->cancelled()) {
            // Determine if the batch has been cancelled...
            return;
        }

        $parasut_waybill_pid = $this->productStock->meta()->where('key', 'parasut_waybill_pid')->first();
        if ($parasut_waybill_pid) {
            return; //$parasut_waybill_pid->value;
        }

        $res = $this->sendService('waybill')->object();
        $this->productStock->meta()->firstOrCreate(['key' => 'parasut_waybill_pid'], ['value' => $res->data->id]);

        $res = $this->sendService('rent')->object();
        $this->productStock->meta()->firstOrCreate(['key' => 'parasut_renting_pid'], ['value' => $res->data->id]);
    }

    private function sendService($productType)
    {
        // Product Stock is variant of product so we need to get product
        $product = $this->productStock->product->product;

        $res = Http::withToken($this->token)
            ->post('https://api.parasut.com/v4/402231/products', [
                'data' => [
                    //"id" => Str::of($product->id)->padLeft(7, 0)->prepend($this->appCode . $this->getProductTypeCode($productType)), // bu alan parasut tarafında oluşturuluyor
                    "type" => "products",
                    "attributes" => [
                        "code" => Str::of($product->id)->padLeft(7, 0)->prepend($this->appCode . $this->getProductTypeCode($productType)),
                        "name" => $this->calculateName($product, $productType),
                        "vat_rate" => 20,
                        "unit" => "Adet",
                        "communications_tax_rate" => 0,
                        "archived" => false,
                        "list_price" => 1,
                        "currency" => "TRL",
                        "buying_price" => 0,
                        "buying_currency" => "TRL",
                        "inventory_tracking" => true,
                        "initial_stock_count" => 0,
                        "barcode" => Str::of($product->id . '00' . $this->productStock->id)->padLeft(10, 0)->prepend($this->getBarcodePrependByProductTypeCode($productType)),
                    ],
                    "relationships" => [
                        "category" => [
                            "data" => [
                                "id" => $this->getCategory($productType),
                                "type" => "item_categories"
                            ]
                        ]
                    ],
                ]
            ]);

        logger()->channel('parasut')->info('sendProductToParasutJob ' . $product->id . ' Barkod ' . Str::of($product->id)->padLeft(10, 0)->prepend($this->getBarcodePrependByProductTypeCode($productType)) . ' Code ' . Str::of($product->id)->padLeft(7, 0)->prepend($this->appCode . $this->getProductTypeCode($productType)) . ' Ürün Adı ' . $this->calculateName($product, $productType), $res->json());
        return $res;
    }

    private function getBarcodePrependByProductTypeCode($productType)
    {
        return match (true) {
            config('app.app_short_name') == 'KB' && $productType == 'waybill' => 5,
            config('app.app_short_name') == 'KB' && $productType == 'rent' => 6,
            config('app.app_short_name') == 'KM' && $productType == 'waybill' => 7,
            config('app.app_short_name') == 'KM' && $productType == 'rent' => 8,
        };
    }

    private function getProductTypeCode($productType)
    {
        return match ($productType) {
            'waybill' => 'W',
            'rent' => 'R',
        };
    }

    private function calculateName($product, $productType)
    {
        return match ($productType) {
            'waybill' => $product->getName . ' SN: ' . $this->productStock->sn,
            'rent' => $product->getName . ' ' . $this->productStock->product->getVaariant . ' Operasyonel Kiralama Bedeli',
        };
    }

    private function getCategory($productType)
    {
        return match (true) {
            config('app.app_short_name') == 'KB' && $productType == 'waybill' => 8629084,
            config('app.app_short_name') == 'KB' && $productType == 'rent' => 8629086,
            config('app.app_short_name') == 'KM' && $productType == 'waybill' => 8629085,
            config('app.app_short_name') == 'KM' && $productType == 'rent' => 8629087,
        };
    }
}
