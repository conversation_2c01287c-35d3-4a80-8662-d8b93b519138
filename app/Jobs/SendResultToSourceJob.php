<?php

namespace App\Jobs;

use App\Models\ScoringRequest;
use App\States\ScoringRequest\WebhookSentApprovedState;
use App\States\ScoringRequest\WebhookSentRejectedState;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SendResultToSourceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $scoringRequestId;
    protected ScoringRequest $scoringRequest;

    /**
     * Job retry sayısı
     */
    public int $tries = 3;

    /**
     * Job timeout süresi (saniye)
     */
    public int $timeout = 30;

    /**
     * Create a new job instance.
     */
    public function __construct(int $scoringRequestId)
    {
        $this->scoringRequestId = $scoringRequestId;
        $this->scoringRequest = ScoringRequest::find($scoringRequestId);

        // Yüksek öncelikli queue'ya ata
        $this->onQueue('scoring-high-priority');
    }

    /**
     * Get the scoring request ID
     */
    public function getScoringRequestId(): int
    {
        return $this->scoringRequestId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Scoring request'i bul
            $scoringRequest = ScoringRequest::with(['scoringSource', 'scoringResult'])
                ->find($this->scoringRequestId);

            if (!$scoringRequest) {
                Log::error('SendResultToSourceJob: Scoring request bulunamadı', [
                    'scoring_request_id' => $this->scoringRequestId
                ]);

                $this->scoringRequest->notes()->create([
                    'content' => 'Skorlama sonucu kaynağa gönderilemedi. Scoring request bulunamadı.',
                ]);

                return;
            }

            if (!$scoringRequest->scoringResult) {
                Log::error('SendResultToSourceJob: Scoring result bulunamadı', [
                    'scoring_request_id' => $this->scoringRequestId,
                    'ulid' => $scoringRequest->ulid
                ]);

                $this->scoringRequest->notes()->create([
                    'content' => 'Skorlama sonucu kaynağa gönderilemedi. Scoring result bulunamadı.',
                ]);

                return;
            }

            $source = $scoringRequest->scoringSource;

            if (!$source || !$source->is_active) {
                Log::warning('SendResultToSourceJob: Inaktif kaynak, webhook gönderilmiyor', [
                    'scoring_request_id' => $this->scoringRequestId,
                    'ulid' => $scoringRequest->ulid,
                    'source_id' => $source?->id,
                    'source_active' => $source?->is_active
                ]);

                $scoringRequest->notes()->create([
                    'content' => 'Skorlama sonucu kaynağa gönderilemedi. Kaynak aktif değil.',
                    'editor_id' => auth()->id() ?? null
                ]);

                return;
            }

            if (!$source->webhook_url) {
                Log::warning('SendResultToSourceJob: Webhook URL tanımlı değil', [
                    'scoring_request_id' => $this->scoringRequestId,
                    'ulid' => $scoringRequest->ulid,
                    'source_id' => $source->id
                ]);

                return;
            }

            // Webhook URL'ini temizle (boşlukları kaldır)
            $webhookUrl = trim($source->webhook_url);

            // Webhook payload'unu hazırla
            $payload = $this->prepareWebhookPayload($scoringRequest);

            Log::info('SendResultToSourceJob: Webhook gönderiliyor', [
                'scoring_request_id' => $this->scoringRequestId,
                'ulid' => $scoringRequest->ulid,
                'webhook_url' => $webhookUrl,
                'original_webhook_url' => $source->webhook_url,
                'payload' => $payload
            ]);

            // HTTP client ayarları
            $headers = [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'User-Agent' => 'KB-Scoring-System/1.0',
            ];

            // API key varsa ekle
            if ($source->api_key) {
                $headers['Authorization'] = 'Bearer ' . $source->api_key;
            }

            // Webhook'u gönder
            $response = Http::timeout(25)
                ->retry(2, 1000) // 2 defa dene, 1 saniye bekle
                ->withHeaders($headers)
                ->post($webhookUrl, $payload);

            if ($response->successful()) {
                // Başarılı gönderim
                $this->handleSuccessfulWebhook($scoringRequest, $response);
            } else {
                // Başarısız gönderim
                $this->handleFailedWebhook($scoringRequest, $response);
            }
        } catch (\Exception $e) {
            Log::error('SendResultToSourceJob: Webhook gönderiminde hata', [
                'scoring_request_id' => $this->scoringRequestId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Job'ı yeniden deneyebilmek için exception'ı fırlat
            throw $e;
        }
    }

    /**
     * Webhook payload'unu hazırla
     */
    private function prepareWebhookPayload(ScoringRequest $scoringRequest): array
    {
        $result = $scoringRequest->scoringResult;

        return [
            'ulid' => $scoringRequest->ulid,
            'status' => $result->is_approved ? 'approved' : 'rejected',
            'score' => $result->score,
            'approved_amount' => $result->approved_amount,
            'requested_amount' => $scoringRequest->requested_amount,
            'customer' => [
                'full_name' => $scoringRequest->full_name,
                'email' => $scoringRequest->email,
                'tckn' => $scoringRequest->tckn,
            ],
            'processed_at' => $result->processed_at->toISOString(),
            'webhook_sent_at' => now()->toISOString(),
            'additional_data' => $scoringRequest->additional_data,
        ];
    }

    /**
     * Başarılı webhook sonrası işlemler
     */
    private function handleSuccessfulWebhook(ScoringRequest $scoringRequest, $response): void
    {
        // webhook_sent_at'i güncelle
        $scoringRequest->scoringResult->update([
            'webhook_sent_at' => now()
        ]);

        $status = $scoringRequest->status;

        // State'i doğru webhook sent state'e geçir
        if ($scoringRequest->scoringResult->is_approved) {
            $scoringRequest->status->transitionTo(WebhookSentApprovedState::class);
        } else {
            $scoringRequest->status->transitionTo(WebhookSentRejectedState::class);
        }

        Log::info('SendResultToSourceJob: Webhook başarıyla gönderildi', [
            'scoring_request_id' => $this->scoringRequestId,
            'ulid' => $scoringRequest->ulid,
            'response_status' => $response->status(),
            'response_body' => $response->body()
        ]);

        // Not oluştur
        $scoringRequest->notes()->create([
            'content' => 'Skorlama sonucu kaynağa başarıyla gönderildi. Onay Durumu: ' . $status . ' - Kaynak: ' . $scoringRequest->scoringSource?->name,
            'editor_id' => auth()->id() ?? null
        ]);
    }

    /**
     * Başarısız webhook sonrası işlemler
     */
    private function handleFailedWebhook(ScoringRequest $scoringRequest, $response): void
    {
        Log::warning('SendResultToSourceJob: Webhook gönderimi başarısız', [
            'scoring_request_id' => $this->scoringRequestId,
            'ulid' => $scoringRequest->ulid,
            'response_status' => $response->status(),
            'response_body' => $response->body()
        ]);

        // HTTP 4xx hatalarında retry yapma
        if ($response->status() >= 400 && $response->status() < 500) {
            Log::warning('SendResultToSourceJob: 4xx hatası, retry yapılmıyor', [
                'scoring_request_id' => $this->scoringRequestId,
                'response_status' => $response->status()
            ]);
            return;
        }

        $scoringRequest->notes()->create([
            'content' => 'Skorlama sonucu kaynağa gönderilemedi. Hata Kodu: ' . $response->status() . ' - Hata Mesajı: ' . substr($response->body(), 0, 100),
            'editor_id' => auth()->id() ?? null
        ]);

        // 5xx hatalarında job'ı yeniden dene
        throw new \Exception("Webhook failed with status: " . $response->status());
    }

    /**
     * Job failed olduğunda çalışır
     */
    public function failed(\Throwable $exception): void
    {
        try {
            $scoringRequest = ScoringRequest::find($this->scoringRequestId);

            if ($scoringRequest) {
                Log::error('SendResultToSourceJob: Job tamamen başarısız oldu', [
                    'scoring_request_id' => $this->scoringRequestId,
                    'ulid' => $scoringRequest->ulid,
                    'error' => $exception->getMessage(),
                    'attempts' => $this->attempts()
                ]);

                // Not oluştur
                $scoringRequest->notes()->create([
                    'content' => 'Skorlama sonucu kaynağa gönderilemedi. Hata: ' . $exception->getMessage() . ' - Kaynak: ' . $scoringRequest->scoringSource?->name,
                ]);

                // Additional data'ya hata bilgisini ekle
                $additionalData = $scoringRequest->additional_data ?? [];
                $additionalData['webhook_failed'] = [
                    'error' => $exception->getMessage(),
                    'failed_at' => now()->toISOString(),
                    'attempts' => $this->attempts()
                ];

                $scoringRequest->update(['additional_data' => $additionalData]);
            }
        } catch (\Exception $e) {
            Log::error('SendResultToSourceJob: Failed method\'da da hata oluştu', [
                'error' => $e->getMessage()
            ]);
        }
    }
}
