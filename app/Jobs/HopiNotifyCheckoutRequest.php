<?php

namespace App\Jobs;

use App\Models\Order\Order;
use App\Services\Hopi\Hopi;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;

class HopiNotifyCheckoutRequest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private int $birdId;
    private Order $order;
    private float $usedHopiBalance;
    private ?string $campaign;

    /**
     * Create a new job instance.
     *
     * @param int $birdId
     * @param Order $order
     * @param float $usedHopiBalance
     * @param string $campaign
     */
    public function __construct(int $birdId, Order $order, float $usedHopiBalance, ?string $campaign)
    {
        $this->birdId = $birdId;
        $this->order = $order;
        $this->usedHopiBalance = $usedHopiBalance;
        $this->campaign = $campaign;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $hopi = new Hopi();
        hopi_logger('HopiNotifyCheckoutRequest', [$this->order->id]);
        $provisionID = \App\Models\Order\Order::find($this->order->id)->meta()->where('key', 'hopi_provision_id')->first()?->provisionId;
        $res = $hopi->NotifyCheckoutRequest(
            birdId: $this->birdId,
            purchaseDate: $this->order->created_at->toDateString() . 'T' . $this->order->created_at->toTimeString(),
            hopiTxID: $provisionID,
            firstMonthAmount: $this->order->orderTransactions->first()->amount,
            usedHopiBalance: $this->usedHopiBalance,
            orderNumber: $this->order->order_number,
            selectedCampaign: $this->campaign,
            productList: $this->order->orderItems->map(function ($item) {
                return [
                    'barcode' => 'PID' . $item->product_id,
                    'quantity' => $item->quantity,
                    'amount' => $item->price,
                ];
            }),
        );
        Cache::set('HopiNotifyCheckoutRequest_' . $this->order->id, $res, now()->addDays(30));
        hopi_logger('HopiNotifyCheckoutRequest', [$res]);
        return $res;
    }
}
