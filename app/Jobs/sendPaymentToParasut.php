<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use App\Models\OrderTransaction;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;

class sendPaymentToParasut implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public OrderTransaction $orderTransaction) {}

    public function handle()
    {
        // Rate limit kontrolü
        $rateLimitUntil = Cache::get('parasut_rate_limit_until');
        if ($rateLimitUntil && $rateLimitUntil > now()->timestamp) {
            $waitSeconds = $rateLimitUntil - now()->timestamp;
            // Job'ı belirtilen süre sonra tekrar çalıştır
            $this->release($waitSeconds);
            return;
        }
        
        try {
            // chcek if customer has not parasut account
            if (! $this->isParasutAccountExists()) {
                // create parasut account
                $this->orderTransaction->order->user->createParasutAccount($this->orderTransaction->order);
            }

            // send payment to parasut
            $this->orderTransaction->sendPaymentToParasut();
        } catch (\Exception $e) {
            // Rate limit exception kontrolü
            if (str_starts_with($e->getMessage(), 'RATE_LIMIT:')) {
                $waitSeconds = (int) str_replace('RATE_LIMIT:', '', $e->getMessage());
                // Job'ı belirtilen süre sonra tekrar çalıştır
                $this->release($waitSeconds);
                return;
            }
            
            // Diğer hatalar için exception'ı tekrar fırlat
            throw $e;
        }
    }

    // is parasut account exists
    public function isParasutAccountExists(): bool
    {
        $parasutAccountId = $this->orderTransaction->order->user->parasutAccountId;
        return $parasutAccountId ? true : false;
    }
}
