<?php

namespace App\Jobs;

use App\Models\PaymentSmsLink;
use App\Models\ScoringRequest;
use App\Services\SmsService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SendPaymentSmsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $scoringRequestId;
    protected bool $forceResend = false;

    /**
     * Job retry sayısı
     */
    public int $tries;

    /**
     * Job timeout süresi (saniye)
     */
    public int $timeout = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(int $scoringRequestId, bool $forceResend = false)
    {
        $this->scoringRequestId = $scoringRequestId;
        $this->forceResend = $forceResend;

        // Config'den retry sayısını al
        $this->tries = config('app.sms_payment.retry_attempts', 3);

        // Normal öncelikli queue'ya ata (SMS acil değil)
        $this->onQueue('scoring-high-priority');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // SMS ödeme sistemi aktif mi kontrol et
            if (!config('app.sms_payment.enabled', true)) {
                Log::info('SendPaymentSmsJob: SMS ödeme sistemi devre dışı, atlanıyor', [
                    'scoring_request_id' => $this->scoringRequestId
                ]);
                
                // Not ekle - SMS sistemi devre dışı
                $scoringRequest = ScoringRequest::find($this->scoringRequestId);
                if ($scoringRequest) {
                    $scoringRequest->notes()->create([
                        'content' => 'SMS gönderimi atlandı: SMS ödeme sistemi devre dışı.'
                    ]);
                }
                return;
            }

            // Scoring request'i bul
            $scoringRequest = ScoringRequest::with(['scoringResult', 'scoringSource'])
                ->find($this->scoringRequestId);

            if (!$scoringRequest) {
                Log::error('SendPaymentSmsJob: Scoring request bulunamadı', [
                    'scoring_request_id' => $this->scoringRequestId
                ]);
                return;
            }

            // Skorlama sonucu var mı ve onaylı mı kontrol et
            $result = $scoringRequest->scoringResult;
            if (!$result || !$result->is_approved || $result->approved_amount <= 0) {
                Log::warning('SendPaymentSmsJob: Onaylanmamış talep için SMS gönderilmiyor', [
                    'scoring_request_id' => $this->scoringRequestId,
                    'ulid' => $scoringRequest->ulid,
                    'is_approved' => $result?->is_approved,
                    'approved_amount' => $result?->approved_amount
                ]);
                
                // Not ekle - Onaylanmamış talep
                $scoringRequest->notes()->create([
                    'content' => sprintf(
                        'SMS gönderimi iptal edildi: Talep onaylanmamış veya onay tutarı 0. (Onay durumu: %s, Tutar: %s)',
                        $result?->is_approved ? 'Evet' : 'Hayır',
                        $result?->approved_amount ?? '0'
                    )
                ]);
                return;
            }

            // Telefon numarasını al (additional_data'dan)
            $phoneNumber = $this->extractPhoneNumber($scoringRequest);
            if (!$phoneNumber) {
                Log::error('SendPaymentSmsJob: Telefon numarası bulunamadı', [
                    'scoring_request_id' => $this->scoringRequestId,
                    'ulid' => $scoringRequest->ulid,
                    'additional_data' => $scoringRequest->additional_data
                ]);
                
                // Not ekle - Telefon numarası bulunamadı
                $scoringRequest->notes()->create([
                    'content' => 'SMS gönderimi başarısız: Telefon numarası bulunamadı. additional_data içerisinde phone, gsm, mobile veya telephone alanlarından biri bulunmalıdır.'
                ]);
                return;
            }

            // Daha önceden SMS gönderilmiş mi kontrol et
            $existingLink = PaymentSmsLink::where('scoring_request_id', $scoringRequest->id)
                ->where('status', '!=', 'expired')
                ->latest()
                ->first();

            if ($existingLink && !$this->forceResend) {
                Log::info('SendPaymentSmsJob: Bu talep için zaten aktif SMS linki var', [
                    'scoring_request_id' => $this->scoringRequestId,
                    'ulid' => $scoringRequest->ulid,
                    'existing_link_id' => $existingLink->id,
                    'existing_status' => $existingLink->status
                ]);
                
                // Not ekle - Mevcut link var
                $scoringRequest->notes()->create([
                    'content' => sprintf(
                        'SMS gönderimi atlandı: Zaten aktif bir ödeme linki mevcut. (Link ID: %s, Durum: %s)',
                        $existingLink->id,
                        $existingLink->status
                    )
                ]);
                return;
            }

            // Eğer mevcut link varsa ve forceResend true ise onu kullan, yoksa oluştur
            if ($existingLink && $this->forceResend) {
                $paymentLink = $existingLink;
            } else {
                // Ödeme linki oluştur
                $paymentLink = $this->createPaymentLink($scoringRequest, $phoneNumber);
            }

            // SMS içeriğini hazırla
            $smsContent = $this->prepareSmsContent($scoringRequest, $paymentLink);

            Log::info('SendPaymentSmsJob: SMS gönderiliyor', [
                'scoring_request_id' => $this->scoringRequestId,
                'ulid' => $scoringRequest->ulid,
                'phone_number' => $this->maskPhoneNumber($phoneNumber),
                'payment_link_id' => $paymentLink->id,
                'expires_at' => $paymentLink->expires_at
            ]);

            // SMS'i gönder
            $smsService = app(SmsService::class);
            $smsResult = $smsService->sendSms($phoneNumber, $smsContent);

            if ($smsResult['success']) {
                // SMS başarıyla gönderildi
                $paymentLink->update([
                    'sent_at' => now(),
                    'status' => 'sent'
                ]);

                Log::info('SendPaymentSmsJob: SMS başarıyla gönderildi', [
                    'scoring_request_id' => $this->scoringRequestId,
                    'ulid' => $scoringRequest->ulid,
                    'payment_link_id' => $paymentLink->id,
                    'sms_result' => $smsResult
                ]);

                // Not oluştur
                $scoringRequest->notes()->create([
                    'content' => 'SMS ödeme linki gönderildi. Link: ' . $paymentLink->token,
                ]);
            } else {
                // SMS gönderimi başarısız
                Log::error('SendPaymentSmsJob: SMS gönderimi başarısız', [
                    'scoring_request_id' => $this->scoringRequestId,
                    'ulid' => $scoringRequest->ulid,
                    'payment_link_id' => $paymentLink->id,
                    'error' => $smsResult['error'] ?? 'Bilinmeyen hata'
                ]);
                
                // Not ekle - SMS gönderim hatası
                $scoringRequest->notes()->create([
                    'content' => 'SMS gönderimi başarısız: ' . ($smsResult['error'] ?? 'Bilinmeyen hata') . '. Yeniden denenecek.'
                ]);

                // Job'ı yeniden dene
                throw new \Exception("SMS gönderimi başarısız: " . ($smsResult['error'] ?? 'Bilinmeyen hata'));
            }
        } catch (\Exception $e) {
            Log::error('SendPaymentSmsJob: SMS gönderiminde hata', [
                'scoring_request_id' => $this->scoringRequestId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Job'ı yeniden deneyebilmek için exception'ı fırlat
            throw $e;
        }
    }

    /**
     * Telefon numarasını additional_data'dan çıkar
     */
    private function extractPhoneNumber(ScoringRequest $scoringRequest): ?string
    {
        $additionalData = $scoringRequest->additional_data;

        if (!$additionalData) {
            return null;
        }

        // Olası telefon alanları
        $possibleFields = ['phone', 'gsm', 'mobile', 'telephone', 'phone_number'];

        foreach ($possibleFields as $field) {
            if (isset($additionalData[$field]) && !empty($additionalData[$field])) {
                return $this->normalizePhoneNumber($additionalData[$field]);
            }
        }

        return null;
    }

    /**
     * Telefon numarasını normalize et
     */
    private function normalizePhoneNumber(string $phone): string
    {
        // Sadece rakamları al
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // Türkiye için +90 ekle
        if (strlen($phone) === 10 && str_starts_with($phone, '5')) {
            $phone = '90' . $phone;
        } elseif (strlen($phone) === 11 && str_starts_with($phone, '05')) {
            $phone = '9' . $phone;
        }

        return '+' . $phone;
    }

    /**
     * Ödeme linki oluştur
     */
    private function createPaymentLink(ScoringRequest $scoringRequest, string $phoneNumber): PaymentSmsLink
    {
        // Expire süresi
        $expireHours = config('app.sms_payment.link_expire_hours', 24);
        $expiresAt = now()->addHours($expireHours);

        // Unique token oluştur
        $token = Str::random(32);

        // Payment URL oluştur
        $baseUrl = config('app.sms_payment.base_url');
        $paymentUrl = $baseUrl . '/' . $token;

        return PaymentSmsLink::create([
            'scoring_request_id' => $scoringRequest->id,
            'phone_number' => $phoneNumber,
            'token' => $token,
            'expires_at' => $expiresAt,
            'status' => 'pending'
        ]);
    }

    /**
     * SMS içeriğini hazırla
     */
    private function prepareSmsContent(ScoringRequest $scoringRequest, PaymentSmsLink $paymentLink): string
    {
        $result = $scoringRequest->scoringResult;
        $customerName = $scoringRequest->full_name;
        $approvedAmount = number_format($result->approved_amount, 0, ',', '.');
        $paymentUrl = config('app.sms_payment.base_url') . '/' . $paymentLink->token;
        $expireTime = $paymentLink->expires_at->format('d.m.Y H:i');

        return "Sayın {$customerName}, {$approvedAmount} TL kiralama talebiniz onaylandı! " .
            "Ödeme işlemi için: {$paymentUrl} " .
            "Link geçerlilik süresi: {$expireTime}. " .
            "Bilgi: 0850 255 1552";
    }

    /**
     * Telefon numarasını maskele (loglarda güvenlik için)
     */
    private function maskPhoneNumber(string $phone): string
    {
        if (strlen($phone) < 4) {
            return $phone;
        }

        return substr($phone, 0, 3) . str_repeat('*', strlen($phone) - 6) . substr($phone, -3);
    }

    /**
     * Job failed olduğunda çalışır
     */
    public function failed(\Throwable $exception): void
    {
        try {
            $scoringRequest = ScoringRequest::find($this->scoringRequestId);

            if ($scoringRequest) {
                Log::error('SendPaymentSmsJob: Job tamamen başarısız oldu', [
                    'scoring_request_id' => $this->scoringRequestId,
                    'ulid' => $scoringRequest->ulid,
                    'error' => $exception->getMessage(),
                    'attempts' => $this->attempts()
                ]);

                // PaymentSmsLink varsa error durumuna geçir
                $paymentLink = PaymentSmsLink::where('scoring_request_id', $scoringRequest->id)
                    ->where('status', 'pending')
                    ->latest()
                    ->first();

                if ($paymentLink) {
                    // Status enum'da error yok, additional_data'ya hata bilgisini ekle
                    $additionalData = $scoringRequest->additional_data ?? [];
                    $additionalData['sms_payment_failed'] = [
                        'error' => $exception->getMessage(),
                        'failed_at' => now()->toISOString(),
                        'attempts' => $this->attempts(),
                        'payment_link_id' => $paymentLink->id
                    ];

                    $scoringRequest->update(['additional_data' => $additionalData]);
                    
                    // Not ekle - Job tamamen başarısız
                    $scoringRequest->notes()->create([
                        'content' => sprintf(
                            'SMS gönderimi kalıcı olarak başarısız oldu. %d deneme yapıldı. Hata: %s',
                            $this->attempts(),
                            $exception->getMessage()
                        )
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error('SendPaymentSmsJob: Failed method\'da da hata oluştu', [
                'error' => $e->getMessage()
            ]);
        }
    }
}
