<?php

namespace App\Jobs;

use App\Models\Order;
use App\Models\User;
use EonVisualMedia\LaravelKlaviyo\Klaviyo;
use EonVisualMedia\LaravelKlaviyo\TrackEvent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class klavioRejectedOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 5;
    public int|float $backoff = 5 * 60; // 5 minutes wait if failed, then try again

    private User $user;
    private Order $order;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(User $user, Order $order)
    {
        $this->user = $user;
        $this->order = $order;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $orderItems = [];
        $this->order->orderItems->map(function ($orderItem) use (&$orderItems) {
            $v = $orderItem->product;
            $p = $v->product;
            $t = [
                "ProductID" => $p->id,
                "SKU" => $v->sku,
                "ProductName" => $p->getName,
                "Quantity" => $orderItem->quantity,
                "ItemPrice" => $orderItem->price,
                "RowTotal" => $orderItem->total,
                "ProductURL" => env('FRONTEND_URL') . '/' . $p->defaultUrl->slug,
                "ImageURL" => $p->firstPhotoUrl,
                //"Categories" => ["Fiction", "Children"],
                "Brand" => $p->brand->name
            ];
            $orderItems[] = $t;
        });

        Klaviyo::track(TrackEvent::make(
            'Rejected Order',
            [
                'time' => now()->toAtomString(),
                'value' => $this->order->total,
                'value_currency' => 'TRY',
                'unique_id' => time(),
                'properties' => [
                    "CheckoutURL" => env('APP_URL') . "/admin/all-orders/" . $this->order->id . "?activeRelationManager=0",
                    //'OrderId' => $this->order->order_number,
                    'Categories' => ['Cat1'],
                    //'ItemNames' => $this->order->items->map(fn($x) => $x->product->product->getName)->toArray(),
                    //'Brands' => $this->order->items->map(fn($x) => $x->product->product->brand->name)->toArray(),
                    'Items' => $orderItems
                ],
            ],
            [
                'email' => $this->user->email,
                'first_name' => $this->user->first_name,
                'last_name' => $this->user->last_name,
            ],
            now()
        ));
    }
}
