<?php

namespace App\Jobs;

use App\Models\Cart\Cart;
use App\Models\Lunar\SubscriptionMonths;
use App\Models\User;
use EonVisualMedia\LaravelKlaviyo\Klaviyo;
use EonVisualMedia\LaravelKlaviyo\TrackEvent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class klavioStartedCheckout implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 5;
    public int|float $backoff = 5 * 60; // 5 minutes wait if failed, then try again

    private User $user;
    private \Illuminate\Support\Carbon $timestamp;
    private Cart $cart;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(User $user, Cart $cart)
    {
        $this->user = $user;
        $this->timestamp = now();
        $this->cart = $cart;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Klaviyo::track(TrackEvent::make(
            'Started Checkout',
            [
                'CurrencySymbol' => '₺',
                'Currency' => 'TRY',
                'Categories' => $this->cart->items->map(fn($x) => $x->product->product->collections->map(fn($y) => json_decode($y->attribute_data)->name->value->tr)->toArray())->flatten()->unique(),
                'ItemNames' => $this->cart->items->map(fn($x) => $x->product->product->name)->toArray(),
                '$event_id' => time(),
                '$value' => $this->cart->total,
                '$extra' => [
                    'Items' => [
                        $this->cart->items->map(fn($x) => [
                            'Quantity' => $x->quantity,
                            'ProductID' => $x->product->product->id,
                            'VariantID' => $x->product->id,
                            'Name' => $x->product->product->name,
                            'URL' => config('app.app_frontend_url') . '/urun/' . $x->product->product->defaultUrl->slug,
                            'Images' => [
                                [
                                    'URL' => $x->product->product->coverImage()['thumb_webp']
                                ]
                            ],
                            'Categories' => $x->product->product->collections->map(fn($y) => json_decode($y->attribute_data)->name?->value?->tr)->toArray(),
                            'Variation' => [
                                'month' => SubscriptionMonths::getSelectedMonth($x->month)
                            ],
                            'SubTotal' => $x->sub_total,
                            'Total' => $x->total,
                            'LineTotal' => $x->total,
                            'Tax' => $x->tax_amount,
                            'TotalWithTax' => $x->total
                        ])->toArray(),
                    ],
                    'SubTotal' => $this->cart->sub_total,
                    'ShippingTotal' => 0,
                    'TaxTotal' => $this->cart->total,
                    'GrandTotal' => $this->cart->total,
                    'CartRebuildKey' => Str::random(50),
                ],
            ],
            [
                '$email' => $this->user->email,
                '$first_name' => $this->user->first_name,
                '$last_name' => $this->user->last_name,
            ],
            $this->timestamp
        ));
    }
}
