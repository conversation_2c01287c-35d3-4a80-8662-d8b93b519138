<?php

namespace App\Jobs;

use App\Models\Order;
use App\Models\User;
use EonVisualMedia\LaravelKlaviyo\Klaviyo;
use EonVisualMedia\LaravelKlaviyo\TrackEvent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class klavioApprovedOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 5;
    public int|float $backoff = 5 * 60; // 5 minutes wait if failed, then try again

    private User $user;
    private Order $order;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(User $user, Order $order)
    {
        $this->user = $user;
        $this->order = $order;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $orderItems = [];
        $this->order->orderItems->map(function ($orderItem) use (&$orderItems) {
            $v = $orderItem->product;
            $p = $v->product;
            $t = [
                "ProductID" => $p->id,
                "SKU" => $v->sku,
                "ProductName" => $p->getName,
                "Quantity" => $orderItem->quantity,
                "ItemPrice" => $orderItem->price,
                "RowTotal" => $orderItem->total,
                "ProductURL" => env('FRONTEND_URL') . '/' . $p->defaultUrl->slug,
                "ImageURL" => $p->firstPhotoUrl,
                //"Categories" => ["Fiction", "Children"],
                "Brand" => $p->brand->name
            ];
            $orderItems[] = $t;
        });

        Klaviyo::track(TrackEvent::make(
            'Approved Order',
//            [
//                '$event_id' => $this->order->id . '_' . time(),
//                '$value' => $this->order->total,
//                "CheckoutURL" => env('APP_URL') . "/admin/all-orders/" . $this->order->id . "?activeRelationManager=0",
//                //'OrderId' => $this->order->order_number,
//                'Categories' => ['Cat1'],
//                'ItemNames' => $this->order->orderItems->map(fn($x) => $x->product->product->getName)->toArray(),
//                //'Brands' => $this->order->items->map(fn($x) => $x->product->product->brand->name)->toArray(),
//                'Items' => $orderItems
//            ],
            [
                'time' => now()->toAtomString(),
                'value' => $this->order->total,
                'value_currency' => 'TRY',
                'unique_id' => time(),
                'properties' => [
                    "CheckoutURL" => env('APP_URL') . "/admin/all-orders/" . $this->order->id . "?activeRelationManager=0",
                    //'OrderId' => $this->order->order_number,
                    'Categories' => ['Cat1'],
                    'ItemNames' => $this->order->orderItems->map(fn($x) => $x->product->product->getName)->toArray(),
                    //'Brands' => $this->order->items->map(fn($x) => $x->product->product->brand->name)->toArray(),
                    'Items' => $orderItems,
//                    'Title' => $this->product->name,
//                    'ItemId' => $this->product->id,
//                    'Categories' => $this->product->collections->map(fn($y) => json_decode($y->attribute_data)->name->value->tr)->flatten()->unique()->toArray(),
//                    'ImageUrl' => $this->product->coverImage()['thumb_webp'],
//                    'Url' => config('app.app_frontend_url') . '/urun/' . $this->product->defaultUrl->slug,
//                    'Metadata' => [
//                        'Price' => $this->product->variants->whereNull('deleted_at')->first()->prices->whereNull('deleted_at')->filter(fn($x) => $x->subscription_months_id == $x->whereNull('deleted_at')->where('priceable_id', $x->product->id)->where('price', '>', 0)->max('subscription_months_id'))->first()->price->value / 100,
//                    ],
                ],
            ],
            [
                'email' => $this->user->email,
                'first_name' => $this->user->first_name,
                'last_name' => $this->user->last_name,
            ],
            now()
        ));
    }
}
