<?php

namespace App\Jobs;

use App\Mail\ScoringModerationRequiredMail;
use App\Models\ScoringRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendScoringModerationEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = 60;

    /**
     * Create a new job instance.
     *
     * @param ScoringRequest $scoringRequest
     * @param array $evaluationData
     */
    public function __construct(
        public ScoringRequest $scoringRequest,
        public array $evaluationData
    ) {
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            $emails = config('scoring.moderation.notification_emails', []);
            
            if (empty($emails)) {
                Log::warning('No moderation emails configured', [
                    'scoring_request_id' => $this->scoringRequest->id
                ]);
                return;
            }

            // Send email to all configured recipients
            foreach ($emails as $email) {
                if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    Mail::to($email)->send(new ScoringModerationRequiredMail(
                        $this->scoringRequest,
                        $this->evaluationData
                    ));
                    
                    Log::info('Moderation email sent', [
                        'scoring_request_id' => $this->scoringRequest->id,
                        'recipient' => $email,
                        'product_value' => $this->evaluationData['product_value'] ?? 0
                    ]);
                }
            }

            // Add note to scoring request
            $this->scoringRequest->notes()->create([
                'content' => sprintf(
                    'Moderasyon bildirimi gönderildi (Mal bedeli: %s TL, Skor: %d, Çarpan: %s)',
                    number_format($this->evaluationData['product_value'] ?? 0, 2, ',', '.'),
                    $this->evaluationData['score'] ?? 0,
                    $this->evaluationData['multiplier'] ?? 1
                )
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send moderation email', [
                'scoring_request_id' => $this->scoringRequest->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e; // Re-throw to trigger retry
        }
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Moderation email job permanently failed', [
            'scoring_request_id' => $this->scoringRequest->id,
            'error' => $exception->getMessage()
        ]);

        // Add failure note
        $this->scoringRequest->notes()->create([
            'content' => 'Moderasyon bildirimi gönderilemedi: ' . $exception->getMessage()
        ]);
    }
}