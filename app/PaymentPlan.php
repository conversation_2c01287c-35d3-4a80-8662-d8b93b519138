<?php

namespace App;

use App\Models\OrderTransaction;
use Carbon\Carbon;

class PaymentPlan
{
    public array $plan;

    public function __construct(
        public ?Carbon $startDate = null,
        public ?int    $card_id = null,
        public ?int    $order_id = null,
        public ?float  $amount = null,
        public ?int    $months = 36,
    )
    {
    }

    public function calculate($amount, $months): void
    {
        for ($i = 1; $i <= $this->months; $i++) {
            $this->plan[$i] = [
                'due_date' => $this->startDate->clone()->addMonth($i - 1),
                'payment_status_id' => 2,
                'amount' => $i <= $months ? $amount : 0,
                'card_id' => $this->card_id,
                'order_id' => $this->order_id,
            ];
        }
    }

    public function add($amount, $months): self
    {
        if (empty($this->plan)) {
            $this->calculate($amount, $months);
            return $this;
        }

        for ($i = 1; $i <= $months; $i++) {
            $this->plan[$i]['amount'] += $amount;
        }

        return $this;
    }

    public function setCampaignPriceForGivenMonths($campaignPrice, $months): self
    {
        if (empty($this->plan)) {
            throw new \Exception('Plan is empty. Please create plan first.');
        }

        for ($i = 1; $i <= $months; $i++) {
            $this->plan[$i]['amount'] = $campaignPrice;
        }

        return $this;
    }

    // save plan to database
    public function save(): void
    {
        foreach ($this->plan as $key => $item) {
            if ($item['amount'] == 0) continue;
            OrderTransaction::updateOrCreate([
                'order_id' => $item['order_id'],
                'due_date' => $item['due_date'],
            ], [
                'payment_status_id' => $key == 1 ? 1 : 2, // Sadece ilk plan ödendi diğerleri ödeme alınmadı
                'last_payment_check' => $key == 1 ? $this->startDate : null,
                'bank_last_message' => $key == 1 ? 'success' : null,
                'amount' => $item['amount'],
                'payment_type' => 1,
                'card_id' => $item['card_id'],
            ]);
        }

    }

    public function saveWithoutTouchingPayedItems(): void
    {
        foreach ($this->plan as $key => $item) {
            if ($item['amount'] == 0) continue;
            OrderTransaction::firstOrCreate([
                'order_id' => $item['order_id'],
                'due_date' => $item['due_date'],
            ], [
                'payment_status_id' => $key == 1 ? 1 : 2, // Sadece ilk plan ödendi diğerleri ödeme alınmadı
                'last_payment_check' => $key == 1 ? $this->startDate : null,
                'bank_last_message' => $key == 1 ? 'success' : null,
                'amount' => $item['amount'],
                'payment_type' => 1,
                'card_id' => $item['card_id'],
            ]);
        }
    }
}
