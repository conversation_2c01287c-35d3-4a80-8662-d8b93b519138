<?php

namespace App\Observers;

use App\Models\Lunar\Collection;
use App\Models\Lunar\Url;
use RyanChandler\FilamentNavigation\Models\Navigation;
use Illuminate\Support\Str;

class NavigationObserver
{

    public function saving(Navigation $navigation)
    {
        $items = $navigation->items;
        foreach ($items as &$item) {
            if (in_array($item['type'], ['ana-kategori', 'alt-kategori'])) {
                $collection_url = Url::where('element_type', 'Lunar\Models\Collection')->where('element_id', $item['data']['category_id'])->where('default', 1)->first();
                $item['data']['category_slug'] = $collection_url->slug ?? null;
                $mainCatSlug = $item['data']['category_slug'];
                $item['data']['icon'] = $this->getIcon($item['data']['category_id']);
                $item['data']['is_visible'] = Collection::find($item['data']['category_id'])->is_visible ?? false;

                foreach ($item['children'] as &$child) {
                    if (in_array($child['type'], ['ana-kategori', 'alt-kategori'])) {
                        $subCollection_url = Url::where('element_type', 'Lunar\Models\Collection')->where('element_id', $child['data']['category_id'])->where('default', 1)->first();
                        $child['data']['category_slug'] = $mainCatSlug . '/' . $subCollection_url->slug ?? null;
                        $child['data']['icon'] = $this->getIcon($child['data']['category_id']);
                        $child['data']['is_visible'] = Collection::find($child['data']['category_id'])->is_visible ?? false;
                    }
                }
            }
        }

        $navigation->items = $items;
    }

    public function getIcon($id)
    {
        $collection = Collection::find($id);
        if ($collection->icon && $collection->icon != '') {
            return Str::of($collection->icon)->prepend('https://kiralabunu.fra1.cdn.digitaloceanspaces.com/');
        }
        return null;
    }

    /**
     * Handle the Navigation "created" event.
     *
     * @param \App\Models\Navigation $navigation
     * @return void
     */
    public function created(Navigation $navigation) {}

    /**
     * Handle the Navigation "updated" event.
     *
     * @param \App\Models\Navigation $navigation
     * @return void
     */
    public function updated(Navigation $navigation)
    {
        //ray('updated');
    }

    /**
     * Handle the Navigation "deleted" event.
     *
     * @param \App\Models\Navigation $navigation
     * @return void
     */
    public function deleted(Navigation $navigation)
    {
        //
    }

    /**
     * Handle the Navigation "restored" event.
     *
     * @param \App\Models\Navigation $navigation
     * @return void
     */
    public function restored(Navigation $navigation)
    {
        //
    }

    /**
     * Handle the Navigation "force deleted" event.
     *
     * @param \App\Models\Navigation $navigation
     * @return void
     */
    public function forceDeleted(Navigation $navigation)
    {
        //
    }
}
