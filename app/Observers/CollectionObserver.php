<?php

namespace App\Observers;


use App\Models\Lunar\Collection;

class CollectionObserver
{

    public function saving(Collection $collection)
    {
        //        ray('dsfs');
        //        dd($collection);
    }

    /**
     * Handle the Collection "created" event.
     *
     * @param \App\Models\Collection $collection
     * @return void
     */
    public function created(Collection $collection)
    {
        // dd($collection);
    }

    /**
     * Handle the Collection "updated" event.
     *
     * @param \App\Models\Collection $collection
     * @return void
     */
    public function updated(Collection $collection)
    {
        //        dd($collection);
    }

    /**
     * Handle the Collection "deleted" event.
     *
     * @param \App\Models\Collection $collection
     * @return void
     */
    public function deleted(Collection $collection)
    {
        //
    }

    /**
     * Handle the Collection "restored" event.
     *
     * @param \App\Models\Collection $collection
     * @return void
     */
    public function restored(Collection $collection)
    {
        //
    }

    /**
     * Handle the Collection "force deleted" event.
     *
     * @param \App\Models\Collection $collection
     * @return void
     */
    public function forceDeleted(Collection $collection)
    {
        //
    }
}
