<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Http\Request;

class MotorcycleFormMail extends Mailable
{
    use Queueable;


    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(public array $request)
    {
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            from: new Address('<EMAIL>', 'Kiralabunu Kurumsal Kiralama Talebi'),
            to: [
                new Address('<EMAIL>', 'Deren Altuncu'),
                new Address('<EMAIL>', 'Alper Erdem'),
            ],
            subject: 'Motosiklet Kiralama Talebi',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'mail.motorcycle-form',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
