<?php

namespace App\Mail;

use App\Models\ScoringRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ScoringModerationRequiredMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @param ScoringRequest $scoringRequest
     * @param array $evaluationData
     */
    public function __construct(
        public ScoringRequest $scoringRequest,
        public array $evaluationData
    ) {
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: sprintf(
                'Moderasyon Gerekli - Skorlama Talebi #%s - %s',
                $this->scoringRequest->ulid,
                $this->scoringRequest->full_name
            ),
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.scoring-moderation-required',
            with: [
                'scoringRequest' => $this->scoringRequest,
                'evaluation' => $this->evaluationData,
                'productValue' => number_format($this->evaluationData['product_value'] ?? 0, 2, ',', '.'),
                'approvedAmount' => number_format($this->evaluationData['approved_amount'] ?? 0, 2, ',', '.'),
                'requestedAmount' => number_format($this->scoringRequest->requested_amount, 2, ',', '.'),
                'score' => $this->evaluationData['score'] ?? 'Bilinmiyor',
                'multiplier' => $this->evaluationData['multiplier'] ?? 1,
                'adminUrl' => url("/admin/scoring-requests/{$this->scoringRequest->id}/edit"),
            ],
        );
    }
}