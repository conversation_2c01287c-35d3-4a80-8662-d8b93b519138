<?php

namespace App\Mail;

use App\Models\OrderTransaction;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentDelayEmail extends Mailable
{
    use Queueable, SerializesModels;

    private OrderTransaction $orderTransaction;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(OrderTransaction $orderTransaction)
    {
        $this->orderTransaction = $orderTransaction;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            from: new Address('<EMAIL>', 'Kiralabunu Tahsilat Servisi'),
            to: [
                new Address($this->orderTransaction->order->user->email, $this->orderTransaction->order->user->full_name),
            ],
            bcc: [
                new Address('<EMAIL>', 'Kiralabunu Tahsilat Servisi'),
            ],
            subject: 'Kira Öde<PERSON> Gecikmesi',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'mail.payment-delay',
            with: [
                'orderTransaction' => $this->orderTransaction,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
