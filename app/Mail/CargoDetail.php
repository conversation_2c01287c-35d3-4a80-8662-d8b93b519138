<?php

namespace App\Mail;

use App\Models\Order\Order;
use App\Models\Order\OrderItem;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class CargoDetail extends Mailable
{
    use Queueable, SerializesModels;

    public User $user;
    public OrderItem $orderItem;
    public Order $order;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(OrderItem $orderItem)
    {
        $this->orderItem = $orderItem;
        $this->order = $this->orderItem->order;
        $this->user = $this->order->user;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            from: new Address('<EMAIL>', 'Kiralabunu Üyelik Servisi'),
            to: [
                new Address($this->user->email, $this->user->full_name),
            ],
            replyTo: [
                new Address('<EMAIL>', 'Kiralabunu Üyelik Servisi'),
            ],
            subject: 'Siparişin kargoya verildi',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'mail.cargo-detail',
            with: [
                'user' => $this->user,
                'orderItem' => $this->orderItem,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
