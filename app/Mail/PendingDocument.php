<?php

namespace App\Mail;

use App\Models\Order;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PendingDocument extends Mailable
{
    use Queueable, SerializesModels;

    public User $user;
    public Order $order;
    public array $documents;


    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(User $user, Order $order, array $documents)
    {
        $this->user = $user;
        $this->order = $order;
        $this->documents = $documents;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            from: new Address('<EMAIL>', 'Kiralabunu Üyelik Servisi'),
            to: [
                new Address($this->user->email, $this->user->full_name),
            ],
            replyTo: [
                new Address('<EMAIL>', 'Kiralabunu Üyelik Servisi'),
            ],
            subject: ' Belge Talebi ',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'mail.pending-document',
            with: [
                'user' => $this->user,
                'order' => $this->order,
                'documents' => $this->documents,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
