<?php

namespace App\Listeners;

use App\Events\FindexFileReceivedFromBrowserAutomation;
use App\Events\ScoringCompleted;
use App\Jobs\SendFindexToSkorlabunu;
use App\Models\ScoringRequest;
use App\Models\ScoringResult;
use App\Services\FindexScoringService;
use App\Services\ScoreEvaluationService;
use App\Services\ScoringResultService;
use App\States\ScoringRequest\ApprovedState;
use App\States\ScoringRequest\RejectedState;
use App\States\ScoringRequest\ScoredState;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SendFindexFileToSkorlabunu
{

    protected ScoreEvaluationService $scoreEvaluationService;
    protected ScoringResultService $scoringResultService;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(ScoreEvaluationService $scoreEvaluationService, ScoringResultService $scoringResultService)
    {
        $this->scoreEvaluationService = $scoreEvaluationService;
        $this->scoringResultService = $scoringResultService;
    }

    /**
     * Handle the event.
     *
     * @param \App\Events\FindexFileReceivedFromBrowserAutomation $event
     * @return void
     */
    public function handle(FindexFileReceivedFromBrowserAutomation $event): void
    {
        logger()->info('Findex file received from browser automation. SendFindexFileToSkorlabunu listener started. Scoring request id: ' . $event->scoringRequestId, [
            'event' => $event,
        ]);

        $scoringRequest = ScoringRequest::find($event->scoringRequestId);

        // Local skorlama kullanılacaksa
        if (config('services.skorlabunu.use_local_scoring')) {
            $this->handleLocalScoring($scoringRequest, $event->localPdfPath);
        } else {
            // SendFindexToSkorlabunu job'ını dispatch et
            // Local dosyanın silinmesini iste (deleteAfterSend = true)
            SendFindexToSkorlabunu::dispatch(
                $event->localPdfPath,
                $event->scoringRequestId, // her zaman bu konuda bir user id olmayabilir, diğer kaynaklardan gelen veriler bir user id ile ilişkilendirilmemiş durumda şu aşamada, parametre boş geçemeyeceğimzi için scoringRequestId gönderiyorum.
                true, // deleteAfterSend = true
                $scoringRequest // scoringRequest modelini gönderiyorum.
            );
        }
    }

    /**
     * Local skorlama işlemini gerçekleştir
     *
     * @param ScoringRequest $scoringRequest
     * @param string $pdfPath
     * @return void
     */
    private function handleLocalScoring(ScoringRequest $scoringRequest, string $pdfPath): void
    {
        try {
            Log::info('Local scoring başlatılıyor', [
                'scoring_request_id' => $scoringRequest->id,
                'pdf_path' => $pdfPath
            ]);

            // HTML içeriği var mı kontrol et
            if (empty($scoringRequest->findex_evaluation_table)) {
                Log::error('Findex evaluation table HTML içeriği bulunamadı', [
                    'scoring_request_id' => $scoringRequest->id
                ]);

                $scoringRequest->notes()->create([
                    'content' => 'Local skorlama başarısız: Findex HTML içeriği bulunamadı',
                ]);

                return;
            }

            // FindexScoringService kullanarak skorlama yap
            $scoringService = new FindexScoringService();
            $result = $scoringService->calculateScoreFromHtml($scoringRequest->findex_evaluation_table);

            // Webhook benzeri response data oluştur
            $responseData = [
                'tracking_id' => 'LOCAL_' . $scoringRequest->ulid,
                'status' => 'completed',
                'score' => data_get($result, 'score', 0),
                'completed' => true,
                'risk_status' => data_get($result, 'risk_category.category', 'BILINMIYOR'),
                'min_rent_amount' => data_get($result, 'rent_range.min', 0),
                'max_rent_amount' => data_get($result, 'rent_range.max', 0),
                'rental_recommended' => data_get($result, 'rent_range.recommended', 0) > 0,
                'findeks_score' => data_get($result, 'extracted_data.findex_score'),
                'local_scoring_details' => $result // Tüm detaylı sonuçları sakla
            ];

            // ScoringResult oluştur
            ScoringResult::createSkorlaResult($scoringRequest, $responseData, $pdfPath);

            // Not ekle
            $scoringRequest->notes()->create([
                'content' => sprintf(
                    'Local skorlama tamamlandı. Skor: %s, Risk: %s, Kira Aralığı: %s-%s TL',
                    data_get($result, 'score', 0),
                    data_get($result, 'risk_category.category', 'BILINMIYOR'),
                    number_format(data_get($result, 'rent_range.min', 0), 0, ',', '.'),
                    number_format(data_get($result, 'rent_range.max', 0), 0, ',', '.')
                ),
            ]);

            Log::info('Local skorlama başarıyla tamamlandı', [
                'scoring_request_id' => $scoringRequest->id,
                'score' => data_get($result, 'score', 0),
                'risk_category' => data_get($result, 'risk_category.category', 'BILINMIYOR')
            ]);

            // local skorlama tamamlandığında, skorlama limitini hesapla
            // Transaction ile skorlama sonucunu kaydet
            DB::beginTransaction();

            try {
                // Check if auto evaluation is enabled for this source
                $useAdvancedEvaluation = $this->isAutoEvaluationEnabled($scoringRequest->scoring_source_id);
                Log::info('useAdvancedEvaluation', ['useAdvancedEvaluation' => $useAdvancedEvaluation]);

                // Skor değerlendirmesi yap
                if ($useAdvancedEvaluation) {
                    // Use new evaluation with multiplier
                    $evaluation = $this->scoreEvaluationService->evaluateWithMultiplier($scoringRequest, data_get($result, 'score', 0), data_get($result, 'rent_range.max', 0));
                } else {
                    // Use standard evaluation
                    $evaluation = $this->scoreEvaluationService->evaluate(data_get($result, 'score', 0), $scoringRequest->requested_amount);
                }

                $isApproved = $evaluation['is_approved'];
                // Skorlama talep kaynağı 1 (Partner Portal) değilse ve talep edilen tutar, skorlama sonucundan daha yüksekse, red durumuna geçir.
                if ($scoringRequest->scoring_source_id !== 1 && $scoringRequest->requested_amount > $evaluation['approved_amount']) {
                    $isApproved = false;
                }

                // Skorlama talep kaynağı 1 (Partner Portal) ve approved amount 0 ise, red durumuna geçir.
                if ($scoringRequest->scoring_source_id === 1 && $evaluation['approved_amount'] === 0) {
                    $isApproved = false;
                }

                // ScoringResult kaydını servis ile oluştur
                $scoringResult = $this->scoringResultService->createResult(
                    scoringRequest: $scoringRequest,
                    score: data_get($result, 'score', 0),
                    isApproved: $isApproved,
                    approvedAmount: $evaluation['approved_amount'],
                    findexJournalId: $findexJournalId ?? null,
                    processedAt: $processedAt ?? now(),
                    evaluationData: $evaluation
                );

                // State'i ScoredState'e geçir, sonra onay/red durumuna al
                $scoringRequest->status->transitionTo(ScoredState::class);

                // Sonuca göre Approved veya Rejected state'ine geçir
                if ($isApproved) {
                    $scoringRequest->status->transitionTo(ApprovedState::class);
                } else {
                    $scoringRequest->status->transitionTo(RejectedState::class);
                }

                DB::commit();

                $scoringRequest->notes()->create([
                    'content' => 'Skorlama sonucu: ' . $isApproved . ' - Talep edilen tutar: ' . $scoringRequest->requested_amount . ' - Skorlama sonucu: ' . $evaluation['approved_amount'],
                ]);

                Log::info('Skorlama sonucu başarıyla işlendi', [
                    'ulid' => $scoringRequest->ulid,
                    'score' => data_get($result, 'score', 0),
                    'is_approved' => $isApproved,
                    'approved_amount' => $evaluation['approved_amount'],
                    'findex_journal_id' => $findexJournalId ?? null
                ]);

                // Event tetikle (webhook gönderimi için)
                ScoringCompleted::dispatch($scoringRequest);
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Local skorlama sırasında hata oluştu', [
                    'scoring_request_id' => $scoringRequest->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }

            // app(ScoringResultService::class)->createResult(
            //     scoringRequest: $scoringRequest,
            //     score: data_get($result, 'score', 0),
            //     isApproved: false,
            //     // approvedAmount: $scoringRequest->requested_amount
            //     approvedAmount: 0,
            //     // findexJournalId parametresi, varsayılan değeri null olduğu için çıkarıldı.
            //     // processedAt parametresi, metod içinde null ise now() kullanıldığı ve
            //     // sizin de now() gönderme niyetiniz olduğu için çıkarıldı.
            // );
        } catch (\Exception $e) {
            Log::error('Local skorlama sırasında hata oluştu', [
                'scoring_request_id' => $scoringRequest->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $scoringRequest->notes()->create([
                'content' => 'Local skorlama başarısız: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Check if auto evaluation is enabled for this source
     *
     * @param int $sourceId
     * @return bool
     */
    private function isAutoEvaluationEnabled(int $sourceId): bool
    {
        if (!config('scoring.enabled', true)) {
            return false;
        }

        $enabledSources = config('scoring.enabled_sources', '*');

        // If string and equals *, enabled for all
        if (is_string($enabledSources) && $enabledSources === '*') {
            return true;
        }

        // Convert to array if needed
        if (is_string($enabledSources)) {
            $enabledSources = array_map('trim', explode(',', $enabledSources));
        }

        // Check if source ID is in the list
        return in_array((string)$sourceId, $enabledSources, true);
    }
}
