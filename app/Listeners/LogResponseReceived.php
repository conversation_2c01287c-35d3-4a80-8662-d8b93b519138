<?php

namespace App\Listeners;

use Illuminate\Http\Client\Events\ResponseReceived;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;


class LogResponseReceived
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(Request $request, Response $response)
    {
//        Log::info('Response LogResponseReceived', [$response]);
//        Log::info('Request LogResponseReceived', [$request]);
    }

    /**
     * Handle the event.
     *
     * @param \Illuminate\Http\Client\Events\ResponseReceived $event
     * @return void
     */
    public function handle(ResponseReceived $event)
    {
        //Log::info('ResponseReceived', [$event]);
//        logger('ResponseReceived', [$event->request->url(), $event->response->body(), $event->response->status()]);
//        logger('ResponseReceived', [$event->request->url(), $event]);
        Log::debug('HTTP request is being received.', [
            'body' => $event->response->body(),
            'headers' => $event->response->headers(),
            'status' => $event->response->status(),
        ]);
    }
}
