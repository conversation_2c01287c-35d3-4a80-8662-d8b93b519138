<?php

namespace App\Listeners;

use Illuminate\Http\Client\Events\RequestSending;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class LogResponseSending
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(Request $request, Response $response)
    {
//        Log::info('Response LogResponseSending', [$response]);
//        Log::info('Request LogResponseSending', [$request]);
    }

    /**
     * Handle the event.
     *
     * @param \Illuminate\Http\Client\Events\RequestSending $event
     * @return void
     */
    public function handle(RequestSending $event)
    {
//        logger('RequestSending', [$event->request->url(), $event->request->headers()]);
//        logger('RequestSending', [$event->request->url(), $event]);
        Log::debug('HTTP request is being sent.', [
            'body' => $event->request->body(),
            'url' => $event->request->url(),
            'headers' => $event->request->headers(),
            'data' => $event->request->data(),
        ]);
//        Log::info('RequestSending', [$event]);
    }
}
