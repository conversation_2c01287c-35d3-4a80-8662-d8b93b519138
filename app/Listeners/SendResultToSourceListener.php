<?php

namespace App\Listeners;

use App\Events\ScoringCompleted;
use App\Jobs\SendResultToSourceJob;
use App\Jobs\SendPaymentSmsJob;
use Illuminate\Support\Facades\Log;

class SendResultToSourceListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ScoringCompleted $event): void
    {
        $scoringRequest = $event->scoringRequest;

        Log::info('ScoringCompleted event alındı, webhook job\'ı dispatch ediliyor', [
            'scoring_request_id' => $scoringRequest->id,
            'ulid' => $scoringRequest->ulid,
            'status' => get_class($scoringRequest->status),
            'is_approved' => $scoringRequest->scoringResult?->is_approved,
            'webhook_url' => $scoringRequest->scoringSource?->webhook_url,
            'auto_processed' => $scoringRequest->additional_data['auto_processed'] ?? false,
        ]);

        // Check if this was auto-processed (from limit)
        $isAutoProcessed = $scoringRequest->additional_data['auto_processed'] ?? false;
        $delaySeconds = $isAutoProcessed ? 5 : 1;

        // Webhook gönderim job'ını queue'ya ekle
        // Auto-processed için 5 saniye, diğerleri için 1 saniye delay
        SendResultToSourceJob::dispatch($scoringRequest->id)
            ->delay(now()->addSeconds($delaySeconds));

        // Onaylanmış taleplerde SMS ödeme linki gönder
        if (
            $scoringRequest->scoringResult?->is_approved
            && $scoringRequest->scoringSource?->send_payment_sms_on_approval
        ) {
            Log::info('ScoringCompleted: Onaylanmış talep için SMS job\'ı dispatch ediliyor', [
                'scoring_request_id' => $scoringRequest->id,
                'ulid' => $scoringRequest->ulid,
                'approved_amount' => $scoringRequest->scoringResult->approved_amount
            ]);

            // SMS job'ını 1 saniye delay ile gönder (webhook gönderiminden sonra)
            SendPaymentSmsJob::dispatch($scoringRequest->id)
                ->delay(now()->addSeconds(1));
        } else {
            Log::info('ScoringCompleted: SMS job\'ı dispatch edilmedi', [
                'scoring_request_id' => $scoringRequest->id,
                'ulid' => $scoringRequest->ulid,
                'is_approved' => $scoringRequest->scoringResult?->is_approved,
                'send_payment_sms_on_approval' => $scoringRequest->scoringSource?->send_payment_sms_on_approval,
            ]);
        }
    }
}
