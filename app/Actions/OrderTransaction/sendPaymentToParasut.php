<?php

namespace App\Actions\OrderTransaction;

use App\Models\OrderTransaction;
use App\Services\Parasut\Parasut;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;

class sendPaymentToParasut
{
    public function __invoke(OrderTransaction $orderTransaction)
    {
        // 1. Meta kontrolü
        if ($orderTransaction->meta()->where('key', 'parasut_payment_sent')->exists()) {
            return;
        }

        // 2. Cache lock ile race condition kontrolü
        $lock = Cache::lock('parasut_payment_' . $orderTransaction->id, 10);

        if (!$lock->get()) {
            return;
        }

        try {
            // 3. Parasut API'ye ödeme gönderimi
            $response = Http::withToken(Parasut::getToken())
                ->post('https://api.parasut.com/v4/402231/contacts/' . $orderTransaction->order->user->parasutAccountId . '/contact_credit_transactions', [
                    'data' => [
                        'type' => 'transactions',
                        'attributes' => [
                            'description' => $orderTransaction->order->order_number . ' numaralı sipariş ödemesi',
                            'account_id' => $this->getAccountId(),
                            'date' => now()->format('Y-m-d'),
                            'amount' => $orderTransaction->amount,
                            'exchange_rate' => 1,
                            'payable_ids' => [
                                $orderTransaction->meta()->where('key', 'parasut_renting_invoice_id')->first()?->value
                            ]
                        ]
                    ]
                ]);

            // 4. Yanıt kontrolü ve işlemler
            if ($response->successful()) {
                // Log response
                logger()->channel('parasut')->info('Parasut Tahsilat Başarılı OrderTransaction ID: ' . $orderTransaction->id . ' Order Number: ' . $orderTransaction->order->order_number, ['response' => json_encode($response->json(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)]);

                // Save parasut payment id
                $orderTransaction->saveParasutPaymentId($response->json('data.id'));
            } else {
                // Rate limit kontrolü
                $responseData = $response->json();
                if (isset($responseData['errors']) && is_array($responseData['errors'])) {
                    foreach ($responseData['errors'] as $error) {
                        if (isset($error['title']) && $error['title'] === 'Too many requests' && isset($error['detail'])) {
                            // Bekleme süresini parse et
                            if (preg_match('/Try again in (\d+) seconds\.?/', $error['detail'], $matches)) {
                                $waitSeconds = (int) $matches[1];
                                $waitUntil = now()->addSeconds($waitSeconds);
                                
                                // Cache'e kaydet
                                Cache::put('parasut_rate_limit_until', $waitUntil->timestamp, $waitSeconds + 10);
                                
                                // Log rate limit
                                logger()->channel('parasut')->warning('Parasut Rate Limit - OrderTransaction ID: ' . $orderTransaction->id, [
                                    'wait_seconds' => $waitSeconds,
                                    'wait_until' => $waitUntil->toDateTimeString()
                                ]);
                                
                                // Rate limit exception fırlat (job tarafında yakalanacak)
                                throw new \Exception('RATE_LIMIT:' . $waitSeconds);
                            }
                        }
                    }
                }
                
                // Log error
                logger()->channel('parasut')->error('Parasut Tahsilat Hatası', ['response' => json_encode($response->json(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE), 'orderTransaction' => $orderTransaction->id, 'fallback' => $response->body()]);

                // Send email
                Mail::html(
                    "Sipariş Numarası: {$orderTransaction->order->order_number}\n <br>" .
                        "Sipariş ID: {$orderTransaction->order->id}\n <br>" .
                        "Sipariş Tarihi: {$orderTransaction->order->created_at}\n <br>" .
                        "Ödeme Tutarı: {$orderTransaction->amount}\n <br>" .
                        "Ödeme Vadeli: {$orderTransaction->due_date}\n <br>" .
                        "IT için Hata Detayı: <pre>" . json_encode($response->json(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>",
                    function ($message) {
                        $message
                            ->to('<EMAIL>')
                            ->cc('<EMAIL>')
                            ->subject('Parasut Tahsilat Kaydı Hatası');
                    }
                );
            }
        } finally {
            $lock->release();
        }
    }

    private function getAccountId(): int
    {
        return match (config('app.app_short_name')) {
            'KB' => 682201,
            'KM' => 402232,
            default => throw new \Exception('Geçersiz app_short_name')
        };
    }
}
