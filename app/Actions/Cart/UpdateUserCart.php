<?php

namespace App\Actions\Cart;

use App\Models\User;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateUserCart
{
    use AsAction;

    public function handle(?User $user, array $attributes)
    {
        if (!$user) {
            throw new AuthenticationException();
        }

        return DB::transaction(function () use ($user, $attributes) {
            $cart = $user->currentCart();

            $cart->update($attributes);

            $cart->load('items.product');

            return $cart;
        });
    }
}
