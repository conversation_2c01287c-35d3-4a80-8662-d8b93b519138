<?php

namespace App\Actions\Cart;

use App\Contracts\CartItem;
use App\Models\Cart\Cart;
use App\Models\User;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class AddProductToCart
{
    use AsAction;

    /**
     * @param User|null $user
     * @param CartItem $item
     *
     * @return Cart
     */
    public function handle(?User $user, CartItem $item, int $month, bool $isInsuranceRequested = false)
    {
        if (!$user) {
            throw new AuthenticationException();
        }

        return DB::transaction(function () use ($user, $item, $month, $isInsuranceRequested) {
            $cart = $user->currentCart();

            $cartItem = $cart->items()->firstOrCreate([
                'product_type' => $item->getMorphClass(),
                'product_id' => $item->getKey(),
                'month' => $month,
                'is_insurance_requested' => $isInsuranceRequested,
            ], [
                'quantity' => 1,
            ]);

//            if (!$cartItem->wasRecentlyCreated) {
//                $cartItem->increment('quantity');
//            }

            $cart->load('items.product');

            return $cart;
        });
    }
}
