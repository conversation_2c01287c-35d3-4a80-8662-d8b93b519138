<?php

namespace App\Http\Requests\UserCart;

use App\Models\Coupon;
use App\Rules\CouponRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class UpdateCartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'coupon' => ['sometimes', 'nullable', 'string', new CouponRule($this->user()->currentCart())],
        ];
    }

    /**
     * Get the validated data from the request.
     *
     * @return mixed
     */
    public function validated($key = null, $default = null)
    {
        $validated = $this->validator->validated();

        if (!isset($validated['coupon']) && $validated['coupon'] !== null) {
            return $validated;
        }

        $code = $validated['coupon'];

        unset($validated['coupon']);

        $validated['coupon_id'] = $code ? Coupon::whereCode($code)->value('id') : null;

        return $validated;
    }

    protected function failedValidation(Validator $validator)
    {
        // Perform your response
        // by default it will throw ValidationException.
        try {
            parent::failedValidation($validator);
        } catch (\Illuminate\Validation\ValidationException $e) {
            throw new \Illuminate\Validation\ValidationException($validator, response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->getMessageBag()->toArray()
            ], 422));
        }
    }
}
