<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        'api/auth/user/*',
        'api/auth/login',
        'api/auth/tg',
        'api/auth/register',
        'validation-check',
        'api/business-request',
        'api/kiralamotor-request',
        'webhook/skorlabunu-handler',
    ];
}
