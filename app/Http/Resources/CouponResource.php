<?php

namespace App\Http\Resources;

use App\Models\Coupon;

/**
 * @mixin \App\Models\Coupon
 */
class CouponResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'description' => $this->description,
            'type' => $this->type,
            'value' => $this->value,
            'limit' => $this->limit,
            'min_cart_amount' => $this->min_cart_amount,
            'max_cart_amount' => $this->max_cart_amount,
            'published' => $this->published,
            'start_date' => $this->start_date,
            'due_date' => $this->due_date,
            'spin' => $this->spin,
            'spin_index' => $this->spin_index,
            'used_total' => $this->used_total,
            'user_total' => $this->user_total,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
