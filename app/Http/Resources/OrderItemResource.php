<?php

namespace App\Http\Resources;

use App\Models\Lunar\SubscriptionMonths;
use App\Models\Order\OrderItem;

/**
 * @mixin \App\Models\Order\OrderItem
 */
class OrderItemResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        logger($this->product_id);

        return [
            'id' => $this->id,
            'product_id' => $this->product_id,
            'product_type' => $this->product_type,
            'product' => $this->product ? $this->product->getResource() : new OrderItem(),
            'variant' => $this->product->options()->get()->map(fn($option) => json_decode($option->productOptionValue->name)->tr ?? '')->implode(', '),
            'quantity' => $this->quantity,
            'price' => $this->price,
            'sub_total' => $this->sub_total,
            'tax_included' => $this->tax_included,
            'tax_amount' => $this->tax_amount,
            'tax_rate' => $this->tax_rate,
//            'discount_type' => $this->discount_type,
//            'discount_value' => $this->discount_value,
//            'discount_amount' => $this->discount_amount,
            'plan' => SubscriptionMonths::find($this->plan),
            'total' => $this->total,
            'cargo_at' => $this->cargo_at,
            'delivered_at' => $this->delivered_at,
            'contract_expired_at' => $this->contract_expired_at,
            'is_user_suitable_control' => $this->is_user_suitable_control ? true : false,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
