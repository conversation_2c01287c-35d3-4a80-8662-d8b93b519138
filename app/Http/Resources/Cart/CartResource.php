<?php

namespace App\Http\Resources\Cart;

use App\Http\Resources\BaseResource;
use App\Http\Resources\CouponResource;
use App\Http\Resources\CurrencyResource;
use App\Http\Resources\UserResource;

/**
 * @mixin \App\Models\Cart\Cart
 */
class CartResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        if (!$this->resource) {
            return [];
        }

        //$product = (object) \Illuminate\Support\Facades\Http::get('http://demo-store.test/api/products/' . $productId)->json()['product'];
        //$productVariantPrices = (object) \Illuminate\Support\Facades\Http::get('http://demo-store.test/api/product/prices/' . $product->id)->json()['prices'];

        return [
            'data' => [
                'id' => $this->id,
                'user_id' => $this->user_id,
                'user' => UserResource::make($this->user),
                'coupon' => CouponResource::make($this->whenLoaded('coupon')),
                'sub_total' => (float)$this->sub_total,
                'discount_amount' => (float)$this->discount_amount,
                'tax_amount' => (float)$this->tax_amount,
                'total' => (float)$this->total,
                'items' => CartItemResource::collection($this->items),
                'insurance_total' => $this->insurance_total,
                'items_count' => $this->items->sum(fn($item) => $item->quantity),
                'created_at' => $this->created_at,
                'updated_at' => $this->updated_at,
            ],
            'meta' => [
                'min_cart_total' => 0,
                'currency' => CurrencyResource::make((object)[
                    'id' => null,
                    'code' => 'TRY',
                    'name' => 'TL',
                    'symbol' => 'tl',
                    'created_at' => null,
                    'updated_at' => null,
                ]),
            ],
        ];
    }
}
