<?php

namespace App\Http\Resources\Cart;

use App\Http\Resources\BaseResource;
use App\Models\Lunar\SubscriptionMonths;

/**
 * @mixin \App\Models\Cart\CartItem
 */
class CartItemResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'product_id' => $this->product_id,
            'product_type' => $this->product_type,
            'product' => $this->product->getResource(),
            'quantity' => $this->quantity,
            'sub_total' => $this->getSubTotalAttribute(),
            'tax_amount' => $this->getTaxAmountAttribute(),
            'discount_amount' => $this->getDiscountAmountAttribute(),
            'month_id' => $this->month,
            'is_insurance_requested' => $this->is_insurance_requested,
            'tags' => $this->product_type == 'App\Models\Lunar\ProductVariant' ? $this->product->product->tags : [],
            'month' => SubscriptionMonths::find($this->month),
            'total' => $this->getTotalAttribute(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
