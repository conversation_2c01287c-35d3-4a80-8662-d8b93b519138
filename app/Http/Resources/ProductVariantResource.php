<?php

namespace App\Http\Resources;

use App\Models\Lunar\MediaStorage;
use App\Models\Lunar\SubscriptionMonths;
use Illuminate\Support\Facades\Cache;

/**
 * @mixin \App\Models\Product\ProductVariant;
 */
class ProductVariantResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        if (!Cache::has('subscription_months_id_on_product_variant_resource_' . $this->id)) {
            $subscription_months_id_on_product_variant_resource = $this->prices()->where('price', '>', 0)->whereNull('deleted_at')->groupBy('subscription_months_id')->get('subscription_months_id')->pluck('subscription_months_id');
            Cache::set('subscription_months_id_on_product_variant_resource_' . $this->id, $subscription_months_id_on_product_variant_resource, now()->addSeconds(120));
        } else {
            $subscription_months_id_on_product_variant_resource = Cache::get('subscription_months_id_on_product_variant_resource_' . $this->id);
        }

        return [
            'id' => $this->id,
            'product_id' => $this->product_id,
            //'product' => ProductResource::make($this->whenLoaded('product')),
            'currency' => CurrencyResource::make($this->whenLoaded('product', fn() => $this->product->currency)),
            //'product_option_type_id' => $this->product_option_type_id,
            //'type' => ProductOptionTypeResource::make($this->whenLoaded('type')),
            'attribute_data' => json_decode($this->product->attribute_data),
//            'slug' => $this->slug,
//            'stock_amount' => $this->stock_amount,
//            'stock_alert' => $this->stock_alert,
//            'price' => $this->price,
            'name' => $this->name,
//            'discount_type' => $this->discount_type,
//            'discount_value' => $this->discount_value,
            //'monthOptions' => $this->prices()->whereNull('deleted_at')->where('price', '>', 0)->groupBy('subscription_months_id')->get('subscription_months_id'),
            'monthOptions' => SubscriptionMonths::whereIn('id', $subscription_months_id_on_product_variant_resource)->orderBy('value')->get(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'url' => $this->product->defaultUrl,
            'status' => $this->product->status,
            'images' => MediaStorage::where('product_id', $this->product_id)->get(),
            'imagesWebP' => $this->product->coverImage()
        ];
    }
}
