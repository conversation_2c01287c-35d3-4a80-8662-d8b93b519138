<?php

namespace App\Http\Controllers;

use App\Models\Lunar\ProductVariant;
use App\Models\Order\Order;
use App\Models\SupportRequest;
use App\Services\ReturnProduct\ReturnProductService;
use Illuminate\Http\Request;

class SupportRequestController extends Controller
{
    public function index()
    {
        return response()->json([
            'message' => 'Received',
            'support_request' => SupportRequest::with(['product.product', 'product.product.storegeMedia', 'supportRequestType'])->where('user_id', auth()->id())->get(),
        ]);
    }

    public function create(Request $request)
    {
        $request->validate([
            'product' => 'required',
            'order' => 'required',
            'supportRequestType' => 'required|int',
            'note' => 'required',
        ]);

        if (cache()->has('support_request_' . auth()->id())) {
            return response()->json([
                'message' => '1 dakika içerisinde sadece bir destek talebi oluşturabilirsiniz. Lütfen bekleyin.',
            ], 429);
        }

        cache()->put('support_request_' . auth()->id(), md5($request->get('note') . $request->get('supportRequestType') . $request->get('order')), 60);

        $rps = new ReturnProductService;
        $rps->setOrder(Order::find($request->get('order')));
        $rps->setProductVariant(ProductVariant::find($request->get('product')['product']['id']));
        $rps->openSupportRequest(
            $request->get('supportRequestType'),
            $request->get('note'),
        );

        return response()->json([
            'message' => 'Destek Talebiniz Oluşturuldu',
            'support_request' => $rps->getSupportRequest(),
        ]);
    }
}
