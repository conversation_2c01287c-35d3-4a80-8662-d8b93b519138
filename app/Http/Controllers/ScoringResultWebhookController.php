<?php

namespace App\Http\Controllers;

use App\Events\ScoringCompleted;
use App\Models\ScoringRequest;
use App\Models\ScoringResultNew;
use App\Services\ScoreEvaluationService;
use App\Services\ScoringResultService;
use App\States\ScoringRequest\ScoredState;
use App\States\ScoringRequest\ApprovedState;
use App\States\ScoringRequest\RejectedState;
use App\States\ScoringRequest\ManuallyProcessedState;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;


/**
 * Skorlabunu'dan gelen skorlama sonucunu al ve işle,
 * Şu anda local skorlama yapıldığı için bu controller'ı KULLANMIYORUZ.
 */
class ScoringResultWebhookController extends Controller
{
    protected ScoreEvaluationService $scoreEvaluationService;
    protected ScoringResultService $scoringResultService;

    public function __construct(ScoreEvaluationService $scoreEvaluationService, ScoringResultService $scoringResultService)
    {
        $this->scoreEvaluationService = $scoreEvaluationService;
        $this->scoringResultService = $scoringResultService;
    }

    /**
     * Findex'ten gelen skorlama sonucunu al ve işle
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function receive(Request $request): JsonResponse
    {
        try {

            Log::info('ScoringResultWebhookController receive', ['request' => $request->all()]);

            // Request validation
            $validator = Validator::make($request->all(), [
                'ulid' => 'required|string|size:26',
                'score' => 'required|integer|min:0|max:500',
                'findex_journal_id' => 'nullable|string|max:255',
                'processed_at' => 'nullable|date',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Geçersiz veri formatı',
                    'errors' => $validator->errors(),
                    'error_code' => 'VALIDATION_ERROR'
                ], 422);
            }

            $ulid = $request->ulid;
            $score = $request->score;
            $findexJournalId = $request->findex_journal_id;
            $processedAt = $request->processed_at ? now()->parse($request->processed_at) : now();

            // Scoring request'i bul
            $scoringRequest = ScoringRequest::where('ulid', $ulid)->first();

            if (!$scoringRequest) {
                Log::warning('Skorlama sonucu alındı fakat talep bulunamadı', [
                    'ulid' => $ulid,
                    'score' => $score,
                    'findex_journal_id' => $findexJournalId
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Skorlama talebi bulunamadı',
                    'error_code' => 'REQUEST_NOT_FOUND'
                ], 404);
            }

            // Manuel işlenmiş kayıtları kontrol et (skip logic)
            if ($scoringRequest->isManuallyProcessed()) {
                Log::info('Skorlama sonucu alındı fakat talep manuel işlenmiş, atlanıyor', [
                    'ulid' => $ulid,
                    'score' => $score,
                    'manual_processed_at' => $scoringRequest->manual_processed_at,
                    'manual_processed_by' => $scoringRequest->manual_processed_by
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Talep manuel işlenmiş, skorlama sonucu atlandı',
                    'data' => [
                        'ulid' => $ulid,
                        'status' => 'skipped_manual_processed'
                    ]
                ]);
            }

            // Daha önce skorlanmış mı kontrol et
            if ($scoringRequest->scoringResult) {
                Log::warning('Skorlama sonucu alındı fakat talep zaten skorlanmış', [
                    'ulid' => $ulid,
                    'existing_score' => $scoringRequest->scoringResult->score,
                    'new_score' => $score
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Bu talep zaten skorlanmış',
                    'error_code' => 'ALREADY_SCORED'
                ], 409);
            }

            // Transaction ile skorlama sonucunu kaydet
            DB::beginTransaction();

            try {
                // Check if auto evaluation is enabled for this source
                $useAdvancedEvaluation = $this->isAutoEvaluationEnabled($scoringRequest->scoring_source_id);
                Log::info('useAdvancedEvaluation', ['useAdvancedEvaluation' => $useAdvancedEvaluation]);

                // Skor değerlendirmesi yap
                // TODO: Burada maxRentPrice'ı alıyoruz, ama bu değerin nereden geleceği konusunda bir fikir yok.
                if ($useAdvancedEvaluation) {
                    // Use new evaluation with multiplier
                    $evaluation = $this->scoreEvaluationService->evaluateWithMultiplier($scoringRequest, $score, 0);
                } else {
                    // Use standard evaluation
                    $evaluation = $this->scoreEvaluationService->evaluate($score, $scoringRequest->requested_amount);
                }

                // ScoringResult kaydını servis ile oluştur
                $scoringResult = $this->scoringResultService->createResult(
                    scoringRequest: $scoringRequest,
                    score: $score,
                    isApproved: $evaluation['is_approved'],
                    approvedAmount: $evaluation['approved_amount'],
                    findexJournalId: $findexJournalId,
                    processedAt: $processedAt,
                    evaluationData: $evaluation
                );

                // State'i ScoredState'e geçir, sonra onay/red durumuna al
                $scoringRequest->status->transitionTo(ScoredState::class);

                // Sonuca göre Approved veya Rejected state'ine geçir
                if ($evaluation['is_approved']) {
                    $scoringRequest->status->transitionTo(ApprovedState::class);
                } else {
                    $scoringRequest->status->transitionTo(RejectedState::class);
                }

                DB::commit();

                Log::info('Skorlama sonucu başarıyla işlendi', [
                    'ulid' => $ulid,
                    'score' => $score,
                    'is_approved' => $evaluation['is_approved'],
                    'approved_amount' => $evaluation['approved_amount'],
                    'findex_journal_id' => $findexJournalId
                ]);

                // Event tetikle (webhook gönderimi için)
                ScoringCompleted::dispatch($scoringRequest);

                return response()->json([
                    'success' => true,
                    'message' => 'Skorlama sonucu başarıyla işlendi',
                    'data' => [
                        'ulid' => $ulid,
                        'score' => $score,
                        'is_approved' => $evaluation['is_approved'],
                        'approved_amount' => $evaluation['approved_amount'],
                        'status' => $scoringRequest->status->value ?? 'scored'
                    ]
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Skorlama sonucu işlenirken hata oluştu', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Skorlama sonucu işlenirken bir hata oluştu',
                'error_code' => 'INTERNAL_ERROR'
            ], 500);
        }
    }

    /**
     * Check if auto evaluation is enabled for this source
     *
     * @param int $sourceId
     * @return bool
     */
    private function isAutoEvaluationEnabled(int $sourceId): bool
    {
        if (!config('scoring.enabled', true)) {
            return false;
        }

        $enabledSources = config('scoring.enabled_sources', '*');

        // If string and equals *, enabled for all
        if (is_string($enabledSources) && $enabledSources === '*') {
            return true;
        }

        // Convert to array if needed
        if (is_string($enabledSources)) {
            $enabledSources = array_map('trim', explode(',', $enabledSources));
        }

        // Check if source ID is in the list
        return in_array((string)$sourceId, $enabledSources, true);
    }
}
