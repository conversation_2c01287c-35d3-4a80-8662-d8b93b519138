<?php

namespace App\Http\Controllers;

use App\Jobs\klavioViewedProduct;
use App\Models\Favourite;
use App\Models\Lunar\Product;

class FavouriteController extends Controller
{
    public function toogle($pid)
    {
        $f = Favourite::where('user_id', auth()->id())
            ->where('product_id', $pid)
            ->first();

        if ($f) {
            $f->delete();
            return ['status' => 'deleted'];
        }

        Favourite::create([
            'user_id' => auth()->id(),
            'product_type' => Product::class,
            'product_id' => $pid,
        ]);

        return ['status' => 'created'];

    }

    public function all()
    {
        return Favourite::where('user_id', auth()->id())
            ->with(['product', 'product.variants.prices',
                'product.variants.prices.subscriptionMonths',
                'product.defaultUrl', 'product.brand', 'product.mediaStorage'])
            ->get();
    }

    public function status($pid)
    {
        $f = Favourite::where('user_id', auth()->id())
            ->where('product_id', $pid)
            ->first();

        // Klaviyo Viewed Product Dispatch
        klavioViewedProduct::dispatch(request()->user(), Product::find($pid));

        if ($f) {
            return ['status' => 'exist'];
        }

        return ['status' => 'not_exist'];
    }
}
