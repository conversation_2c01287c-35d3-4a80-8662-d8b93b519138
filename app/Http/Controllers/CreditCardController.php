<?php

namespace App\Http\Controllers;

use App\Facades\IyzipayFacade;
use App\Jobs\klavioStartedCheckout;
use App\Models\CreditCard;
use App\Models\Iyzico3DRequest;
use App\Models\User;
use Illuminate\Http\Request;

class CreditCardController extends Controller
{

    public function index()
    {
        // Trigger Klaviyo Started Checkout Event If Request Header Has Trigger Header
        if (request()->hasHeader('Trigger') && request()->header('Trigger') == 'Klaviyo') {
            klavioStartedCheckout::dispatch(request()->user(), request()->user()->cart);
        }
        
        $card_env = config('iyzipay.iyzipay_env');
        $cc = CreditCard::where('billable_id', auth()->id())->where('service_env', $card_env)->get();
        return response()->json($cc);
    }

    public function create(Request $request)
    {
        $cc = IyzipayFacade::addCreditCard($request->user(), $request->toArray());
        return response()->json($cc);
    }

    public function create3DS(Request $request)
    {
        try {
            $cc = IyzipayFacade::start3DRequest($request->user(), $request->toArray(), $request->bearerToken());
            if ($cc->getStatus() == 'failure') {
                return response()->json([
                    'message' => $cc->getErrorMessage(),
                    'code' => $cc->getErrorCode(),
                    'type' => 'iyzipay_error',
                ], 403);
            }
            return $cc->getHtmlContent();
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'type' => 'iyzipay_error',
            ], 403);
        }
    }

    public function complete3DS(Request $request)
    {
        $iyReq = Iyzico3DRequest::where('token', $request->token)->firstOrFail();
        $status = IyzipayFacade::complete3DRequest(User::find($iyReq->user_id), $request->toArray());

//        yzipay\Model\ThreedsInitialize {#2473 // app/Services/Iyzipay.php:173
//        -rawResult: "{"status":"failure","errorCode":"5177","errorMessage":"Sadece Sandbox kredi kartları ile ödeme yapılabilir","locale":"tr","systemTime":1680258121730,"conversationId":"1680258121"}"
//        -status: "failure"
//        -errorCode: "5177"
//        -errorMessage: "Sadece Sandbox kredi kartları ile ödeme yapılabilir"
//        -errorGroup: null
//        -locale: "tr"
//        -systemTime: 1680258121730
//        -conversationId: "1680258121"
//        -htmlContent: null

        return $this->success(['status' => $status], '3DS Completed');
    }

}
