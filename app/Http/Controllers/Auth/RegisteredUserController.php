<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\RegisterRequest;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Mail;

class RegisteredUserController extends Controller
{
    /**
     * Handle an incoming registration request.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(RegisterRequest $request)
    {
        /** @var User $user */
        $user = User::create($request->payload());

        $token = $user->createToken('Personal Access Token');

        // TODO: Send email verification
        //event(new Registered($user));

        $user->moveGuestCartToActiveCart();
        Mail::queue(new \App\Mail\Registered($user));

        return $this->created(array_merge(compact('user'), $token->toArray()), message: 'Registered');
    }
}
