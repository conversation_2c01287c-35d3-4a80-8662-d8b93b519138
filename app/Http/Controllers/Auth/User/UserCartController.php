<?php

namespace App\Http\Controllers\Auth\User;

use App\Http\Controllers\Controller;
use App\Http\Middleware\CheckPhoneVerification;
use App\Http\Requests\UserCart\AddCartProductRequest;
use App\Http\Requests\UserCart\RemoveCartProductRequest;
use App\Http\Requests\UserCart\UpdateCartItemRequest;
use App\Http\Requests\UserCart\UpdateCartRequest;
use App\Http\Resources\Cart\CartResource;
use App\Repositories\Cart\CartRepository;

class UserCartController extends Controller
{
    public function __construct(public CartRepository $cart)
    {
        $this->middleware(CheckPhoneVerification::class)->only('index');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return CartResource::make($this->cart->byCurrentUser());
    }

    /**
     * Update the user cart.
     *
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateCartRequest $request)
    {
        $cart = $this->cart->updateByCurrentUser($request->validated());
        return CartResource::make($cart);
    }

    /**
     * Add a product to cart.
     *
     * @param AddCartProductRequest $request
     *
     * @return CartResource
     */
    public function addProduct(AddCartProductRequest $request)
    {

//"product_id" => 812
//"month" => 5
//"product_variant_id" => 1821

        $cart = $this->cart->addProduct($request->guessProduct(), $request->get('month'), $request->get('is_insurance_requested', false));

        return CartResource::make($cart);
    }

    /**
     * Remove a product from cart.
     *
     * @param RemoveCartProductRequest $request
     *
     * @return CartResource
     */
    public function removeProduct(RemoveCartProductRequest $request)
    {
        $items = $request->get('itemIds');
        foreach ($items as $item) {
            $request->merge(['product_variant_id' => $item['product_id'], 'month' => $item['month_id']]);
            $cart = $this->cart->removeProduct($request->guessProduct(), $item['month_id']);
        }

        return CartResource::make($cart);
    }

    /**
     * Remove all products from cart.
     *
     * @return CartResource
     */
    public function clear()
    {
        $cart = $this->cart->clear();

        return CartResource::make($cart);
    }

    public function getAuth()
    {
        return auth()->user() ?? response()->json(['message' => 'Unauthenticated'], 401);
    }

    public function updateCartItem(UpdateCartItemRequest $request)
    {
        $cart = $this->cart->updateCartItem($request->getCartItem($request->get('cart_item_id')), $request->get('month'), $request->get('insurance', false));
        return CartResource::make($cart);
    }
}
