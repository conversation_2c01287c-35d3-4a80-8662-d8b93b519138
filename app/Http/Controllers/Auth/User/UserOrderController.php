<?php

namespace App\Http\Controllers\Auth\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\Order\OrderUpdateAddressRequest;
use App\Http\Resources\Order\OrderResource;
use App\Models\User;
use App\Repositories\Order\OrderRepository;
use App\Repositories\User\UserRepository;

class UserOrderController extends Controller
{
    public function __construct(public UserRepository $repository, public OrderRepository $orderRepository)
    {
        // code...
    }

    public function index()
    {
        $resource = $this->repository->userOrderList();

        return OrderResource::collection($resource);
    }

    public function adminUserOrders(User $user)
    {
        $resource = $this->repository->adminUserOrderListById($user);
        return OrderResource::collection($resource);
    }

    public function show($id)
    {
        $resource = $this->repository->userOrderDetail($id);

        return OrderResource::make($resource);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param OrderUpdateAddressRequest $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function updateAddress(OrderUpdateAddressRequest $request, $id)
    {
        $order = $this->orderRepository->update($request->validated(), $id);

        return OrderResource::make($order);
    }
}
