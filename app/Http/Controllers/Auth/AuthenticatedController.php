<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuthenticatedController extends Controller
{
    /**
     * Handle an incoming authentication request.
     *
     * @param \App\Http\Requests\Auth\LoginRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(LoginRequest $request)
    {
        try {
            $user = $request->authenticate();
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'errors' => $e->errors()
            ]);
        }

        $user->loadMissing('roles', 'permissions');

        // Kullanıcının misafir sepetindeki ürünleri aktif sepetine taşı
        $user->moveGuestCartToActiveCart();

        return $this->success(array_merge([
            'user' => UserResource::make($user),
        ], $user->createToken('Personal Access Token')->toArray()));
    }

    public function tgLogin(Request $request)
    {
        $email = $request->get('email');
        $user = Auth::guard('web')->loginUsingId(User::where('email', $email)->first()->id);
        $user->loadMissing('roles', 'permissions');
        return $this->success(array_merge([
            'user' => UserResource::make($user),
        ], $user->createToken('Personal Access Token')->toArray()));
    }

    /**
     * Log the user out of the application.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        $request->user()->tokens()->delete();

        return $this->success(message: 'The personal access token has been deleted.');
    }
}
