<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Carbon\Carbon;

class OrderController extends Controller
{
    public function getOBF(Order $order)
    {
        if ($order->created_at->lte(Carbon::parse('2024-01-14'))) {
            return view('order.obf')->with([
                'order' => $order,
            ]);
        }

        return view('order.obf-20240109')->with([
            'order' => $order,
        ]);
    }

    public function getMKS(Order $order)
    {
        if ($order->created_at->lte(Carbon::parse('2024-01-14'))) {
            return view('order.mks')->with([
                'order' => $order,
            ]);
        }

        return view('order.mks-20240109')->with([
            'order' => $order,
        ]);
    }
}
