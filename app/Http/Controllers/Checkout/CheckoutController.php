<?php

namespace App\Http\Controllers\Checkout;

use App\Facades\IyzipayFacade;
use App\Http\Controllers\Controller;
use App\Http\Middleware\CheckPhoneVerification;
use App\Http\Requests\Checkout\CheckoutRequest;
use App\Http\Resources\Order\OrderResource;
use App\Jobs\klavioPlaceOrder;
use App\Jobs\sendOrderReceivedToSlack;
use App\Models\CreditCard;
use App\Repositories\Cart\CartRepository;
use App\Repositories\Order\OrderRepository;
use App\StorableClasses\Plan;
use EonVisualMedia\LaravelKlaviyo\Klaviyo;
use EonVisualMedia\LaravelKlaviyo\TrackEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class CheckoutController extends Controller
{
    public function __construct(public CartRepository $cart, public OrderRepository $orderRepository)
    {
        $this->middleware(['auth', function ($request, $next) {
            $cart = $this->cart->byCurrentUser();

            if ($cart->isEmpty()) {
                return response()->json([
                    'message' => __('Cart is empty'),
                    'code' => 403,
                    'type' => 'cart_empty',
                ], 403);
            }

            return $next($request);
        }]);

        $this->middleware(CheckPhoneVerification::class);
    }

    /**
     * Checkout the cart.
     *
     * @param CheckoutRequest $request
     */
    public function __invoke(CheckoutRequest $request)
    {
        if (!$request->user()->address()->exists()) {
            return response()->json([
                'message' => __('Please select a address before performing this action.'),
                'code' => 403,
                'type' => 'address_not_specified',
            ], 403);
        }

        try {

            Log::info('CheckoutController::__invoke()', [
                'request' => $request->toArray(),
                'user' => $request->user()->toArray(),
            ]);

            if ($request->filled("number")) {
                IyzipayFacade::addCreditCard($request->user(), $request->toArray());
            }

            $order = $this->orderRepository->build($this->cart->byCurrentUser(), []);
            $responseMessage = empty($order) ? 'Redirection' : 'Order Created';

            $plan = new Plan();
            $plan->name($request->user()->fullName . "/" . $order->order_number)
                ->price($order->total)
                ->attributes($order->toArray());

            $request->user()->subscribe($plan);
            $request->user()->paySubscription($order->id);

            Klaviyo::track(TrackEvent::make(
                'Placed Order',
                [
                    '$event_id' => '1234_WINNIEPOOH',
                    '$value' => 9.99,
                ]
            ));

            // depo oluştur
//            Inventory::firstOrCreate([
//                'user_id' => auth()->id(),
//                'is_user_inventory' => 1,
//            ], [
//                'name' => auth()->user()->full_name . ' ' . auth()->user()->tckn . ' Deposu',
//                'slug' => Str::of(auth()->user()->full_name . ' ' . auth()->user()->tckn . ' Deposu')->slug(),
//            ]);

            return $this->success([
                'order' => OrderResource::make($order),
            ], $responseMessage);
        } catch (\Exception $e) {
            //dd($e->getFile(), $e->getLine());
            return response()->json([
                'message' => $e->getMessage(),
                'code' => 403,
                'type' => 'iyzipay_error',
            ], 403);
        }

    }

    public function compeletePaymentWithRegisteredCard(Request $request)
    {
        if (!$request->user()->address()->exists()) {
            return response()->json([
                'message' => __('Please select a address before performing this action.'),
                'code' => 403,
                'type' => 'address_not_specified',
            ], 403);
        }

        try {

            Log::info('CheckoutController::compeletePaymentWithRegisteredCard()', [
                'request' => $request->toArray(),
                'user' => $request->user()->toArray(),
            ]);

            $order = null;

            $lock = Cache::lock('completePaymentWithRegisteredCard_' . $request->user()->id, 30);
            if ($lock->get()) {

                $cc = CreditCard::find($request->get('cc'));

                if ($cc->billable_id != $request->user()->id) {
                    throw new \Exception("Hatalı Kredi Kartı");
                }

                DB::transaction(function () use ($request, $cc, &$order, &$responseMessage) {
                    $order = $this->orderRepository->build($this->cart->byCurrentUser(), ['cc' => $cc]);
                    $responseMessage = empty($order) ? 'Redirection' : 'Order Created';

                    Cache::set('order_' . $order->id, $order, now()->addDays(10));
                    Cache::set('order_user_' . $request->user()->id, $order, now()->addSeconds(30));

                    Log::info('CheckoutController::ordercreatedButNotPayedYet()', [
                        'order' => $order->toArray(),
                        'responseMessage' => $responseMessage,
                    ]);

                    // Check affiliate_teknosa
                    if ($request->get('affiliate_teknosa')) {
                        if ($order->items->filter(function ($item) use ($request) {
                                return $item->product->product->id == $request->get('affiliate_teknosa');
                            })->count() > 0) {
                            $order->affiliate = 'teknosa';
                            $order->save();
                        }
                    }

                    //            $plan = new Plan();
                    //            $plan->name($request->user()->fullName . "/" . $order->id)
                    //                ->price($order->total)
                    //                ->attributes($order->toArray());
                    //
                    //            $request->user()->subscribe($plan);

                    $request->user()->payTransaction($order->orderTransactions()->first());

                    // empty cart
                    $this->cart->byCurrentUser()->delete();

                    Mail::queue(new \App\Mail\NewOrder($request->user(), $order));
                    klavioPlaceOrder::dispatch($request->user(), $order);
                    sendOrderReceivedToSlack::dispatch($order);

                    return $this->success([
                        'order' => OrderResource::make($order),
                    ], $responseMessage);
                });

            } else {
                $order = Cache::get('order_user_' . $request->user()->id);
                $responseMessage = empty($order) ? 'Redirection' : 'Order Created';
                return $this->success([
                    'order' => OrderResource::make($order),
                ], $responseMessage);
            }
        } catch (\Exception $e) {

//            Log::info('CheckoutController::orderPaymentError()', [
//                'order' => $order->toArray(),
//                'responseMessage' => $e->getMessage(),
//            ]);

            return response()->json([
                'message' => $e->getMessage(),
                'code' => 403,
                'type' => 'iyzipay_error',
            ], 403);
        }

    }
}
