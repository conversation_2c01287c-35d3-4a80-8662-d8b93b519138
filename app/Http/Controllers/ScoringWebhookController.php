<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreScoringRequestRequest;
use App\Jobs\SendScoringToRedisJob;
use App\Models\ScoringRequest;
use App\Models\ScoringSource;
use App\Services\ScoringLimitService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;

class ScoringWebhookController extends Controller
{
    /**
     * Skorlama talebi al
     *
     * @param StoreScoringRequestRequest $request
     * @return JsonResponse
     */
    public function receive(StoreScoringRequestRequest $request): JsonResponse
    {
        try {
            // Skorlama kaynağını kontrol et
            $scoringSource = ScoringSource::findOrFail($request->scoring_source_id);

            if (!$scoringSource->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Skorlama kaynağı aktif değil',
                    'error_code' => 'SOURCE_INACTIVE'
                ], 422);
            }

            // Skorlama talebini oluştur
            $scoringRequest = ScoringRequest::create([
                'ulid' => (string)Str::ulid(),
                'scoring_source_id' => $scoringSource->id,
                'full_name' => $request->full_name,
                'tckn' => $request->tckn,
                'email' => $request->email,
                'birth_date' => $request->birth_date,
                'requested_amount' => $request->requested_amount,
                'requested_duration_months' => $request->requested_duration_months,
                'additional_data' => $request->additional_data,
            ]);

            // Log kayıt
            Log::info('Yeni skorlama talebi alındı, ScoringWebhookController request id: ' . $request->id, [
                'ulid' => $scoringRequest->ulid,
                'scoring_source_id' => $scoringSource->id,
                'source_name' => $scoringSource->name,
                'full_name' => $request->full_name,
                'tckn' => $request->tckn,
                'requested_amount' => $request->requested_amount,
            ]);

            // Check if request can be auto-processed with existing limit
            $limitService = new ScoringLimitService();
            $autoProcessed = $limitService->checkAndProcessWithLimit($scoringRequest);

            if ($autoProcessed) {
                // Request was auto-processed, skip Redis
                Log::info('Skorlama talebi otomatik işlendi, Redis\'e gönderilmedi', [
                    'ulid' => $scoringRequest->ulid,
                    'status' => $scoringRequest->fresh()->status->value ?? 'unknown',
                ]);

                $this->sendNotificationToKbEmployees($scoringRequest->fresh());

                return response()->json([
                    'success' => true,
                    'message' => 'Skorlama talebi başarıyla işlendi',
                    'data' => [
                        'ulid' => $scoringRequest->ulid,
                        'status' => $scoringRequest->fresh()->status->value ?? 'pending',
                        'created_at' => $scoringRequest->created_at->toISOString(),
                        'auto_processed' => true,
                    ]
                ], 201);
            }

            // Redis'e gönderme job'ını queue'ya ekle
            SendScoringToRedisJob::dispatch($scoringRequest->id)
                ->onQueue('scoring-high-priority')
                ->delay(now()->addSeconds(5)); // 5 saniye gecikme

            $this->sendNotificationToKbEmployees($scoringRequest);

            return response()->json([
                'success' => true,
                'message' => 'Skorlama talebi başarıyla alındı',
                'data' => [
                    'ulid' => $scoringRequest->ulid,
                    'status' => $scoringRequest->status->value ?? 'pending',
                    'created_at' => $scoringRequest->created_at->toISOString(),
                ]
            ], 201);
        } catch (\Exception $e) {
            // Debug için detaylı hata loglama
            Log::error('ScoringWebhookController: Hata', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Skorlama talebi işlenirken bir hata oluştu',
                'error_code' => 'INTERNAL_ERROR',
                // Test ortamında gerçek hata mesajını göster
                'debug_message' => app()->environment('testing') ? $e->getMessage() : null,
                'debug_trace' => app()->environment('testing') ? $e->getTraceAsString() : null
            ], 500);
        }
    }

    private function sendNotificationToKbEmployees(ScoringRequest $scoringRequest)
    {
        // Test ortamında Slack bildirimi gönderme
        // if (app()->environment('testing') || app()->environment('local')) {
        //     Log::info('Test ortamında Slack bildirimi atlandı', [
        //         'scoring_request_id' => $scoringRequest->id,
        //         'ulid' => $scoringRequest->ulid
        //     ]);
        //     return;
        // }

        try {
            $customer = $scoringRequest->full_name;

            // Slack mesajını oluştur
            $message = [
                'text' => "🔔 *Partner Portal Yeni Skorlama Talebi Oluşturuldu*",
                'blocks' => [
                    [
                        'type' => 'header',
                        'text' => [
                            'type' => 'plain_text',
                            'text' => '🔔 Yeni Skorlama Talebi',
                            'emoji' => true
                        ]
                    ],
                    [
                        'type' => 'section',
                        'fields' => [
                            [
                                'type' => 'mrkdwn',
                                'text' => "*Talep Kodu:*\n{$scoringRequest->ulid}"
                            ],
                            [
                                'type' => 'mrkdwn',
                                'text' => "*Müşteri:*\n{$scoringRequest->full_name}"
                            ]
                        ]
                    ],
                    [
                        'type' => 'actions',
                        'elements' => [
                            [
                                'type' => 'button',
                                'text' => [
                                    'type' => 'plain_text',
                                    'text' => 'Talebi Görüntüle',
                                    'emoji' => true
                                ],
                                'url' => url("/admin/scoring-requests/{$scoringRequest->id}/edit")
                            ]
                        ]
                    ]
                ]
            ];

            // Slack webhook URL'si
            $webhookUrl = '*********************************************************************************';

            // HTTP isteği gönder
            $response = Http::post($webhookUrl, $message);

            if (!$response->successful()) {
                Log::error("Slack bildirimi gönderilemedi: " . $response->body());
            }
        } catch (\Exception $e) {
            Log::error("Slack bildirimi gönderilirken hata oluştu: " . $e->getMessage());
        }
    }

    /**
     * Skorlama talebi durumu sorgula
     *
     * @param Request $request
     * @param string $ulid
     * @return JsonResponse
     */
    public function status(Request $request, string $ulid): JsonResponse
    {
        try {
            $scoringRequest = ScoringRequest::where('ulid', $ulid)->first();

            if (!$scoringRequest) {
                return response()->json([
                    'success' => false,
                    'message' => 'Skorlama talebi bulunamadı',
                    'error_code' => 'REQUEST_NOT_FOUND'
                ], 404);
            }

            $response = [
                'success' => true,
                'data' => [
                    'ulid' => $scoringRequest->ulid,
                    'status' => $scoringRequest->status->value ?? 'pending',
                    'full_name' => $scoringRequest->full_name,
                    'requested_amount' => $scoringRequest->requested_amount,
                    'created_at' => $scoringRequest->created_at->toISOString(),
                    //                    'redis_sent_at' => $scoringRequest->redis_sent_at?->toISOString(),
                ]
            ];

            // Skorlama sonucu varsa ekle
            if ($scoringRequest->scoringResult) {
                $response['data']['result'] = [
                    'score' => $scoringRequest->scoringResult->score,
                    'is_approved' => $scoringRequest->scoringResult->is_approved,
                    'approved_amount' => $scoringRequest->scoringResult->approved_amount,
                    'processed_at' => $scoringRequest->scoringResult->processed_at->toISOString(),
                ];
            }

            return response()->json($response);
        } catch (\Exception $e) {
            Log::error('Skorlama talebi durumu sorgulanırken hata oluştu', [
                'ulid' => $ulid,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Skorlama talebi durumu sorgulanırken bir hata oluştu',
                'error_code' => 'INTERNAL_ERROR'
            ], 500);
        }
    }
}
