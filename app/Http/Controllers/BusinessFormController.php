<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class BusinessFormController extends Controller
{
    public function saveForm(Request $request)
    {
        $text = "İsim Soyisim: {$request->get('name')} \n E-Posta: {$request->get('email')} \n Telefon: {$request->get('gsm')} \n Şirket Adı: {$request->get('firmName')} \n Mesaj: {$request->get('message')}";
        $res = \Illuminate\Support\Facades\Http::post('*********************************************************************************', ['text' => $text])->body();
        //$res = 'ok';

        Mail::queue(new \App\Mail\BusinessFormMail($text));

        if ($res == 'ok') {
            return response()->json([
                'status' => 'success',
                'message' => 'Form başarıyla kaydedildi.',
            ]);
        } else {
            return response()->json([
                'status' => 'error',
                'message' => 'Form kaydedilirken bir hata oluştu.',
            ]);
        }
    }

    public function saveMotorcycleForm(Request $request)
    {
        try {
            $sendmail = Mail::queue(new \App\Mail\MotorcycleFormMail($request->all()));

            return response()->json([
                'status' => 'success',
                'message' => 'Form başarıyla kaydedildi.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Form kaydedilirken bir hata oluştu.',
            ]);
        }
    }
}
