<?php

namespace App\Http\Controllers;

use App\Models\ScoringResult;
use App\Models\ScoringRequest;
use App\Models\ScoringLimit;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SkorlabunuWebhookController extends Controller
{
    public function handle(Request $request)
    {
        // Log the incoming webhook
        Log::channel('skorlabunu')->info('Skorlabunu webhook received', [
            'payload' => $request->all()
        ]);

        // Validate the request
        $validated = $request->validate([
            'tracking_id' => 'required|string',
            'status' => 'required|string',
            'score' => 'nullable|numeric',
            'completed' => 'boolean',
            'risk_status' => 'nullable|string',
            'min_rent_amount' => 'nullable|numeric',
            'max_rent_amount' => 'nullable|numeric',
            'rental_recommended' => 'nullable|boolean',
            'findeks_score' => 'nullable|numeric',
        ]);

        // Önce ScoringRequest içinde tracking_id ara
        $scoringRequest = ScoringRequest::where('skorlabunu_tracking_id', $validated['tracking_id'])->first();

        if ($scoringRequest) {
            return $this->handleScoringRequestResult($scoringRequest, $validated);
        }

        // ScoringRequest'te bulunamadıysa, user meta table'da ara
        $user = User::whereHas('meta', function ($query) use ($validated) {
            $query->where('key', 'skorlabunu_tracking_id')
                ->where('value', $validated['tracking_id']);
        })->first();

        if (!$user) {
            Log::channel('skorlabunu')->error('Neither scoring request nor user found for tracking ID', [
                'tracking_id' => $validated['tracking_id']
            ]);
            return response()->json(['message' => 'Tracking ID not found'], 404);
        }

        return $this->handleUserResult($user, $validated);
    }

    /**
     * Handle scoring result for ScoringRequest
     */
    protected function handleScoringRequestResult(ScoringRequest $scoringRequest, array $validated)
    {
        return DB::transaction(function () use ($scoringRequest, $validated) {
            Log::channel('skorlabunu')->info('Scoring request found for tracking ID', [
                'tracking_id' => $validated['tracking_id'],
                'scoring_request_id' => $scoringRequest->id
            ]);

            // Create scoring result for ScoringRequest
            ScoringResult::createSkorlaResult($scoringRequest, $validated, $scoringRequest->findex_pdf_path);

            // Create scoring limit if rental is recommended and we have a max rent amount
            if ($validated['rental_recommended'] ?? false) {
                $this->createScoringLimit(
                    $scoringRequest,
                    $validated['score'] ?? $validated['findeks_score'] ?? 0,
                    $validated['max_rent_amount'] ?? 0
                );
            }

            // ScoringRequest'e not ekle
            $scoringRequest->notes()->create([
                'content' => 'Skorlabunu servisinden gelen skorlama sonucu: ' . $validated['score'],
            ]);

            return response()->json(['message' => 'Webhook processed successfully for scoring request'], 200);
        });
    }

    /**
     * Handle scoring result for User
     */
    protected function handleUserResult(User $user, array $validated)
    {
        return DB::transaction(function () use ($user, $validated) {
            Log::channel('skorlabunu')->info('User found via meta for tracking ID', [
                'tracking_id' => $validated['tracking_id'],
                'user_id' => $user->id
            ]);

            // Create scoring result for User
            ScoringResult::createSkorlaResult($user, $validated, $user->findex_document);

            // Create scoring limit if rental is recommended and we have user's TCKN
            if (($validated['rental_recommended'] ?? false) && !empty($user->tckn)) {
                $scoringLimit = ScoringLimit::create([
                    'tckn' => $user->tckn,
                    'user_id' => $user->id,
                    'scoring_request_id' => null,
                    'score' => $validated['score'] ?? $validated['findeks_score'] ?? 0,
                    'approved_limit' => $validated['max_rent_amount'] ?? 0,
                    'remaining_limit' => $validated['max_rent_amount'] ?? 0,
                    'valid_until' => now()->addDays(30),
                ]);

                // Update user meta with current scoring limit ID
                $user->setCurrentScoringLimit($scoringLimit);

                Log::info('Scoring limit created for user', [
                    'user_id' => $user->id,
                    'scoring_limit_id' => $scoringLimit->id,
                    'approved_limit' => $scoringLimit->approved_limit
                ]);
            }

            return response()->json(['message' => 'Webhook processed successfully for user'], 200);
        });
    }

    /**
     * Create scoring limit for scoring request
     */
    protected function createScoringLimit(ScoringRequest $scoringRequest, $score, $approvedAmount)
    {
        try {
            // Find user by TCKN
            $user = null;
            if (!empty($scoringRequest->tckn)) {
                $user = User::where('tckn', $scoringRequest->tckn)->first();
            }

            // Create scoring limit
            $scoringLimit = ScoringLimit::createFromScoringResult(
                $scoringRequest,
                $approvedAmount,
                $score,
                $user
            );

            // If user exists, update their meta with current scoring limit ID
            if ($user) {
                $user->setCurrentScoringLimit($scoringLimit);
            }

            Log::info('Scoring limit created from Skorlabunu webhook', [
                'scoring_limit_id' => $scoringLimit->id,
                'scoring_request_id' => $scoringRequest->id,
                'user_id' => $user?->id,
                'approved_limit' => $approvedAmount
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create scoring limit from Skorlabunu webhook', [
                'error' => $e->getMessage(),
                'scoring_request_id' => $scoringRequest->id
            ]);
        }
    }
}
