<?php

namespace App\Http\Controllers\Tosla;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ToslaController extends Controller
{
    public function confirmPayment(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Payment validated', 'status' => true, 'data' => $request->all()])->setStatusCode(200);
    }

    public function checkStatus(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Check Status', 'status' => true, 'data' => $request->all()])->setStatusCode(200);
    }
}
