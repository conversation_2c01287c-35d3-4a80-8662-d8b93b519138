<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ScoringRequest;
use App\Jobs\SendPaymentSmsJob;
use Illuminate\Support\Facades\Log;

class PaymentSmsTriggerController extends Controller
{
    /**
     * Manuel olarak ödeme SMS'ini yeniden göndermek için endpoint.
     *
     * Route: POST /api/scoring/{ulid}/payment-sms
     */
    public function send(Request $request, string $ulid)
    {
        try {
            // ULID ile talebi bul
            $scoringRequest = ScoringRequest::with(['scoringResult', 'scoringSource'])
                ->where('ulid', $ulid)
                ->first();

            if (!$scoringRequest) {
                Log::warning('PaymentSmsTriggerController: Skorlama talebi bulunamadı', [
                    'ulid' => $ulid,
                    'triggered_by' => $request->user()?->id,
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Skorlama talebi bulunamadı.'
                ], 404);
            }

            // Onaylanmış mı kontrol et
            if (!$scoringRequest->scoringResult || !$scoringRequest->scoringResult->is_approved) {
                // Not ekle - Onaylanmamış talep için SMS gönderim denemesi
                $scoringRequest->notes()->create([
                    'content' => 'SMS gönderim talebi reddedildi: Skorlama talebi onaylanmamış durumda.',
                    'editor_id' => $request->user()?->id
                ]);

                Log::warning('PaymentSmsTriggerController: Onaylanmamış talep için SMS gönderim denemesi', [
                    'scoring_request_id' => $scoringRequest->id,
                    'ulid' => $ulid,
                    'is_approved' => $scoringRequest->scoringResult?->is_approved,
                    'triggered_by' => $request->user()?->id,
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Bu talep onaylanmamış durumda, SMS gönderilemez.'
                ], 409);
            }

            // Job'ı dispatch et (forceResend varsayılan: false)
            SendPaymentSmsJob::dispatch($scoringRequest->id);

            // Not ekle - Başarılı SMS gönderim talebi
            $triggerUser = $request->user();
            $scoringRequest->notes()->create([
                'content' => sprintf(
                    'Ödeme SMS\'i manuel olarak tetiklendi. Tetikleyen: %s (ID: %s)',
                    $triggerUser ? ($triggerUser->name ?? $triggerUser->email) : 'Sistem',
                    $triggerUser?->id ?? 'N/A'
                ),
                'editor_id' => $triggerUser?->id
            ]);

            Log::info('PaymentSmsTriggerController: SMS yeniden gönderim job\'ı dispatch edildi', [
                'scoring_request_id' => $scoringRequest->id,
                'ulid' => $ulid,
                'triggered_by' => $request->user()?->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'SMS gönderimi tetiklendi.'
            ], 202);
        } catch (\Exception $e) {
            // Not ekle - Hata durumu (sadece scoringRequest varsa)
            if (isset($scoringRequest) && $scoringRequest) {
                $scoringRequest->notes()->create([
                    'content' => 'SMS gönderim talebi başarısız: ' . $e->getMessage(),
                    'editor_id' => $request->user()?->id
                ]);
            }

            Log::error('PaymentSmsTriggerController: SMS gönderimi tetiklenirken hata', [
                'ulid' => $ulid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'SMS gönderimi tetiklenirken bir hata oluştu.'
            ], 500);
        }
    }
}
