<?php

namespace App\Http\Livewire;

use App\Models\OrderItems;
use App\States\Order\OrderApproved;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Component;

class ProductPurchaseBuyedTable extends Component implements Tables\Contracts\HasTable
{

    use Tables\Concerns\InteractsWithTable;

    protected function getTableQuery(): Builder
    {
        return OrderItems::query()
//            ->withoutGlobalScopes([
//                SoftDeletingScope::class,
//            ])
            ->leftJoin('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereIn('orders.status', [
                OrderApproved::class,
            ])
            ->where('order_items.is_user_suitable_control', 1)
            ->where('order_items.is_purchased', 1)
            ->whereNull('order_items.cancelled_at')
            ->select('order_items.*')
            ->orderBy('order_items.id', 'desc');
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\ImageColumn::make('product.product.getFirstPhoto')
                ->height(120)
                ->label('Ürün Resmi'),

            Tables\Columns\TextColumn::make('product.product.getNameMultiline')->label('Ürün Adı')->weight('medium')
                ->description(fn(OrderItems $record): string => $record->product?->product?->getExcerptText ?? '')
                ->html(),

            Tables\Columns\TextColumn::make('product.getVaariant')->label('Varyant'),

            Tables\Columns\TextColumn::make('order.order_number')->label('Sipariş No'),
            Tables\Columns\TextColumn::make('order.created_at')->label('Sipariş Tarihi')->dateTime(),
            Tables\Columns\TextColumn::make('order.user.full_name')->label('Müşteri Adı'),
            Tables\Columns\TextColumn::make('plan')->label('Kiralama Süresi')
                ->getStateUsing(function (OrderItems $record): null|string {
                    return $record->planObj->name;
                })
                ->alignRight(),
            Tables\Columns\TextColumn::make('order.total')
                ->label('Kira Tutarı')
                ->alignRight()
                ->formatStateUsing(fn(string $state): string => $state . ' ₺'),

            Tables\Columns\TextColumn::make('quantity')->label('Adet'),

//                Tables\Columns\ToggleColumn::make('is_purchased')
//                    ->label('Satın Alındı Mı?')
//                    ->sortable()
//                    ->toggleable(),

            IconColumn::make('is_exist_on_kb_inventory')
                ->label('Ürün KB Depoda var mı?')
                ->boolean(),

//            IconColumn::make('is_purchased')
//                ->label('Satın Alındı Mı?')
//                ->boolean(),

            IconColumn::make('is_supplied')
                ->label('KB Depoya Ulaştı Mı?')
                ->boolean(),

//            Tables\Columns\TextColumn::make('product_stock_id')->label('Product Stock ID'),
//            Tables\Columns\TextColumn::make('order_id')->label('order ID'),
            Tables\Columns\TextColumn::make('id')->label('order item rID'),
        ];
    }

    protected function getTableActions(): array
    {
        return [
            Action::make('Ürün Depo Girişi Yap')
                ->color('danger')
                ->disabled(fn(OrderItems $record) => $record->is_supplied)
                //->url(fn(OrderItems $record): string => '/admin/product-stocks/create?product_id=' . $record->product_id . '&order_item_id=' . $record->id),
                ->url(fn(OrderItems $record): string => '/admin/product-stocks/' . $record->product_stock_id . '/edit?order_item_id=' . $record->id),
        ];
    }

    public function render()
    {
        return view('livewire.product-purchase-buyed-table');
    }
}
