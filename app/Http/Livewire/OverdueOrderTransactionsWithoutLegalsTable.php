<?php

namespace App\Http\Livewire;

use App\Models\OrderTransaction;
use App\States\Order\Investigation;
use App\States\Order\OrderApproved;
use App\States\Order\OrderAtLegalPursuit;
use App\States\Order\OrderCancelled;
use App\States\Order\OrderCompleted;
use App\States\Order\OrderDenied;
use App\States\Order\OrderDocumentWaiting;
use App\States\Order\OrderEvaluation;
use App\States\Order\OrderReceived;
use App\States\Order\OrderRefunded;
use App\States\Order\OrderRenting;
use App\States\Order\OrderShipped;
use Closure;
use Filament\Forms;
use Filament\Tables;
use Filament\Tables\Actions\Position;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Livewire\Component;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class OverdueOrderTransactionsWithoutLegalsTable extends Component implements Tables\Contracts\HasTable
{
    use Tables\Concerns\InteractsWithTable;

    protected function getTableQuery(): Builder
    {
        return OrderTransaction::query()
            ->where('payment_status_id', 2)
            ->where('due_date', '<', now())
            ->whereHas('order', function (Builder $query) {
                $query->where('status', '!=', OrderAtLegalPursuit::class);
            })
            ->whereHas('order.user', function (Builder $query) {
                $query->where('is_company', false);
            })
            // ->select('order_transactions.*')
            // ->selectRaw('MIN(id) as min_id')
            ->groupBy('order_id');
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('id')
                ->label('ID')
                ->size('sm')
                ->visible(fn(): bool => auth()->user()->hasRole('Super Admin'))
                ->sortable(),
            Tables\Columns\TextColumn::make('order.order_number')
                ->size('sm')
                ->fontFamily('mono')
                ->label('Sipariş Numarası')
                ->searchable(isIndividual: true),
            Tables\Columns\TextColumn::make('order.user.full_name_db')
                ->size('sm')
                ->wrap()
                ->searchable(isIndividual: true)
                ->label('Müşteri Adı'),
            Tables\Columns\TextColumn::make('order.user.phone')
                ->getStateUsing(function (OrderTransaction $record): null|string {
                    $no = Str::of($record->order->user->phone)->remove('(')->remove(')')->remove(' ')->remove('-');
                    if ($no->startsWith(5))
                        $no = $no->prepend('+90');
                    if ($no->startsWith(0))
                        $no = $no->prepend('+9');

                    $message = "Merhaba " . $record->order->user->first_name . " Bey,\n";
                    $message .= "Kiralabunu tahsilat ekibinden tarafınıza ulaşıyorum; İsmim " . auth()->user()->first_name . ".\n\n";
                    $message .= "Sizinle Kiralabunu'na olan aylık kira ödemeniz ile ilgili iletişime geçiyorum.\n\n";
                    $message .= $record->order->orderProductListCommaSeperated . " ürününüzün " . number_format($record->amount, 2, ',', '.') . " TL tutarında kira ödemesi otomatik ödemeyle çekilemedi; gecikmiş bulunuyor.\n\n";
                    $message .= "Ödeme için kredi kartında limit açmanızı, dilerseniz yeni kredi kartı tanımlamanızı veya aşağıdaki hesap bilgilerine Havale / EFT yapmanızı rica ederiz.\n\n";
                    $message .= "Alıcı Hesap Adı: Kiralabunu Elektronik Pazarlama ve Ticaret Anonim Şirketi\n";
                    $message .= "IBAN: TR54 0020 5000 0971 6030 8000 01";

                    return '<a href="https://wa.me/' . $no . '?text=' . urlencode($message) . '" target="_blank">' . $no . '</a>';
                })
                ->html()
                ->size('sm')
                ->fontFamily('mono')
                ->searchable(isIndividual: true)
                ->label('Müşteri Tel'),
            // Tables\Columns\TextColumn::make('order.status_text')
            //     ->label('Statü')
            //     ->size('sm')
            //     ->wrap(),
            Tables\Columns\TextColumn::make('lastPayedRentsDate')
                ->label('Son Tahsilat Tarihi')
                ->size('sm')
                ->date(),
            Tables\Columns\TextColumn::make('order.orderProductList')
                ->label('Ürün Adı')
                ->size('sm')
                ->html(),
            Tables\Columns\TextColumn::make('plan')
                ->getStateUsing(function (OrderTransaction $record): null|string {
                    return $record->order?->orderItems->map(fn($item) => $item->planObj->name)->implode('<br />');
                })
                ->alignEnd()
                ->html()
                ->label('Kiralama Süresi')
                ->size('sm'),
            // Tables\Columns\TextColumn::make('order.shippingAddress')
            //     ->getStateUsing(function (OrderTransaction $record): null|string {
            //         return $record->order->shippingAddress?->city . ' / ' . $record->order->shippingAddress?->county;
            //     })
            //     ->label('İl İlçe')
            //     ->wrap()
            //     ->size('sm'),
            Tables\Columns\TextColumn::make('amount')
                ->label('Aylık Tutar')
                ->fontFamily('mono')
                ->formatStateUsing(fn(string $state): string => number_format($state, 2, ',', '.') . '₺')
                ->alignRight()
                ->size('sm'),
            // Tables\Columns\TextColumn::make('order.created_at')
            //     ->label('Sipariş Tarihi')
            //     ->date()
            //     ->size('sm'),
            // Tables\Columns\TextColumn::make('last_payment_check')
            //     ->label('Son Çekim Denemesi Tarihi')
            //     ->date()
            //     ->size('sm'),
            // Tables\Columns\TextColumn::make('offical_delay_emailed_at')
            //     ->label('Gecikme Bildirimi')
            //     ->date()
            //     ->size('sm'),
            // Tables\Columns\TextColumn::make('notice_of_termination_emailed_at')
            //     ->label('Fesih Bildirimi')
            //     ->date()
            //     ->size('sm'),
            // Tables\Columns\TextColumn::make('customer_contact_status_label')
            //     ->label('Müşteri İletişim Durumu')
            //     ->size('sm'),
        ];
    }

    protected function getTableActions(): array
    {
        return [];
    }

    protected function getTableBulkActions(): array
    {
        return [
            ExportBulkAction::make()
        ];
    }

    protected function getTableFilters(): array
    {
        return [
            Tables\Filters\Filter::make('status')
                ->form([
                    Forms\Components\Select::make('status')
                        ->label('Sipariş Statüsü')
                        ->options([
                            OrderReceived::class => 'Yeni',
                            OrderEvaluation::class => 'Değerlendiriliyor',
                            OrderDocumentWaiting::class => 'Belge Bekleniyor',
                            Investigation::class => 'Şüpheli İşlem',
                            OrderApproved::class => 'Onaylandı',
                            OrderShipped::class => 'Kargolandı',
                            OrderDenied::class => 'Reddedildi',
                            OrderCancelled::class => 'İptal',
                            OrderCompleted::class => 'Tamamlandı',
                            OrderRefunded::class => 'İade',
                            OrderRenting::class => 'Kiralama Devam Ediyor',
                            OrderAtLegalPursuit::class => 'Yasal Takip Siparişi',
                        ]),
                    //->placeholder(fn($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query
                        ->when(
                            $data['status'],
                            fn(Builder $query, $statu): Builder => $query->whereHas('order', function (Builder $query) use ($statu) {
                                $query->where('status', $statu);
                            }),
                        );
                })
                ->indicateUsing(function (array $data): array {
                    $indicators = [];
                    if ($data['status'] ?? null) {
                        $indicators['status'] = 'Sipariş Durumu ' . get_order_status_text($data['status']) . ' olarak filtrelenmiştir.';
                    }

                    return $indicators;
                }),
        ];
    }

    protected function getTableActionsPosition(): ?string
    {
        return Position::BeforeCells;
    }

    protected function getTableRecordUrlUsing(): ?Closure
    {
        return fn(Model $record): string => route('filament.resources.overdue-order-transactions-eski.view', ['record' => $record]);
    }

    protected function getTableRecordClassesUsing(): ?Closure
    {
        return function (Model $record) {
            $diffDate = now()->diffInDays($record->due_date);
            return match (true) {
                $diffDate >= 20 => "bg-red-300 $diffDate",
                $diffDate >= 15 => "bg-orange-300 $diffDate",
                $diffDate >= 0 => "$diffDate",
                default => throw new \Exception('Unexpected match value ' . $diffDate),
            };
        };
    }

    public function render()
    {
        return view('livewire.overdue-order-transactions-without-legals-table');
    }
}
