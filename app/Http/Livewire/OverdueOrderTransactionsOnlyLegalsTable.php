<?php

namespace App\Http\Livewire;

use App\Models\OrderTransaction;
use App\States\Order\Investigation;
use App\States\Order\OrderApproved;
use App\States\Order\OrderAtLegalPursuit;
use App\States\Order\OrderCancelled;
use App\States\Order\OrderCompleted;
use App\States\Order\OrderDenied;
use App\States\Order\OrderDocumentWaiting;
use App\States\Order\OrderEvaluation;
use App\States\Order\OrderReceived;
use App\States\Order\OrderRefunded;
use App\States\Order\OrderRenting;
use App\States\Order\OrderShipped;
use Filament\Forms;
use Filament\Tables;
use Filament\Tables\Actions\Position;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Component;

class OverdueOrderTransactionsOnlyLegalsTable extends Component implements Tables\Contracts\HasTable
{
    use Tables\Concerns\InteractsWithTable;

    protected function getTableQuery(): Builder
    {
        return OrderTransaction::query()
            ->where('payment_status_id', 2)
            ->where('due_date', '<', now())
            ->whereHas('order', function (Builder $query) {
                $query->where('status', OrderAtLegalPursuit::class);
            });
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('id')
                ->sortable(),
            Tables\Columns\TextColumn::make('order.order_number')
                ->searchable(isIndividual: true),
            Tables\Columns\TextColumn::make('order.user.full_name_db')
                ->searchable(isIndividual: true)
                ->label('Müşteri Adı'),
            Tables\Columns\TextColumn::make('order.user.phone')
                ->getStateUsing(function (OrderTransaction $record): null|string {
                    return '<a href="https://wa.me/' . $record->order->user->phone . '" target="_blank">' . $record->order->user->phone . '</a>';
                })
                ->html()
                ->searchable(isIndividual: true)
                ->label('Müşteri Tel'),

            Tables\Columns\TextColumn::make('order.status_text')
                ->label('Statü')
                ->wrap(),
            Tables\Columns\TextColumn::make('due_date')
                ->label('Vade Tarihi')
                ->date(),

            Tables\Columns\TextColumn::make('order.orderProductList')
                ->label('Ürün Adı')
                ->wrap()
                ->html(),
            Tables\Columns\TextColumn::make('plan')
                ->getStateUsing(function (OrderTransaction $record): null|string {
                    return $record->order?->orderItems->map(fn($item) => $item->planObj->name)->implode('<br />');
                })
                ->alignEnd()
                ->html()
                ->label('Kiralama Süresi'),

            Tables\Columns\TextColumn::make('order.shippingAddress')
                ->getStateUsing(function (OrderTransaction $record): null|string {
                    return $record->order->shippingAddress?->city . ' / ' . $record->order->shippingAddress?->county;
                })
                ->label('İl İlçe')
                ->wrap(),

            Tables\Columns\TextColumn::make('amount')
                ->label('Aylık Tutar')
                ->formatStateUsing(fn(string $state): string => $state . ' ₺')
                ->alignRight(),

            Tables\Columns\TextColumn::make('order.created_at')
                ->label('Sipariş Tarihi')
                ->date(),

            Tables\Columns\TextColumn::make('last_payment_check')
                ->label('Son Çekim Denemesi Tarihi')
                ->date(),

            //                Tables\Columns\TextColumn::make('delay_emailed_at')
            //                    ->label('Gecikme EPostası')
            //                    ->date(),
            Tables\Columns\TextColumn::make('offical_delay_emailed_at')
                ->label('Gecikme Bildirimi')
                ->date(),
            Tables\Columns\TextColumn::make('notice_of_termination_emailed_at')
                ->label('Fesih Bildirimi')
                ->date(),
            Tables\Columns\TextColumn::make('customer_contact_status_label')
                ->label('Müşteri İletişim Durumu'),
        ];
    }

    protected function getTableActions(): array
    {
        return [];
    }

    protected function getTableFilters(): array
    {
        return [
            Tables\Filters\Filter::make('status')
                ->form([
                    Forms\Components\Select::make('status')
                        ->label('Sipariş Statüsü')
                        ->options([
                            OrderReceived::class => 'Yeni',
                            OrderEvaluation::class => 'Değerlendiriliyor',
                            OrderDocumentWaiting::class => 'Belge Bekleniyor',
                            Investigation::class => 'Şüpheli İşlem',
                            OrderApproved::class => 'Onaylandı',
                            OrderShipped::class => 'Kargolandı',
                            OrderDenied::class => 'Reddedildi',
                            OrderCancelled::class => 'İptal',
                            OrderCompleted::class => 'Tamamlandı',
                            OrderRefunded::class => 'İade',
                            OrderRenting::class => 'Kiralama Devam Ediyor',
                            OrderAtLegalPursuit::class => 'Yasal Takip Siparişi',
                        ]),
                    //->placeholder(fn($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query
                        ->when(
                            $data['status'],
                            fn(Builder $query, $statu): Builder => $query->whereHas('order', function (Builder $query) use ($statu) {
                                $query->where('status', $statu);
                            }),
                        );
                })
                ->indicateUsing(function (array $data): array {
                    $indicators = [];
                    if ($data['status'] ?? null) {
                        $indicators['status'] = 'Sipariş Durumu ' . get_order_status_text($data['status']) . ' olarak filtrelenmiştir.';
                    }

                    return $indicators;
                }),
        ];
    }

    protected function getTableActionsPosition(): ?string
    {
        return Position::BeforeCells;
    }

    public function render()
    {
        return view('livewire.overdue-order-transactions-only-legals-table');
    }
}
