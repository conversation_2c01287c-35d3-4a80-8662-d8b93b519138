<?php

namespace App\Http\Livewire;

use App\Models\OrderItems;
use App\Models\ProductStock;
use App\States\Order\OrderApproved;
use Closure;
use Filament\Forms;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Component;
use Filament\Forms\Components\Textarea;
use App\Models\B2BProduct;

class ProductPurchaseTable extends Component implements Tables\Contracts\HasTable
{

    use Tables\Concerns\InteractsWithTable;

    protected function getTableQuery(): Builder
    {
        return OrderItems::query()
            //            ->withoutGlobalScopes([
            //                SoftDeletingScope::class,
            //            ])
            ->leftJoin('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereIn('orders.status', [
                OrderApproved::class,
            ])
            ->where('order_items.is_user_suitable_control', 1)
            ->where('order_items.is_purchased', 0)
            ->whereNull('order_items.cancelled_at')
            ->select('order_items.*')
            ->orderBy('order_items.id', 'desc');
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\ImageColumn::make('product.product.getFirstPhoto')
                ->height(fn(OrderItems $record) => $record->product_type == B2BProduct::class ? 44 : 120)
                ->width(fn(OrderItems $record) => $record->product_type == B2BProduct::class ? 120 : 120)
                ->alignCenter()
                ->label('Ürün Resmi'),
            Tables\Columns\TextColumn::make('product.product.getNameMultiline')->label('Ürün Adı')->weight('medium')
                ->size('sm')
                ->description(fn(OrderItems $record): string => $record->product->product->getExcerptText ?? '')
                ->html(),
            Tables\Columns\TextColumn::make('product.getVaariant')->label('Varyant')
                ->size('sm'),
            Tables\Columns\TextColumn::make('order.order_number')->label('Sipariş No')
                ->fontFamily('mono')
                ->size('sm'),
            Tables\Columns\TextColumn::make('order.created_at')->label('Sipariş Tarihi')->dateTime()
                ->wrap()
                ->size('sm'),
            Tables\Columns\TextColumn::make('order.user.full_name')->label('Müşteri Adı')
                ->wrap()
                ->size('sm'),
            Tables\Columns\TextColumn::make('plan')->label('Kiralama Süresi')
                ->getStateUsing(function (OrderItems $record): null|string {
                    return $record->planObj->name;
                })
                ->alignRight()
                ->size('sm'),
            Tables\Columns\TextColumn::make('order.total')
                ->label('Kira Tutarı')
                ->alignRight()
                ->fontFamily('mono')
                ->formatStateUsing(fn(string $state): string => number_format($state, 2, ',', '.') . ' ₺')
                ->size('sm'),
            Tables\Columns\TextColumn::make('quantity')->label('Adet')
                ->alignRight()
                ->fontFamily('mono')
                ->size('sm'),
            IconColumn::make('is_exist_on_kb_inventory')
                ->label('Depoda var mı?')
                ->boolean(),
        ];
    }

    protected function getTableActions(): array
    {
        return [
            Action::make('exist_on_kb_inventory_action')
                ->label('Depodaki Ürünü Sevk Et')
                ->color('danger')
                ->disabled(fn(OrderItems $record) => !$record->is_exist_on_kb_inventory)
                ->mountUsing(fn(Forms\ComponentContainer $form, OrderItems $record) => $form->fill([
                    //'product_stock_id' => (int)$record->product_stock_id,
                    'is_purchased' => true,
                    'is_supplied' => true,
                    'product_id' => $record->product_id,
                ]))
                ->action(fn(OrderItems $record, array $data) => $record->update($data))
                ->form(
                    [
                        Forms\Components\Hidden::make('product_id')->reactive(),
                        Forms\Components\Select::make('product_stock_id')
                            ->label('Depodaki Ürün Seri No')
                            ->options(fn(Closure $get) => ProductStock::where('product_id', $get('product_id'))->whereIn('inventory_id', [1, config('app.intermediate_inventory_id')])->whereNotNull('sn')->pluck('sn', 'id'))
                            ->required(),

                        // Bu menüden ilerlendirğinde ürünün doğrudan kargo ekranına ulaşası gerekiyor
                        Forms\Components\Toggle::make('is_supplied')
                            ->label('Siparişin depodaki ürün sevk edilerek tamamlanacaktır. Onaylıyor musunuz?')
                            ->required()
                            ->inline(),

                        Forms\Components\Hidden::make('is_purchased'),
                        Forms\Components\Hidden::make('is_supplied'),
                        Forms\Components\Hidden::make('product_stock_id'),
                    ]
                ),

            Action::make('Satın alındı mı?')
                ->label('Satın alındı mı?')
                ->color('danger')
                //                    ->disabled(fn(OrderTransaction $record) => $record->payment_status_id == 1)
                ->mountUsing(fn(Forms\ComponentContainer $form, OrderItems $record) => $form->fill([
                    'is_purchased' => (int)$record->is_purchased,
                ]))
                ->action(fn(OrderItems $record, array $data) => $record->saveIsPurchased($data['is_purchased'], $data['purchase_price'] ?? null))
                ->form(
                    [
                        Forms\Components\TextInput::make('purchase_price')
                            ->numeric()
                            ->mask(
                                fn(Forms\Components\TextInput\Mask $mask) => $mask
                                    ->numeric()
                                    ->decimalPlaces(2) // Set the number of digits after the decimal point.
                                    ->decimalSeparator(',') // Add a separator for decimal numbers.
                                    ->mapToDecimalSeparator([',']) // Map additional characters to the decimal separator.
                                    ->minValue(0) // Set the minimum value that the number can be.
                                    ->normalizeZeros() // Append or remove zeros at the end of the number.
                                    ->padFractionalZeros() // Pad zeros at the end of the number to always maintain the maximum number of decimal places.
                                    ->thousandsSeparator('.') // Add a separator for thousands.
                            )
                            //                                ->required(fn($data) => (bool)($data['is_purchased'] ?? false))
                            ->required(fn(Closure $get) => $get('is_purchased') === true)
                            ->label('Satın Alma Fiyatı (KDV Dahil)'),

                        Forms\Components\Toggle::make('is_purchased')
                            ->label('Ürünün satın alındığı olarak işlenecek, emin misiniz?')
                            ->reactive()
                            ->inline(),
                    ]
                ),

            Action::make('tedarik_edilemiyor')
                ->label('Tedarik Edilemiyor')
                ->color('danger')
                ->icon('heroicon-o-x-circle')
                ->form([
                    Textarea::make('note')
                        ->label('Not')
                        ->required()
                        ->placeholder('Tedarik edilememe / renk değişimi / alternatif ürün önerimi talebi gibi nedenleri yazınız...')
                ])
                ->action(function ($record, array $data) {
                    $record->sendTheOrderBackToOnboard($data['note']);
                })
                ->requiresConfirmation()
                ->modalHeading('Tedarik Edilemiyor')
                ->modalSubheading('Bu işlem geri alınamaz. Devam etmek istediğinizden emin misiniz?'),
        ];
    }

    public function render()
    {
        return view('livewire.product-purchase-table');
    }
}
