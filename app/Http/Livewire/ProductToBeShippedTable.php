<?php

namespace App\Http\Livewire;

use App\Models\Inventory;
use App\Models\Order\OrderItem;
use App\Models\OrderItems;
use App\Models\ProductStock;
use App\Services\Parasut\Parasut;
use App\States\Order\OrderApproved;
use Filament\Forms;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Livewire\Component;

class ProductToBeShippedTable extends Component implements Tables\Contracts\HasTable
{
    use Tables\Concerns\InteractsWithTable;

    protected function getTableQuery(): Builder
    {
        return OrderItems::query()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ])
            ->leftJoin('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereIn('orders.status', [
                OrderApproved::class,
            ])
            ->where('order_items.is_user_suitable_control', 1)
            ->where('order_items.is_purchased', 1)
            ->where('order_items.is_supplied', 1)
            ->whereNull('order_items.cancelled_at')
            ->whereNull('order_items.deleted_at')
            ->whereNull('order_items.invoice_number')
            ->select('order_items.*')
            ->orderBy('order_items.id', 'desc');
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\ImageColumn::make('product.product.getFirstPhoto')
                ->height(120)
                ->label('Ürün Resmi'),

            Tables\Columns\TextColumn::make('product.product.getNameMultiline')->label('Ürün Adı')->weight('medium')
                ->description(fn(OrderItems $record): string => $record->product->product->getExcerptText ?? '-')
                ->html(),

            Tables\Columns\TextColumn::make('product.getVaariant')->label('Varyant'),
            Tables\Columns\TextColumn::make('order.order_number')->label('Sipariş No'),
            Tables\Columns\TextColumn::make('order.created_at')->label('Sipariş Tarihi')->dateTime(),
            Tables\Columns\TextColumn::make('order.finance_approved_at')->label('Finans Onay Tarihi')->dateTime(),
            Tables\Columns\TextColumn::make('order.user.full_name')->label('Müşteri Adı'),
            Tables\Columns\TextColumn::make('plan')->label('Kiralama Süresi')
                ->getStateUsing(function (OrderItems $record): null|string {
                    return $record->planObj->name;
                })
                ->alignRight(),
            Tables\Columns\TextColumn::make('order.total')
                ->label('Kira Tutarı')
                ->alignRight()
                ->formatStateUsing(fn(string $state): string => $state . ' ₺'),

            Tables\Columns\TextColumn::make('quantity')->label('Adet'),

            IconColumn::make('is_exist_on_kb_inventory')
                ->label('Ürün KB Depoda var mı?')
                ->boolean(),

            IconColumn::make('is_purchased')
                ->label('Satın Alındı Mı?')
                ->boolean(),

            IconColumn::make('is_supplied')
                ->label('KB Depoya Ulaştı Mı?')
                ->boolean(),

            Tables\Columns\TextColumn::make('id')
                ->label('Paraşüt')
                ->formatStateUsing(fn(OrderItems $record) => OrderItem::find($record->id)->meta()->where('key', 'parasut_waybill_id')->first()?->value ? 'Paraşüt ' : '')
                ->url(fn($record) => 'https://uygulama.parasut.com/402231/giden-irsaliyeler/' . OrderItem::find($record->id)->meta()->where('key', 'parasut_waybill_id')->first()?->value, true),
        ];
    }

    protected function getTableActions(): array
    {
        return [
            Action::make('İrsaliye Kes')
                ->label('İrsaliye Oluştur ve Paraşüte Aktar ')
                //                ->visible(fn(OrderItems $record): bool => !Bus::findBatch($record->waybill_batch ?? '')?->finished() || $record->meta()->where('key', 'parasut_waybill_id')->count() == 0)
                ->visible(fn(OrderItems $record): bool => OrderItem::find($record->id)->meta()->where('key', 'parasut_waybill_id')->count() == 0)
                ->color('danger')
                ->action(function (OrderItems $record): OrderItems {
                    $parasut = new Parasut();
                    $parasut->createWaybill(OrderItem::where('id', $record->id)->get());
                    return $record;
                }),

            Action::make('cargo')
                ->visible(fn(OrderItems $record): bool => OrderItem::find($record->id)->meta()->where('key', 'parasut_waybill_id')->count() == 1)
                ->label(fn(OrderItems $record): ?string => $record->cargo_at == null ? 'İrsaliye No Gir' : 'İrsaliye No Güncelle')
                ->color('danger')
                ->mountUsing(fn(Forms\ComponentContainer $form, OrderItems $record) => $form->fill([
                    'invoice_number' => $record->invoice_number,
                ]))
                ->action(function (OrderItems $record, array $data): OrderItems {
                    $data['cargo_at'] = now();
                    $data['invoice_number'] = Str::of($record->order->id)->padLeft(7, 0)->prepend('5')->append($data['invoice_number']);
                    $record->update($data);
                    $cargoNumber = Str::of($record->order->id)->padLeft(7, 0)->prepend('5')->append($record->id);

                    // Kargo veri aktarımı
                    $yurtici = new \App\Services\Cargo\YurticiKargo([
                        'username' => '1048N815146906G',
                        'password' => 'Kk51nUrK2wpBi08a',
                        'test' => false //TEST MODE true / false
                    ]);
                    //
                    //                    dd([
                    //                        'cargoKey' => $record->invoice_number,
                    //                        'invoiceKey' => $record->invoice_number,
                    //                        'receiverCustName' => $record->order->user->address->first_name . ' ' . $record->order->user->address->last_name,
                    //                        'receiverAddress' => $record->order->user->address->address . ' ' . $record->order->user->address->county . ' ' . $record->order->user->address->city,
                    //                        'receiverPhone1' => $record->order->user->address->phone,
                    //                    ]);
                    //
                    $kargoYolla = $yurtici->createCargo([
                        'cargoKey' => $cargoNumber,
                        'invoiceKey' => $record->invoice_number,
                        'receiverCustName' => $record->order->shippingAddress->first_name . ' ' . $record->order->shippingAddress->last_name,
                        'receiverAddress' => $record->order->shippingAddress->address . ' ' . $record->order->shippingAddress->county . ' ' . $record->order->shippingAddress->city,
                        'receiverPhone1' => $record->order->shippingAddress->phone,
                    ]);

                    // Depo güncelleme işlemi burada yapılacak
                    $user = $record->order->user;
                    $inventory = Inventory::firstOrCreate([
                        'user_id' => $user->id,
                        'is_user_inventory' => 1,
                    ], [
                        'name' => $user->full_name . ' ' . $user->tckn . ' Deposu',
                        'slug' => Str::of($user->full_name . ' ' . $user->tckn . ' Deposu')->slug(),
                    ]);

                    $productStock = ProductStock::find($record->product_stock_id)
                        ->update([
                            'inventory_id' => $inventory->id,
                            'entried_at' => now(),
                        ]);

                    // Eğer siparişin tüm ürünleri sevk edildiyse siparişin durumunu değiştir

                    //                    $orderItems = OrderItems::where('order_id', $record->order_id)
                    //                        ->whereNull('invoice_number')
                    //                        ->get();

                    return $record;
                })
                ->form(
                    [
                        Forms\Components\TextInput::make('invoice_number')
                            ->label('İrsaliye Numarası')
                            ->required()
                            ->maxLength(255),
                    ]
                ),
        ];
    }

    public function render()
    {
        return view('livewire.product-to-be-shipped-table');
    }
}
