<?php

namespace App\Http\Livewire;

use App\Models\SupportRequest;
use App\States\SupportRequest\SupportCompleted;
use App\States\SupportRequest\SupportProductAtService;
use App\States\SupportRequest\SupportProductControl;
use Filament\Forms;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Component;

class ReturnedProductHasShippedTable extends Component implements Tables\Contracts\HasTable
{
    use Tables\Concerns\InteractsWithTable;

    protected function getTableQuery(): Builder
    {
        return SupportRequest::query()
            ->withoutGlobalScopes([
                //                SoftDeletingScope::class,
            ])
            //            ->leftJoin('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereIn('support_requests.status', [
                SupportProductControl::class,
            ]);
        //            ->where('order_items.is_user_suitable_control', 1)
        //            ->where('order_items.is_purchased', 1)
        //            ->whereNotNull('order_items.invoice_number')
        //            ->select('order_items.*')
        //            ->orderBy('order_items.id', 'desc');
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\ImageColumn::make('product.product.getFirstPhoto')
                ->height(120)
                ->width(120)
                ->label('Ürün Resmi'),
            Tables\Columns\TextColumn::make('product.product.getNameMultiline')->label('Ürün Adı')->weight('medium')
                ->description(fn(SupportRequest $record): string => $record->product->product->getExcerptText ?? '-')
                ->html()
                ->wrap()
                ->size('sm'),
            Tables\Columns\TextColumn::make('product.getVaariant')->label('Varyant')
                ->fontFamily('mono')
                ->size('sm'),
            Tables\Columns\TextColumn::make('productStockItem.sn')->label('SN')
                ->fontFamily('mono')
                ->size('sm'),
            Tables\Columns\TextColumn::make('order.order_number')->label('Sipariş No')
                ->fontFamily('mono')
                ->size('sm'),
            Tables\Columns\TextColumn::make('inventoryId')->label('Depo ID')
                ->visible(fn(): bool => auth()->user()->hasRole('Super Admin'))
                ->size('sm'),
            Tables\Columns\TextColumn::make('order.created_at')->label('Sipariş Tarihi')->date()
                ->size('sm'),
            Tables\Columns\TextColumn::make('order.user.full_name')->label('Müşteri Adı')
                ->size('sm'),
        ];
    }

    protected function getTableActions(): array
    {
        return [
            Action::make('Ürün değerlendirme sonucu')
                ->label(fn(SupportRequest $record): ?string => $record->inventoryId ? 'Ürün değerlendirme sonucu' : 'IT\'den Destek Alın')
                //                ->disabled(fn(SupportRequest $record) => $record->is_product_received === 1)
                ->disabled(fn(SupportRequest $record) => $record->inventoryId === null)
                ->color('danger')
                ->icon('akar-shipping-box-02')
                ->requiresConfirmation()
                //                ->mountUsing(fn(Forms\ComponentContainer $form, SupportRequest $record) => $form->fill([
                //                    'delivered_at' => $record->delivered_at,
                //                    'cargo_receiver_name' => $record->cargo_receiver_name,
                //                ]))
                ->action(function (SupportRequest $record, array $data): SupportRequest {

                    // Support Request Status Update
                    $record->status = $data['status'];
                    $record->save();

                    // Send Product To Main Inventory
                    if ($record->status == SupportCompleted::class) {
                        // Add additional Prices
                        $record->order->orderTransactions()->withTrashed()->first()->addExtraFees(data_get($data, 'purchase_price', 0), data_get($data, 'missing_piece_fee', 0));
                        $orderItem = $record->order->orderItems()->where('product_id', $record->product_id)->first();
                        // Ana depo transfer
                        $orderItem->addProductToMainStock();
                        // Ürün Stok Güncelle
                        $orderItem->returnProductStock(1);
                    } elseif ($record->status == SupportProductAtService::class) {
                        // No action yet
                    }

                    return $record;
                })
                ->form(
                    [
                        Forms\Components\Select::make('status')
                            ->label('Talep Statüsü')
                            ->options([
                                SupportProductAtService::class => 'Servis Süreci',
                                SupportCompleted::class => 'Çözüldü',
                            ])
                            ->required()
                            ->reactive(),

                        //                        Forms\Components\TextInput::make('purchase_price')
                        //                            ->visible(fn(callable $get) => $get('status') == SupportCompleted::class)
                        //                            ->numeric()
                        //                            ->mask(fn(Forms\Components\TextInput\Mask $mask) => $mask
                        //                                ->numeric()
                        //                                ->decimalPlaces(2) // Set the number of digits after the decimal point.
                        //                                ->decimalSeparator(',') // Add a separator for decimal numbers.
                        //                                ->mapToDecimalSeparator([',']) // Map additional characters to the decimal separator.
                        //                                ->minValue(0) // Set the minimum value that the number can be.
                        //                                ->normalizeZeros() // Append or remove zeros at the end of the number.
                        //                                ->padFractionalZeros() // Pad zeros at the end of the number to always maintain the maximum number of decimal places.
                        //                                ->thousandsSeparator('.') // Add a separator for thousands.
                        //                            )
                        //                            ->required()
                        //                            ->label('İptal Ceza Bedeli'),

                        Forms\Components\TextInput::make('missing_piece_fee')
                            ->visible(fn(callable $get) => $get('status') == SupportCompleted::class)
                            ->numeric()
                            ->mask(
                                fn(Forms\Components\TextInput\Mask $mask) => $mask
                                    ->numeric()
                                    ->decimalPlaces(2) // Set the number of digits after the decimal point.
                                    ->decimalSeparator(',') // Add a separator for decimal numbers.
                                    ->mapToDecimalSeparator([',']) // Map additional characters to the decimal separator.
                                    ->minValue(0) // Set the minimum value that the number can be.
                                    ->normalizeZeros() // Append or remove zeros at the end of the number.
                                    ->padFractionalZeros() // Pad zeros at the end of the number to always maintain the maximum number of decimal places.
                                    ->thousandsSeparator('.') // Add a separator for thousands.
                            )
                            ->label('Eksik Parça/Arıza Bedeli'),

                        //                        Forms\Components\TextInput::make('tracking_url')
                        //                            ->label('Takip URL')
                        //                            ->url()
                        //                            ->maxLength(255),
                        //                        Forms\Components\TextInput::make('cargo_receiver_name')
                        //                            ->label('Kargo Teslim Alan Adı')
                        //                            ->required()
                        //                            ->maxLength(255),
                    ]
                ),
        ];
    }

    public function render()
    {
        return view('livewire.returned-product-has-shipped-table');
    }
}
