<?php

namespace App\Http\Livewire;

use App\Models\Order\Order;
use App\Models\Product\ProductStock;
use App\Models\SupportRequest;
use App\Services\ReturnProduct\ReturnProductService;
use App\States\SupportRequest\SupportProductWaiting;
use Filament\Forms;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Component;
use Illuminate\Support\Str;

class ReturnedProductToBeShippedTable extends Component implements Tables\Contracts\HasTable
{

    use Tables\Concerns\InteractsWithTable;

    protected function getTableQuery(): Builder
    {
        return SupportRequest::query()
            ->withoutGlobalScopes([
                //                SoftDeletingScope::class,
            ])
            ->whereIn('support_requests.status', [
                SupportProductWaiting::class,
            ]);
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('id')->label('Des. Talep No')
                ->size('sm')
                ->alignCenter()
                ->fontFamily('mono'),
            Tables\Columns\ImageColumn::make('product.product.getFirstPhoto')
                ->height(120)->width(120)
                ->alignCenter()
                ->label('Ürün Resmi'),
            Tables\Columns\TextColumn::make('product.product.getNameMultiline')->label('Ürün Adı')->weight('medium')
                ->description(fn(SupportRequest $record): string => $record->product->product->getExcerptText)
                ->html()
                ->wrap()
                ->size('sm'),
            Tables\Columns\TextColumn::make('product.getVaariant')->label('Varyant')
                ->size('sm'),
            Tables\Columns\TextColumn::make('productStockItem.sn')->label('SN')
                ->searchable(query: function (Builder $query, string $search): Builder {
                    $productStock = ProductStock::where('sn', 'like', '%' . $search . '%')->first();
                    return $query->where('product_id', $productStock?->product_id)->where('order_id', $productStock?->orderItem?->id);
                }, isIndividual: true)
                ->fontFamily('mono')
                ->size('sm'),
            Tables\Columns\TextColumn::make('order.order_number')->label('Sipariş No')
                ->searchable(query: function (Builder $query, string $search): Builder {
                    $order = Order::where('order_number', $search)->first();
                    return $query->where('order_id', $order->id);
                }, isIndividual: true)
                ->fontFamily('mono')
                ->size('sm'),
            Tables\Columns\TextColumn::make('order.created_at')->label('Sipariş Tarihi')->date()
                ->size('sm'),
            Tables\Columns\TextColumn::make('order.user.full_name')->label('Müşteri Adı')
                ->formatStateUsing(fn(string $state): string => Str::title($state))
                ->size('sm'),
        ];
    }

    protected function getTableActions(): array
    {
        return [
            Action::make('Ürün Depoya Ulaştı Mı?')
                //->label(fn(OrderItems $record): ?string => 'Manuel Kargo Bilgisi Girişi')
                ->disabled(fn(SupportRequest $record) => $record->is_product_received === 1)
                ->color('danger')
                ->icon('akar-shipping-box-02')
                ->requiresConfirmation()
                //                ->mountUsing(fn(Forms\ComponentContainer $form, SupportRequest $record) => $form->fill([
                //                    'delivered_at' => $record->delivered_at,
                //                    'tracking_url' => $record->tracking_url,
                //                    'cargo_receiver_name' => $record->cargo_receiver_name,
                //                ]))
                ->action(function (SupportRequest $record, array $data): SupportRequest {

                    $prs = new ReturnProductService();
                    $prs->setDeliveredAt($data['delivered_at']);
                    $prs->setSupportRequest($record);
                    $prs->setOrder(\App\Models\Order\Order::find($record->order->id));
                    $prs->setProductVariant($record->product);
                    $prs->handle();

                    // Save the returned product infos
                    //                    $record->saveReturnedProductInfos($data['delivered_at']);
                    // Send notification to the customer
                    //                    $record->order->orderItems()->where('product_id', $record->product_id)->first()->sendProductReturnSmsAndEmail();

                    //                    $orderStatusByCargoService = new UpdateOrderStatusByCargo(OrderItem::find($record->id));
                    //                    if ($orderStatusByCargoService->updateOrderStatusIfSuitable()) {
                    //                        $record->refresh();
                    //                    }
                    return $record;
                })
                ->form(
                    [
                        Forms\Components\DateTimePicker::make('delivered_at')
                            ->label('Teslim Alma Tarihi')
                            ->default(now())
                            ->required(),
                        //                        Forms\Components\TextInput::make('tracking_url')
                        //                            ->label('Takip URL')
                        //                            ->url()
                        //                            ->maxLength(255),
                        //                        Forms\Components\TextInput::make('cargo_receiver_name')
                        //                            ->label('Kargo Teslim Alan Adı')
                        //                            ->required()
                        //                            ->maxLength(255),
                    ]
                ),
        ];
    }

    public function render()
    {
        return view('livewire.returned-product-to-be-shipped-table');
    }
}
