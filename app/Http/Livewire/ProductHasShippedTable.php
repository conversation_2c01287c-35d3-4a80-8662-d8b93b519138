<?php

namespace App\Http\Livewire;

use App\Models\Order\OrderItem;
use App\Models\OrderItems;
use App\Services\Cargo\UpdateOrderStatusByCargo;
use App\States\Order\OrderApproved;
use Filament\Forms;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\Position;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Livewire\Component;

class ProductHasShippedTable extends Component implements Tables\Contracts\HasTable
{

    use Tables\Concerns\InteractsWithTable;

    protected function getTableQuery(): Builder
    {
        return OrderItems::query()
            ->withoutGlobalScopes([
                //                SoftDeletingScope::class,
            ])
            ->leftJoin('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereIn('orders.status', [
                OrderApproved::class,
            ])
            ->where('order_items.is_user_suitable_control', 1)
            ->where('order_items.is_purchased', 1)
            ->whereNotNull('order_items.invoice_number')
            ->whereNull('order_items.cancelled_at')
            ->whereNull('order_items.deleted_at')
            ->select('order_items.*')
            ->orderBy('order_items.id', 'desc')
            ->with('product.product', 'product.options', 'product.options.productOptionValue');
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('invoice_number')->label('İrsaliye No')->size('sm'),
            Tables\Columns\ImageColumn::make('product.product.getFirstPhoto')
                ->height(120)
                ->label('Ürün Resmi'),

            Tables\Columns\TextColumn::make('product.product.getNameMultiline')->label('Ürün Adı')->weight('medium')->size('sm')
                ->description(fn(OrderItems $record): string => Str::of($record->product?->product?->getExcerptText ?? $record->product)->split(70)->join('<br>'))
                ->html(),

            Tables\Columns\TextColumn::make('product.getVaariant')->label('Varyant')->size('sm'),

            Tables\Columns\TextColumn::make('order.order_number')->label('Sipariş No')->size('sm')->fontFamily('mono'),
            Tables\Columns\TextColumn::make('order.created_at')->label('Sipariş Tarihi')->date('d.m.Y')->size('sm'),
            Tables\Columns\TextColumn::make('order.finance_approved_at')->label('Finans Onay Tarihi')->date('d.m.Y')->size('sm'),
            Tables\Columns\TextColumn::make('order.user.full_name')->label('Müşteri Adı')->size('sm')
                ->formatStateUsing(fn(string $state): string => Str::of($state)->title())
                ->wrap(),
            Tables\Columns\TextColumn::make('cargo_receiver_name')->label('Kargo')->size('sm')->wrap(),
            Tables\Columns\TextColumn::make('plan')->label('Kiralama Süresi')
                ->size('sm')
                ->getStateUsing(function (OrderItems $record): null|string {
                    return $record->planObj->name;
                })
                ->alignRight()
                ->size('sm'),
            Tables\Columns\TextColumn::make('order.total')
                ->label('Kira Tutarı')
                ->alignRight()
                ->formatStateUsing(fn(string $state): string => number_format($state, 2, ',', '.') . ' ₺')
                ->fontFamily('mono')
                ->size('sm'),
            Tables\Columns\TextColumn::make('quantity')->label('Adet')->size('sm'),
            //            Tables\Columns\TextColumn::make('id')->label('Adet1'),
        ];
    }

    protected function getTableActions(): array
    {
        return [
            Action::make('Kargo Etiketi Yazdır')
                ->color('danger')
                ->icon('monoicon-print')
                ->disabled(fn(OrderItems $record) => $record->invoice_number == null)
                //->url(fn(OrderItems $record): string => '/admin/product-stocks/create?product_id=' . $record->product_id . '&order_item_id=' . $record->id),
                ->url(fn(OrderItems $record): string => '/irsaliye/' . $record->id, true),

            Tables\Actions\ActionGroup::make([
                Action::make('cargo')
                    ->label(fn(OrderItems $record): ?string => 'Kargo Entegrasyona Verileri Yeniden Gönder')
                    ->color('danger')
                    ->icon('akar-shipping-box-02')
                    ->requiresConfirmation()
                    ->action(function (OrderItems $record, array $data): OrderItems {

                        $cargoNumber = Str::of($record->order->id)->padLeft(7, 0)->prepend('5')->append($record->id);
                        // Kargo veri aktarımı
                        $yurtici = new \App\Services\Cargo\YurticiKargo([
                            'username' => '1048N815146906G',
                            'password' => 'Kk51nUrK2wpBi08a',
                            'test' => false //TEST MODE true / false
                        ]);
                        //
                        $kargoYolla = $yurtici->createCargo([
                            'cargoKey' => $cargoNumber,
                            'invoiceKey' => $record->invoice_number,
                            'receiverCustName' => $record->order->user->address->first_name . ' ' . $record->order->user->address->last_name,
                            'receiverAddress' => $record->order->user->address->address . ' ' . $record->order->user->address->county . ' ' . $record->order->user->address->city,
                            'receiverPhone1' => $record->order->user->address->phone,
                        ]);

                        return $record;
                    })
                    ->form([]),

                Action::make('cargo_info')
                    ->label(fn(OrderItems $record): ?string => 'Manuel Kargo Bilgisi Girişi')
                    ->color('success')
                    ->icon('akar-shipping-box-02')
                    ->requiresConfirmation()
                    ->mountUsing(fn(Forms\ComponentContainer $form, OrderItems $record) => $form->fill([
                        'delivered_at' => $record->delivered_at,
                        'tracking_url' => $record->tracking_url,
                        'cargo_receiver_name' => $record->cargo_receiver_name,
                    ]))
                    ->action(function (OrderItems $record, array $data): OrderItems {

                        $record->update([
                            'cargo_receiver_name' => $data['cargo_receiver_name'],
                            'tracking_url' => $data['tracking_url'],
                            'delivered_at' => $data['delivered_at'],
                        ]);

                        $orderStatusByCargoService = new UpdateOrderStatusByCargo(OrderItem::find($record->id));
                        if ($orderStatusByCargoService->updateOrderStatusIfSuitable()) {
                            $record->refresh();
                        }
                        return $record;
                    })
                    ->form(
                        [
                            Forms\Components\DateTimePicker::make('delivered_at')
                                ->label('Teslim Tarihi')
                                ->required(),
                            Forms\Components\TextInput::make('tracking_url')
                                ->label('Takip URL')
                                ->url()
                                ->maxLength(255),
                            Forms\Components\TextInput::make('cargo_receiver_name')
                                ->label('Kargo Teslim Alan Adı')
                                ->required()
                                ->maxLength(255),
                        ]
                    ),
            ]),

        ];
    }

    protected function getTableActionsPosition(): ?string
    {
        return Position::BeforeCells;
    }

    public function render()
    {
        return view('livewire.product-has-shipped-table');
    }
}
