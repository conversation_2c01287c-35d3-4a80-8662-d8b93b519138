<?php

namespace App\Rules;

use App\Models\Cart\Cart;
use App\Models\Coupon;
use Illuminate\Database\Eloquent\Collection;

class CalculateCouponDiscount
{

    private Cart $cart;
    private Coupon $coupon;
    private Collection $cartItems;
    private Collection $eligableCartItems;
    private float $discount = 0;

    public function __construct(Cart $cart, Coupon $coupon)
    {
        $this->cart = $cart;
        $this->coupon = $coupon;
        $this->cartItems = $cart->items;
    }

    public function calculateDiscount()
    {
        // Hangi ürünlerin kupona uygun olduğunu bul sonra onları topla
        $this->eligableCartItems = $this->cartItems;
        // İndirim tanımlı olan ürünlere kupon uygulanmaz
//        $this->eligableCartItems = $this->eligableCartItems->filter(function ($cartItem) {
//            return $cartItem->product->prices->max('compare_price')->value == 0;
//        });

        if ($this->coupon->is_subscription_months_rules_enabled) {
            $this->eligableCartItems = $this->eligableCartItems->filter(function ($cartItem) {
                return match ($this->coupon->rule_operator) {
                    '=' => $cartItem->month == $this->coupon->subscription_months_id,
                    '<' => $cartItem->month < $this->coupon->subscription_months_id,
                    '>' => $cartItem->month > $this->coupon->subscription_months_id,
                    '>=' => $cartItem->month >= $this->coupon->subscription_months_id,
                    '<=' => $cartItem->month <= $this->coupon->subscription_months_id,
                };
            });
        }

        if ($this->coupon->is_category_coupon) {
            // Kuponun kategorileri
            $couponCategories = $this->coupon->categories()->pluck('category_id');
            $this->eligableCartItems = $this->eligableCartItems->filter(function ($cartItem) use ($couponCategories) {
                return $cartItem->product->product->collections->pluck('id')->intersect($couponCategories)->isNotEmpty();
            });
        }

        if ($this->coupon->is_product_coupon) {
            // Kuponun geçerli olduğu ürünler
            $couponProducts = $this->coupon->products()->pluck('products.id');
            $this->eligableCartItems = $this->eligableCartItems->filter(function ($cartItem) use ($couponProducts) {
                //dd(collect($cartItem->product->product->id)->intersect([72]));
                //dump($cartItem->product->product->id, $couponProducts);
                return collect($cartItem->product->product->id)->intersect($couponProducts)->isNotEmpty();
            });
        }

        if ($this->coupon->is_disabled_on_discounted_products) {
            // Kuponun geçerli olduğu ürünler
            $this->eligableCartItems = $this->eligableCartItems->filter(function ($cartItem) {
                return $cartItem->product->prices->where('subscription_months_id', $cartItem->month)->first()->compare_price->value == 0;
            });
        }

//        dd($this->eligableCartItems->pluck('id'));

        if ($this->coupon->type == 'fixed' && $this->eligableCartItems->count() > 0) {
            $this->discount = $this->coupon->value;
        } elseif ($this->coupon->type == 'rate') {
            $this->discount = $this->eligableCartItems->sum('total') * ($this->coupon->value / 100);
        }

    }

    /**
     * @return float
     */
    public function getDiscount(): float
    {
        return $this->discount;
    }

    /**
     * @return Collection
     */
    public function getEligableCartItems(): Collection
    {
        return $this->eligableCartItems;
    }

}
