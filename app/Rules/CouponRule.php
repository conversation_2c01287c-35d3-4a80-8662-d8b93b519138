<?php

namespace App\Rules;

use App\Models\Cart\Cart;
use App\Models\Coupon;
use App\States\Order\OrderApproved;
use Illuminate\Contracts\Validation\Rule;

class CouponRule implements Rule
{
    protected $validationMessage;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(protected Cart $cart)
    {
        //
    }

    /**
     * Set the validation message in passes method.
     *
     * @param string $message
     *
     * @return $this
     */
    protected function setValidationMessage(string $message)
    {
        $this->validationMessage = $message;

        return $this;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {

        $coupon = Coupon::whereCode($value)->first();

        foreach (['hasCoupon', 'reachedLimit', 'expired', 'minCartAmount', 'maxCartAmount', 'userMaxCoupponUsability', 'newRegisteredUser'] as $check) {
            $method = 'ensure' . ucfirst($check);
            logger('passes', [$method, $coupon?->code]);

            if ($this->$method($coupon) === false) {
                return false;
            }
        }

        return true;
    }

    protected function ensureNewRegisteredUser($coupon)
    {
        if ($coupon->is_new_user_coupon == 1 && $this->cart->user->orders()->whereNot('status', OrderApproved::class)->count() > 0) {
            $this->setValidationMessage(__('Bu kupon yeni üyelere özeldir.'));
            return false;
        }
    }

    // Bu kural şu anda kural olarak değil de indirime yansıma durumu olarak revize edildi.
    protected function ensureCategoryCoupon($coupon)
    {
        // Kupon yok veya kategori kuponu değilse
        if (!$coupon || $coupon->is_category_coupon == 0) {
            //$this->setValidationMessage(__('Kupon bulunamadı.'));
            return false;
        }

        // Kategori kuponu ise
        if ($coupon->is_category_coupon == 1) {
            // Kuponun kategorileri
            $couponCategories = $coupon->categories()->pluck('category_id');
            // Sepetteki ürünlerin kategorileri
            $cartCategories = $this->cart->items->map(fn($x) => $x->product->product->collections->pluck('id'));

            // Kupon kategorileri ile sepet kategorileri kesişiyorsa
            if (count(array_intersect($couponCategories, $cartCategories)) > 0) {
                return true;
            } else {
                $this->setValidationMessage(__('Kuponun geçerli olduğu bir kategori bulunamadı.'));
                return false;
            }
        }

    }

    /**
     * Ensure if the given coupon is not empty.
     *
     * @param Coupon|null $coupon
     * @return bool|void
     */
    protected function ensureHasCoupon($coupon)
    {
        if (!$coupon || !$coupon->getOriginal('published')) {
            $this->setValidationMessage(__('Kupon bulunamadı.'));

            return false;
        }
    }

    /**
     * Ensure if the given coupon is reached limit.
     *
     * @param Coupon $coupon
     * @return bool|void
     */
    protected function ensureReachedLimit($coupon)
    {
        $usedCoupon = $coupon->orders()->count();

        if ($usedCoupon >= $coupon->limit) {
            $this->setValidationMessage(__('Kullanım limiti aşıldı'));

            return false;
        }
    }

    protected function ensureUserMaxCoupponUsability($coupon)
    {
        $usedCoupon = $coupon->orders()->where('user_id', $this->cart->user_id)->count();

        if ($usedCoupon >= $coupon->customer_usage_limit) {
            $this->setValidationMessage(__('Kullanıcı Kullanım limiti aşıldı'));

            return false;
        }
    }

    /**
     * Ensure if the given coupon is expired.
     *
     * @param Coupon $coupon
     * @return bool|void
     */
    protected function ensureExpired($coupon)
    {
        $now = now();

        if ($now->lt($coupon->start_date) || $now->gt($coupon->due_date)) {
            $this->setValidationMessage(__('Son kullanma tarihi geçti'));

            return false;
        }
    }

    /**
     * Ensure if the given coupon is require min cart amount.
     *
     * @param Coupon $coupon
     * @return bool|void
     */
    protected function ensureMinCartAmount($coupon)
    {
        if ($coupon->min_cart_amount && $coupon->min_cart_amount > $this->cart->sub_total) {
            $this->setValidationMessage(__('Minimum sepet tutarı :amount olmalıdır.', [
                'amount' => $coupon->min_cart_amount,
                'total' => $this->cart->sub_total,
            ]));

            return false;
        }
    }

    /**
     * Ensure if the given coupon is require max cart amount.
     *
     * @param Coupon $coupon
     * @return bool|void
     */
    protected function ensureMaxCartAmount($coupon)
    {
        if ($coupon->max_cart_amount && $coupon->max_cart_amount < $this->cart->sub_total) {
            $this->setValidationMessage(__('Maximum sepet tutarı :amount olmalıdır.', [
                'amount' => $coupon->max_cart_amount,
                'total' => $this->cart->sub_total,
            ]));

            return false;
        }
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return $this->validationMessage;
    }
}
