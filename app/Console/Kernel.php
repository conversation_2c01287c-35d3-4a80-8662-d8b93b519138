<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        //        $schedule->command('iyzipay:subscription_charge')->withoutOverlapping()->dailyAt('08:00');
        $schedule->command('ko:createDailyInvoices')->withoutOverlapping()->dailyAt('07:00')->emailOutputOnFailure('<EMAIL>');
        $schedule->command('ko:sendPaymentDelayEmail')->withoutOverlapping()->dailyAt('11:04')->emailOutputOnFailure('<EMAIL>');
        $schedule->command('ko:sendPaymentDelaySMS')->withoutOverlapping()->dailyAt('11:05')->emailOutputOnFailure('<EMAIL>');
        $schedule->command('ko:sendOfficalDelayEmail')->withoutOverlapping()->dailyAt('08:01')->emailOutputOnFailure('<EMAIL>');
        $schedule->command('ko:verimorIYSSendNewRegisteredUsers')->withoutOverlapping()->dailyAt('02:05')->emailOutputOnFailure('<EMAIL>');
        // check for unpaid transactions, search for another solution for timing
        $schedule->command('iyzipay:getFirstUnpaidTransactionByOrderStatus')->withoutOverlapping()->dailyAt('09:00');
        $schedule->command('iyzipay:getFirstUnpaidTransactionByOrderStatus')->withoutOverlapping()->dailyAt('12:00');
        $schedule->command('iyzipay:getFirstUnpaidTransactionByOrderStatus')->withoutOverlapping()->dailyAt('15:00');
        $schedule->command('iyzipay:getFirstUnpaidTransactionByOrderStatus')->withoutOverlapping()->dailyAt('19:00');
        $schedule->command('iyzipay:getFirstUnpaidTransactionByOrderStatus')->withoutOverlapping()->dailyAt('23:00');

        $schedule->command('ko:removeDeletedVariantsFromCartItems')->withoutOverlapping()->everyFiveMinutes()->emailOutputOnFailure('<EMAIL>');
        $schedule->command('ko:removeDeletedFavouriteProduct')->withoutOverlapping()->everyFourHours()->emailOutputOnFailure('<EMAIL>');
        $schedule->command('ko:removeOutOfStockProductFromCartItems')->withoutOverlapping()->everyFiveMinutes()->emailOutputOnFailure('<EMAIL>');
        $schedule->command('ko:removeExpiredCouponsFromCarts')->withoutOverlapping()->everyFiveMinutes()->emailOutputOnFailure('<EMAIL>')->appendOutputTo(storage_path('logs/remove-expired-coupons-from-carts.log'));

        $schedule->command('ko:prepareGoogleSitemap')->withoutOverlapping()->dailyAt('03:10')->emailOutputOnFailure('<EMAIL>');
        // $schedule->command('ko:prepareTeknosaXML')->withoutOverlapping()->dailyAt('03:15')->emailOutputOnFailure('<EMAIL>');
        $schedule->command('ko:prepareKlavioProductXML')->withoutOverlapping()->dailyAt('03:25')->emailOutputOnFailure('<EMAIL>');
        $schedule->command('ko:closeTimeoutedReturnCargoRequests')->withoutOverlapping()->dailyAt('03:30')->emailOutputOnFailure('<EMAIL>');
        $schedule->command('ko:calculateUserReportMetriks')->withoutOverlapping()->dailyAt('04:20')->emailOutputOnFailure('<EMAIL>');

        //        $schedule->command('ko:calculateRentalCount')->withoutOverlapping()->dailyAt('03:20');
        $schedule->command('ko:updateActiveSupportRequestStatusFromHelpScout')->withoutOverlapping()->hourlyAt(22);
        $schedule->command('ko:updateCargoStatus')->withoutOverlapping()->hourly()
            ->unlessBetween('19:00', '8:00')
            ->days([1, 2, 3, 4, 5, 6])
            ->emailOutputTo('<EMAIL>');

        $schedule->command('horizon:snapshot')->everyFiveMinutes();
        $schedule->command('ko:getStampedIoCommentsForProducts')->withoutOverlapping()->weeklyOn(6, '04:00')->emailOutputOnFailure('<EMAIL>');
        $schedule->command('ko:undeliveredCargoReport')->withoutOverlapping()->weeklyOn(1, '07:00')->emailOutputOnFailure('<EMAIL>');
        $schedule->command('ko:cantInvoicedOrdersReport')->withoutOverlapping()->dailyAt('07:01')->emailOutputOnFailure('<EMAIL>');
        // $schedule->command('ko:compareInventoryAndStockAmount')->withoutOverlapping()->dailyAt('07:02')->emailOutputOnFailure('<EMAIL>');

        $schedule->command('backup:clean')->withoutOverlapping()->weeklyOn(6, '02:13'); // clean up old backups
        $schedule->command('backup:run --only-db')->withoutOverlapping()->everyFifteenMinutes(); // backup database every 15 minutes

        $schedule->command('ko:sendSMSToNewUsersDontHaveAnyOrder')->withoutOverlapping()->dailyAt('09:30')->emailOutputOnFailure('<EMAIL>'); // send sms to new users dont have any order
        $schedule->command('ko:sendPegasusRewardsIfOrderSuitable')->withoutOverlapping()->dailyAt('10:05')->emailOutputOnFailure('<EMAIL>'); // send pegasus rewards if order suitable

        $schedule->command('xml:abonesepeti')
            ->dailyAt('04:40')
            ->emailOutputOnFailure('<EMAIL>')
            ->appendOutputTo(storage_path('logs/abonesepeti-xml.log'));

        $schedule->command('report:sync-products')
            ->dailyAt('05:00')
            ->emailOutputOnFailure('<EMAIL>')
            ->appendOutputTo(storage_path('logs/report-sync-products.log'))
            ->withoutOverlapping();

        $schedule->command('xml:createGoogleAdWordsXml')
            ->dailyAt('05:16')
            ->emailOutputOnFailure('<EMAIL>')
            ->appendOutputTo(storage_path('logs/google-adwords-xml.log'))
            ->withoutOverlapping();

        $schedule->command('ko:purgeCDNCache')
            ->dailyAt('04:55')
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/purge-cdn-cache.log'))
            ->emailOutputOnFailure('<EMAIL>');

        $schedule->command('b2b:calculate-order-parameters')
            ->dailyAt('05:00')
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/calculate-order-parameters.log'))
            ->emailOutputOnFailure('<EMAIL>');

        // $schedule->command('products:sync-from-alba-xml')->dailyAt('02:00')->withoutOverlapping()->emailOutputOnFailure('<EMAIL>')->appendOutputTo(storage_path('logs/sync-alba-xml.log'));
        $schedule->command('products:sync-from-aynet-xml')->dailyAt('02:30')->withoutOverlapping()->emailOutputOnFailure('<EMAIL>')->appendOutputTo(storage_path('logs/sync-aynet-xml.log'));
        $schedule->command('xml:createAynetProductsXml')->dailyAt('02:55')->withoutOverlapping()->emailOutputOnFailure('<EMAIL>')->appendOutputTo(storage_path('logs/aynet-products-xml.log'));

        // Log rotasyon ve yedekleme komutu
        $schedule->command('logs:rotate-backup')
            ->dailyAt(config('log-backup.schedule.daily_at', '03:00'))
            ->when(config('log-backup.schedule.on_one_server', true), function ($schedule) {
                return $schedule->onOneServer();
            })
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/log-rotation.log'))
            ->emailOutputOnFailure(config('log-backup.admin_email', '<EMAIL>'));

        $schedule->command('scoring:check-new-orders')->everyMinute()->withoutOverlapping(); // Deprecated: Doğrudan redis üzerine sipariş aktarımı iptal edildi. yakında bu komutu kaldıracağız.
        //$schedule->command('scoring:check-new-orders')->everyMinute()->withoutOverlapping();

        // 5 günden eski Findex taleplerini işle
        $schedule->command('scoring:process-old-findex-requests')
            ->dailyAt('06:00')
            ->withoutOverlapping()
            ->emailOutputOnFailure('<EMAIL>')
            ->appendOutputTo(storage_path('logs/process-old-findex-requests.log'));

        // Tamamlanan siparişler için anket maili gönderme
        //        $schedule->command('ko:sendCompletedOrderSurveyEmails')
        //            ->withoutOverlapping()
        //            ->dailyAt('11:00')
        //            ->emailOutputOnFailure('<EMAIL>')
        //            ->appendOutputTo(storage_path('logs/completed-order-survey-emails.log'));

        // Stok senkronizasyonu - Ana depo stokları ile ProductVariant stocks senkronizasyonu
        $schedule->command('stocks:sync-product-variants')
            ->everyMinute()
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/stock-sync-cron.log'))
            ->emailOutputOnFailure('<EMAIL>');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
