<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use App\Models\ScoringRequest;
use Carbon\Carbon;

class ProcessOldFindexRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scoring:process-old-findex-requests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process Findex requests older than 15 days, add Redis data to scoring request and remove from Redis';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting to process old Findex requests...');

        // Redis'ten müşteri listesini al
        $customersListKey = 'kb-findex-ba:customers';
        $customersJson = Redis::get($customersListKey);

        if (!$customersJson) {
            $this->info('No customers found in Redis.');
            return Command::SUCCESS;
        }

        $customers = json_decode($customersJson, true);
        if (empty($customers)) {
            $this->info('Customer list is empty.');
            return Command::SUCCESS;
        }

        $this->info('Found ' . count($customers) . ' customers in Redis.');

        $processedCount = 0;
        $updatedCustomers = [];

        foreach ($customers as $customer) {
            // findex_requested_at kontrolü
            if (!isset($customer['findex_requested_at']) || is_null($customer['findex_requested_at'])) {
                // Henüz Findex'e gönderilmemiş, listeye ekle
                $updatedCustomers[] = $customer;
                continue;
            }

            // Tarih kontrolü - 15 günden eski mi?
            $findexRequestedAt = Carbon::parse($customer['findex_requested_at']);
            $daysDiff = $findexRequestedAt->diffInDays(now());

            if ($daysDiff < 15) {
                // 15 günden yeni, listeye ekle
                $updatedCustomers[] = $customer;
                $this->line("Customer {$customer['gsm']} - Findex request is {$daysDiff} days old. Keeping in Redis.");
                continue;
            }

            // 15 günden eski - işlem yap
            $this->info("Processing customer {$customer['gsm']} - Findex request is {$daysDiff} days old.");

            // ScoringRequest'i bul
            $scoringRequest = ScoringRequest::where('ulid', $customer['id'])->first();

            if (!$scoringRequest) {
                Log::warning("ProcessOldFindexRequests: ScoringRequest not found for ULID: {$customer['id']}");
                $this->warn("ScoringRequest not found for ULID: {$customer['id']}. Keeping in Redis.");
                // ScoringRequest bulunamadı, Redis'te tut
                $updatedCustomers[] = $customer;
                continue;
            }

            // Mevcut additional_data'yı al
            $additionalData = $scoringRequest->additional_data ?? [];

            // Redis verisini additional_data'ya ekle
            $additionalData['redis_data'] = $customer;
            $additionalData['processed_from_redis_at'] = now()->toISOString();

            // ScoringRequest'i güncelle
            $scoringRequest->update([
                'additional_data' => $additionalData
            ]);

            // Not ekle
            $scoringRequest->notes()->create([
                'content' => "15 günden eski Findex talebi Redis'ten alındı ve additional_data'ya eklendi. Findex talep tarihi: {$customer['findex_requested_at']}"
            ]);

            $this->line("Updated ScoringRequest {$scoringRequest->id} with Redis data.");
            Log::info("ProcessOldFindexRequests: Updated ScoringRequest", [
                'scoring_request_id' => $scoringRequest->id,
                'ulid' => $scoringRequest->ulid,
                'findex_requested_at' => $customer['findex_requested_at'],
                'days_old' => $daysDiff
            ]);

            $processedCount++;
        }

        // Güncellenmiş listeyi Redis'e geri yaz
        if ($processedCount > 0) {
            if (empty($updatedCustomers)) {
                // Tüm müşteriler işlendi, key'i sil
                Redis::del($customersListKey);
                $this->info("All customers processed. Redis key deleted.");
            } else {
                // Kalan müşterileri Redis'e yaz
                Redis::set($customersListKey, json_encode($updatedCustomers, JSON_UNESCAPED_UNICODE));
                $this->info("Updated Redis with " . count($updatedCustomers) . " remaining customers.");
            }
        }

        $this->info("Completed. Processed {$processedCount} old Findex requests.");

        return Command::SUCCESS;
    }
}
