<?php

namespace App\Console\Commands;

use App\Models\Lunar\ProductVariant;
use Illuminate\Console\Command;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use SimpleXMLElement;


class prepareTeknosaXML extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ko:prepareTeknosaXML';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';
    private $serviceProducts;

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set("memory_limit", -1);
        ini_set('max_execution_time', 0); //0=NOLIMIT

        $this->getData();
        Storage::disk('public')->put('services/teknosa.xml', $this->prepareXMLOutput());

        return Command::SUCCESS;
    }

    public function getData()
    {
        $this->serviceProducts = ProductVariant::with(['product' => function (Builder $query) {
            $query->where('status', 'published');
        }])->whereNotNull('teknosa_id')->get();
    }

    public function prepareXMLOutput()
    {
        $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><channel/>');
        foreach ($this->serviceProducts as $product) {
            dump($product->id);

            // Ürünün ana ürünü aktif değil ise o zaman XML içerisinde yer almasın
            if ($product->product) {
                $maxId = $product->prices()->where('price', '>', 0)->max('subscription_months_id');
                $pri = $product->prices()->where('subscription_months_id', $maxId)->first();

                // Ürünün tüm fiyatları 0 ise o zaman XML içerisinde yer almasın
                if ($maxId) {
                    foreach (explode(',', $product->teknosa_id) as $oracleId) {
                        $track = $xml->addChild('item');
                        $track->addChild('id', $product->id);
                        $track->addChild('title', Str::of($product->product->getName)->replace('&', 'and') . ' - ' . $pri->subscriptionMonths->name);
                        $track->addChild('price', $pri->nonObjectPrice);
                        $track->addChild('Oracle_id', trim($oracleId));
                        $track->addChild('Teknosa_link', 'https://kiralabunu.com/urun/' . $product->product->defaultUrl->slug . '-teknosa');
                    }
                }
            }

//            foreach ($product->prices as $item) {
//                $track = $xml->addChild('item');
//                $track->addChild('id', $product->id);
//                $track->addChild('title', Str::of($product->product->getName)->replace('&', 'and') . ' - ' . $item->subscriptionMonths->name);
//                $track->addChild('price', $item->nonObjectPrice);
//                $track->addChild('Oracle_id', $product->teknosa_id);
//                $track->addChild('Teknosa_link', 'https://kiralabunu.com/urun/' . $product->product->defaultUrl->slug . '-teknosa');
//            }
        }
        return $xml->asXML();
    }
}
