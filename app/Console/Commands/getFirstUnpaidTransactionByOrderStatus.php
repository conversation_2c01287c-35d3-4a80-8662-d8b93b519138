<?php

namespace App\Console\Commands;

use App\Events\SubscriptionCouldNotPaid;
use App\Exceptions\Transaction\TransactionSaveException;
use App\Jobs\ExecutePaymentRequest;
use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class getFirstUnpaidTransactionByOrderStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'iyzipay:getFirstUnpaidTransactionByOrderStatus';
    private int $iter = 1;
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->postSlack([
            "text" => "iyzipay:getFirstUnpaidTransactionByOrderStatus çalıştı"
        ]);
        //$this->chargePayables($this->getWillBeChargedPayables());
        $this->getWillBeChargedPayables();
        return Command::SUCCESS;
    }

    private function getWillBeChargedPayables()
    {
        Order::whereIn('status', ['App\States\Order\OrderRenting', 'App\States\Order\OrderAtLegalPursuit'])
            ->chunkById(200, function (\Illuminate\Database\Eloquent\Collection $orders) {
                $ordersWillProceed = $orders->map(fn($x) => $x->orderTransactions()->whereIn('payment_status_id', [2, 6]) // Bekliyor ve Sigorta / Ödenmedi
                    //->where('card_id', '<>', 1)
                    ->orderBy('due_date')
                    ->whereBetween('due_date', ['2000-01-01', today()->endOfDay()])->first())
                    ->filter(fn($x) => $x != null);
                //$this->info('Ödeme Alınacaklar getWillBeChargedPayables: ' . $ordersWillProceed->pluck('id'));
                $this->chargePayables($ordersWillProceed);
            }, $column = 'id');
    }

    private function chargePayables($payables)
    {
        $this->iter++;
        $time = now()->clone()->addMinutes($this->iter);
        $i = 1;

        $this->info('Ödeme Alınacaklar Sayısı: ' . $payables->count());
        //$this->info($payables->pluck('id'));
        $payables->each(function ($payable) use ($time, $i) {
            try {
                // $this->postSlack([
                //     "text" => "Ödeme alınacak: " . $payable->id . " - Due Date: " . $payable->due_date
                // ]);
                ExecutePaymentRequest::dispatch($payable)->delay($time->clone()->addSeconds($i * 10));
                //$payable->order->user->payTransaction($payable);
            } catch (TransactionSaveException $e) {
                event(new SubscriptionCouldNotPaid($payable));
            } catch (\Exception $e) {
                Log::error('Ödeme Alınması Hatası: ' . $e->getMessage());
            }
        });
    }

    private function postSlack($data)
    {
        $text = $data['text'];
        // Slack Kapital Payment CHANNEL
        $res = \Illuminate\Support\Facades\Http::post('*********************************************************************************', ['text' => $text])->body();

        if ($res == 'ok') {
            //$this->info('Slack mesajı başarıyla gönderildi.');
        } else {
            $this->error('Slack mesajı gönderilemedi.');
        }
    }
}
