<?php

namespace App\Console\Commands;

use App\Models\OrderTransaction;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class sendPaymentDelayEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ko:sendPaymentDelayEmail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $ots = OrderTransaction::where('payment_status_id', 2)
            ->whereBetween('due_date', [today()->subDay()->startOfDay(), today()->subDay()->endOfDay()])
            ->whereNull('delay_emailed_at')
            //->whereNotBetween('created_at', [today()->startOfDay(), today()->endOfDay()])
            //->where('card_id', '<>', 1)
            ->get();

        $ots->each(function ($ot) {
            if ($ot->order->status == 'App\States\Order\OrderRenting' && !$ot->order->user->is_company) {
                Mail::driver('tahsilat')->queue(new \App\Mail\PaymentDelayEmail($ot));
                $ot->update(['delay_emailed_at' => now()]);
            }
        });

        $reminderOts = OrderTransaction::where('payment_status_id', 2)
            ->whereBetween('due_date', [today()->subDays(3)->startOfDay(), today()->subDays(3)->endOfDay()])
            ->whereNotNull('delay_emailed_at')
            ->whereNull('delay_reminder_emailed_at')
            ->get();

        $reminderOts->each(function ($ot) {
            if ($ot->order->status == 'App\States\Order\OrderRenting' && !$ot->order->user->is_company) {
                Mail::driver('tahsilat')->queue(new \App\Mail\PaymentDelayReminderEmail($ot));
                $ot->update(['delay_reminder_emailed_at' => now()]);
            }
        });

        return Command::SUCCESS;
    }
}
