<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Str;

class prepareMassDiscountCoupon extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    //protected $signature = 'ko:prepareMassDiscountCoupon {--count} {--amount} {--description} {--start} {--end} {--type} {--target} {--minSubscriptionMonthsId}';
    protected $signature = 'ko:prepareMassDiscountCoupon';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
//        $count = $this->option('count');
//        $amount = $this->option('amount');
//        $description = $this->option('description');
//        $start = $this->option('start');
//        $end = $this->option('end');
//        $type = $this->option('type');
//        $target = $this->option('target');
//        $minSubscriptionMonthsId = $this->option('minSubscriptionMonthsId');

        for ($i = 0; $i < 3000; $i++) {
            $this->createCoupon();
        }

        return Command::SUCCESS;
    }

    private function createCoupon()
    {
        $coupon = \App\Models\Coupon::create([
            'code' => Str::random(6),
            'description' => 'İnterlink ilk ay % 10 indirim kuponu',
            'type' => 'rate',
            'value' => 10,
            'limit' => 1,
            'min_cart_amount' => 0,
            'max_cart_amount' => 999999,
            'effected_months' => 1,
            'is_subscription_months_rules_enabled' => 0,
            'rule_operator' => '>=',
            'customer_usage_limit' => 1,
            'is_new_user_coupon' => 0,
            'subscription_months_id' => 2,
            'published' => 1,
            'start_date' => '2024-09-01',
            'due_date' => '2024-12-01',
        ]);
    }
}
