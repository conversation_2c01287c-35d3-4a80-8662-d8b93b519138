<?php

namespace App\Console\Commands;

use App\Models\Lunar\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use SimpleXMLElement;

class prepareKlavioProductXML extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ko:prepareKlavioProductXML';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';
    private $serviceProducts;

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set("memory_limit", -1);
        ini_set('max_execution_time', 0); //0=NOLIMIT

        $this->getData();
        Storage::disk('public')->put('services/klavio-product.xml', $this->prepareXMLOutput());

        return Command::SUCCESS;
    }

    private function prepareXMLOutput()
    {
        $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><channel/>');
        foreach ($this->serviceProducts as $product) {
            //$id, $title, $description, $link, $image_link

            // Skip products without a default URL
            if (!$product->defaultUrl) {
                $this->warn("Skipping product ID {$product->id}: No default URL found");
                continue;
            }

            $track = $xml->addChild('item');
            $track->addChild('id', $product->id);
            $track->addChild('title', Str::of($product->getName)->replace('&', 'and'));
            //$track->addChild('description', '<![CDATA[' . $product->getExcerptText . ']]>');
            //$track->addChild('description', htmlentities($product->getExcerptText));
            $track->addChild('description', Str::of($product->getExcerptText)->replace('&', 'and'));
            $track->addChild('link', 'https://kiralabunu.com/urun/' . $product->defaultUrl->slug);
            $track->addChild('image_link', $product->firstPhotoUrl);
        }
        return $xml->asXML();
    }

    private function getData()
    {
        $this->serviceProducts = Product::whereNull('deleted_at')
            ->with('defaultUrl')
            ->get();
    }
}
