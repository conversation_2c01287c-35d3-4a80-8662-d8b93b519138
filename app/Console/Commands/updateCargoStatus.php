<?php

namespace App\Console\Commands;

use App\Models\Order\OrderItem;
use App\Services\Cargo\UpdateOrderStatusByCargo;
use App\Services\Cargo\YurticiKargo;
use App\Services\Parasut\Parasut;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class updateCargoStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ko:updateCargoStatus';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $yurtici = new YurticiKargo([
            'username' => '1048N815146906G',
            'password' => 'Kk51nUrK2wpBi08a',
            'test' => false //TEST MODE true / false
        ]);

        $ots = OrderItem::whereNotNull('invoice_number')
            ->where('id', '>', 16215)
            ->whereNull('delivered_at')
            ->get();

        foreach ($ots as $ot) {
            $this->info($ot->invoice_number);
            $cargoNumber = $ot->invoice_number;

            if ($ot->cargo_at->gt(\Carbon\Carbon::parse('2023-06-20'))) {
                $cargoNumber = Str::of($ot->order->id)->padLeft(7, 0)->prepend('5')->append($ot->id);
            }
            $kargoYolla = $yurtici->cargoStatus([
                //'cargoKeys' => $ot->invoice_number,
                'cargoKeys' => $cargoNumber,
                'onlyTracking' => false
            ]);
            //dd($kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO->operationMessage);
            // Teslim Edilmiş Kargo
            //dump($kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO);

            // Eğer hata var ise
            if (property_exists($kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO, 'errCode')) {
                $ot->cargo_receiver_name = $kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO->errMessage;
                $ot->save();
            }

            if (property_exists($kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO, 'operationCode')) {

                // Kargo işlem görmemiş
                if ($kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO->operationCode == 0) {
                    $ot->cargo_receiver_name = $kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO->operationMessage;
                    $ot->save();
                }

                // Kargo dağıtımda
                if ($kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO->operationCode == 1) {

                    if (!$ot->tracking_url) {
                        Mail::queue(new \App\Mail\CargoDetail($ot));
                    }

                    $ot->cargo_receiver_name = $kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO->operationMessage;
                    $ot->tracking_url = $kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO->shippingDeliveryItemDetailVO->trackingUrl;
                    $ot->save();
                }

                // Teslim Edilmiş Kargo
                if ($kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO->operationCode == 5) {

                    // Kargo teslim edildi ise önce teslim edilen ürün için bilgileri güncelle

                    $ot->cargo_receiver_name = $kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO->shippingDeliveryItemDetailVO->receiverInfo;
                    $ot->tracking_url = $kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO->shippingDeliveryItemDetailVO->trackingUrl;
                    $ot->delivered_at = \Carbon\Carbon::parse($kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO->shippingDeliveryItemDetailVO->deliveryDate . ' ' . $kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO->shippingDeliveryItemDetailVO->deliveryTime);
                    $ot->save();

                    // Sipariş statü güncelle
                    //                    $approvedTotal = $ot->order->items->filter(function ($item) {
                    //                        return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1;
                    //                    })->count();
                    //
                    //                    $shipped = $ot->order->items->filter(function ($item) {
                    //                        return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1 && $item->cargo_at != null;
                    //                    })->count();
                    //
                    //                    if ($approvedTotal == $shipped) {
                    //                        $ot->order()->update([
                    //                            'status' => OrderRenting::class,
                    //                        ]);
                    //                    }

                    $orderStatusByCargoService = new UpdateOrderStatusByCargo($ot);
                    if ($orderStatusByCargoService->updateOrderStatusIfSuitable()) {
                        $this->info('Sipariş statü güncellenmeli ' . $ot->order->id);

                        // Parasut ödeme gönderimi
                        \App\Jobs\sendPaymentToParasut::dispatch($ot->order->orderTransactions->first())->delay(now()->addMinutes(30));

                        // Teslim edilen için ilk dönem fatura oluştur
                        $parasut = new Parasut();
                        $parasut->createRentingInvoice($ot->order->orderTransactions->first());
                    }

                    Storage::disk('s3')->put('cargo/' . $ot->invoice_number . '.json', json_encode($kargoYolla, JSON_UNESCAPED_UNICODE));
                }
            }

            //            $ot->cargo_receiver_name = $kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO->operationMessage ?? $kargoYolla->ShippingDeliveryVO->shippingDeliveryDetailVO->errMessage;
            //            $ot->save();
        }

        return Command::SUCCESS;
    }
}
