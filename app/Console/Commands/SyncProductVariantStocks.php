<?php

namespace App\Console\Commands;

use App\Models\Lunar\ProductVariant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Exception;

class SyncProductVariantStocks extends Command
{
    protected $signature = 'stocks:sync-product-variants 
                          {--dry-run : Run without making changes}';

    protected $description = 'Ana depodaki stok adetlerini ProductVariant stocks kolonu ile senkronize eder';

    public function handle()
    {
        $startTime = microtime(true);
        $isDryRun = $this->option('dry-run');

        Log::channel('stock-sync')->info('Stok senkronizasyonu başladı', [
            'dry_run' => $isDryRun,
            'timestamp' => now()
        ]);

        if ($isDryRun) {
            $this->info('🔍 DRY RUN modu - hiçbir değişiklik yapılmayacak');
        }

        try {
            // 1. Stok takipli ürünleri al (sadece purchasable = 'in_stock' olanlar)
            $this->info('📦 Stok takipli ürünler getiriliyor...');
            $stockManagedVariants = ProductVariant::where('purchasable', 'in_stock')
                ->whereNull('deleted_at')
                ->pluck('id')
                ->toArray();

            $stockManagedCount = count($stockManagedVariants);
            $this->info("✅ {$stockManagedCount} adet stok takipli ürün bulundu");

            Log::channel('stock-sync')->info("Stok takipli ürün sayısı: {$stockManagedCount}");

            if (empty($stockManagedVariants)) {
                $this->warn('⚠️  Stok takipli ürün bulunamadı');
                Log::channel('stock-sync')->warning('Stok takipli ürün bulunamadı');
                return 0;
            }

            // 2. Ana depo stoklarını hesapla (inventory_id = 1)
            $this->info('🏪 Ana depo stokları hesaplanıyor...');
            $mainWarehouseStocks = DB::table('product_stocks')
                ->where('inventory_id', 1)
                ->where('product_type', 'App\\Models\\Lunar\\ProductVariant')
                ->where('is_out_of_order', false)
                ->where('is_reserved', false)
                ->where('is_fixture', false)
                ->whereNull('deleted_at')
                ->whereIn('product_id', $stockManagedVariants)
                ->groupBy('product_id')
                ->selectRaw('product_id, COUNT(*) as stock_count')
                ->pluck('stock_count', 'product_id')
                ->toArray();

            $mainWarehouseProductCount = count($mainWarehouseStocks);
            $this->info("✅ Ana depoda {$mainWarehouseProductCount} ürün için stok bilgisi bulundu");

            Log::channel('stock-sync')->info("Ana depo ürün sayısı: {$mainWarehouseProductCount}", [
                'products_with_stock' => array_keys($mainWarehouseStocks)
            ]);

            // 3. Güncelleme işlemi
            $this->info('🔄 Stok güncelleme işlemi başlıyor...');
            $updatedCount = 0;
            $processedCount = 0;

            ProductVariant::whereIn('id', $stockManagedVariants)
                ->chunk(100, function ($variants) use (&$updatedCount, &$processedCount, $mainWarehouseStocks, $isDryRun) {
                    foreach ($variants as $variant) {
                        $processedCount++;
                        $currentStock = (int) $variant->stock;
                        $newStock = (int) ($mainWarehouseStocks[$variant->id] ?? 0);

                        if ($currentStock !== $newStock) {
                            $logMessage = "Güncelleme gerekli - ProductVariant ID: {$variant->id}, Mevcut: {$currentStock}, Yeni: {$newStock}";

                            Log::channel('stock-sync')->info($logMessage);
                            $this->line("🔧 {$logMessage}");

                            if (!$isDryRun) {
                                $variant->stock = $newStock;
                                $variant->save();
                            }

                            $updatedCount++;
                        }
                    }
                });

            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);

            // Sonuçları raporla
            $this->info('');
            $this->info('📊 SENKRONIZASYON TAMAMLANDI');
            $this->info("⏱️  Süre: {$executionTime} saniye");
            $this->info("📦 İşlenen toplam ürün: {$processedCount}");
            $this->info("🔧 Güncellenen ürün: {$updatedCount}");

            if ($isDryRun) {
                $this->info("🔍 DRY RUN - Hiçbir değişiklik yapılmadı");
            }

            Log::channel('stock-sync')->info('Stok senkronizasyonu tamamlandı', [
                'execution_time_seconds' => $executionTime,
                'processed_count' => $processedCount,
                'updated_count' => $updatedCount,
                'dry_run' => $isDryRun
            ]);

            return 0;
        } catch (Exception $e) {
            $errorMessage = "Stok senkronizasyon hatası: " . $e->getMessage();
            $this->error("❌ {$errorMessage}");

            Log::channel('stock-sync')->error($errorMessage, [
                'exception' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // Hata durumunda email gönder
            try {
                Mail::raw(
                    "Stok senkronizasyon hatası oluştu:\n\n" .
                        "Hata: {$e->getMessage()}\n" .
                        "Dosya: {$e->getFile()}:{$e->getLine()}\n" .
                        "Zaman: " . now()->format('Y-m-d H:i:s'),
                    function ($message) {
                        $message->to('<EMAIL>')
                            ->subject('KB Stok Senkronizasyon Hatası - ' . now()->format('Y-m-d H:i'));
                    }
                );

                Log::channel('stock-sync')->info('Hata bildirimi email ile gönderildi');
            } catch (Exception $mailException) {
                Log::channel('stock-sync')->error('Email gönderimi başarısız', [
                    'mail_error' => $mailException->getMessage()
                ]);
            }

            return 1;
        }
    }
}
