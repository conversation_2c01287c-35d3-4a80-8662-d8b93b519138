<?php

namespace App\Console\Commands;

use App\Models\Lunar\MediaStorage;
use App\Models\Lunar\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class importImagesFromFolder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ko:importImagesFromFolder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Doayalar okunuyor...');
        $listOfFiles = Storage::disk('public')->files('products/wbip');
//        ray('listOfFiles', $listOfFiles);
        foreach ($listOfFiles as $listOfFile) {
            $this->info('Dosya okunuyor: ' . $listOfFile);
            $file = Storage::disk('public')->get($listOfFile);

            $fileName = explode('/', $listOfFile);
            $fileName = end($fileName);
            $fileName = explode('.', $fileName);
            $fileName1 = $fileName[0];
            $fileName = explode('-', $fileName1);
            $fileName = $fileName[0];
//            ray('WP ID', $fileName);

            if (Product::where('wp_product_id', $fileName)->exists()) {

                // dosya var ise DB kaydet ve taşı
                $product = Product::where('wp_product_id', $fileName)->first();
                $m = Storage::disk('public')->move($listOfFile, 'products/ip/' . $fileName1 . '.png');
                $product->mediaStorage()->create([
                    'product_id' => $product->id,
                    'url' => env('APP_URL') . '/storage/products/ip/' . $fileName1 . '.png',
                ]);
//                $this->info('Dosya kaydediliyor: ' . $fileName);
//                Storage::disk('public')->put('products/' . $fileName . '.jpg', $file);
            } else {
                $this->error('WP ID Yok' . $fileName);
            }


//            $fileName = explode('/', $listOfFile);
//            $fileName = end($fileName);
//            $fileName = explode('.', $fileName);
//            $fileName = $fileName[0];
//            $this->info('Dosya kaydediliyor: ' . $fileName);
//            Storage::disk('public')->put('products/' . $fileName . '.jpg', $file);
        }

        return Command::SUCCESS;
    }
}
