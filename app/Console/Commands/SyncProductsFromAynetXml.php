<?php

namespace App\Console\Commands;

use App\FieldTypes\Text;
use App\FieldTypes\TranslatedText;
use App\Helpers\ProductHelper;
use App\Models\Lunar\Brand;
use App\Models\Lunar\Product;
use App\Models\Lunar\ProductVariant;
use App\Services\AynetXmlPriceService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SyncProductsFromAynetXml extends Command
{
    protected $signature = 'products:sync-from-aynet-xml 
                          {--list-multiple-variants : Birden fazla seçeneği olan ürünleri listele}
                          {--limit=0 : İşlenecek maksimum ürün sayısı (0=sınırsız)}';
    protected $description = 'Aynet XML kaynağından Apple ürünlerini çekip senkronize eder';

    public function handle()
    {
        $this->info('Aynet ürünleri draft durumuna alınıyor...');

        try {
            // Tüm aynet_product_id'ye sahip ürünleri draft olarak güncelle
            $aynetProductIds = \App\Models\Meta::where('key', 'aynet_product_id')
                ->where('metaable_type', 'App\Models\Lunar\Product')
                ->pluck('metaable_id');
            
            // Sadece published durumundaki Aynet ürünlerini draft'a çek
            $draftCount = Product::whereIn('id', $aynetProductIds)
                ->where('status', 'published')
                ->update(['status' => 'draft']);
            
            $this->info("{$draftCount} Aynet ürünü draft durumuna alındı.");

            $this->info('Aynet XML kaynağından Apple ürünleri çekiliyor...');
            // Curl ile XML'i çek
            $xmlContent = $this->fetchXmlWithCurl('https://www.aynetteknoloji.com/TicimaxXmlV2/DDDDEF3D990544DB83537C4DEEAB7CE6/');

            if (empty($xmlContent)) {
                $this->error('XML içeriği boş!');
                return Command::FAILURE;
            }

            // XML'i yükle
            $xml = simplexml_load_string($xmlContent);

            if (!$xml) {
                $this->error('XML verisi işlenemedi! XML hataları: ' . implode(', ', libxml_get_errors()));
                libxml_clear_errors();
                return Command::FAILURE;
            }

            // XML yapısını incele ve ürün sayısını doğru şekilde al
            $this->debugXmlStructure($xml);

            // Ürün sayısını belirle
            $totalProducts = $this->countProducts($xml);
            $this->info("Toplam {$totalProducts} ürün bulundu.");

            // Limit bilgisini göster
            $limit = (int)$this->option('limit');
            if ($limit > 0) {
                $this->info("Limit: {$limit} ürün işlenecek.");
            }

            // Birden fazla seçeneği olan ürünleri listele
            if ($this->option('list-multiple-variants')) {
                $this->listProductsWithMultipleVariants($xml);
                return Command::SUCCESS;
            }

            $created = 0;
            $updated = 0;
            $skipped = 0;

            // Ürünleri işle
            $this->processProducts($xml, $created, $updated, $skipped);

            $this->info("İşlem tamamlandı. {$created} ürün oluşturuldu, {$updated} ürün güncellendi, {$skipped} ürün atlandı (Apple dışı markalar).");

            // Ürün içe aktarımı sonrası attribute_data alanındaki field_type değerlerini düzelt
            $this->info('Ürün ve kategori verilerinde field_type alanları düzeltiliyor...');
            ProductHelper::fixAttributeDataFieldType();
            $this->info('Field type değerleri düzeltildi.');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Hata oluştu: " . $e->getMessage());
            Log::error("Aynet XML ürün senkronizasyonu hatası: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * XML yapısını debug etmek için kullanılır
     */
    private function debugXmlStructure($xml)
    {
        $this->info("XML yapısı inceleniyor...");

        // İlk seviye alt elemanları listele
        $this->info("İlk seviye alt elemanlar:");
        foreach ($xml->children() as $child) {
            $this->info("  - {$child->getName()}: " . count($child) . " adet");
        }
    }

    /**
     * XML içindeki ürün sayısını hesaplar
     */
    private function countProducts($xml)
    {
        // Doğrudan Urun elemanı varsa
        if (isset($xml->Urun)) {
            return count($xml->Urun);
        }

        // Tüm XML yapısını recursive olarak incele
        $count = 0;
        $this->countProductElements($xml, $count);

        return $count;
    }

    /**
     * XML içinde ürün elemanlarını sayar
     */
    private function countProductElements($xml, &$count)
    {
        foreach ($xml->children() as $child) {
            if ($child->getName() === 'Urun') {
                $count++;
            }

            if (count($child->children()) > 0) {
                $this->countProductElements($child, $count);
            }
        }
    }

    /**
     * Ürünleri işler
     */
    private function processProducts($xml, &$created, &$updated, &$skipped)
    {
        // Ürünleri bul
        $products = $this->findProducts($xml);

        if (empty($products)) {
            $this->warn("İşlenecek ürün bulunamadı!");
            return;
        }

        // Limit kontrolü
        $limit = (int)$this->option('limit');
        $processedCount = 0;

        foreach ($products as $xmlProduct) {
            // Limit kontrolü
            if ($limit > 0 && $processedCount >= $limit) {
                $this->info("Limit ({$limit}) aşıldı, işlem durduruluyor.");
                break;
            }

            // Apple olmayan ve UrunKartiID 1277 olmayan ürünleri atla
            if ((string)$xmlProduct->Marka !== 'Apple') {
                $skipped++;
                continue;
            }

            $productName = (string)$xmlProduct->UrunAdi;

            // Marka kontrolü ve oluşturma
            $brandName = (string)$xmlProduct->Marka;
            $brand = Brand::firstOrCreate(
                ['name' => $brandName],
                ['slug' => Str::slug($brandName)]
            );

            // Ürün var mı kontrol et
            $meta = \App\Models\Meta::where('key', 'aynet_product_id')
                ->where('value', (string)$xmlProduct->UrunKartiID)
                ->first();

            $product = null;
            if ($meta) {
                // Meta verisinden ürün ID'sini al ve doğrudan Product modeli üzerinden sorgula
                $product = Product::find($meta->metaable_id);
            }

            if (!$product) {
                $this->createProduct($xmlProduct, $brand, $productName);
                $created++;
            } else {
                $this->updateProduct($product, $xmlProduct, $brand, $productName);
                $updated++;
            }

            $processedCount++;

            // Her 10 üründe bir ilerleme bilgisi göster
            if ($processedCount % 10 === 0) {
                $this->info("İşlenen ürün: {$processedCount}");
            }
        }

        $this->info("Toplam {$processedCount} ürün işlendi.");
    }

    /**
     * XML içindeki ürünleri bulur
     */
    private function findProducts($xml)
    {
        $products = [];

        // Doğrudan Urun elemanı varsa
        if (isset($xml->Urun)) {
            return $xml->Urun;
        }

        // Tüm XML yapısını recursive olarak incele
        $this->findProductElements($xml, $products);

        return $products;
    }

    /**
     * XML içinde ürün elemanlarını bulur
     */
    private function findProductElements($xml, &$products)
    {
        foreach ($xml->children() as $child) {
            if ($child->getName() === 'Urun') {
                $products[] = $child;
            }

            if (count($child->children()) > 0) {
                $this->findProductElements($child, $products);
            }
        }
    }

    /**
     * Curl ile XML içeriğini çeker
     */
    private function fetchXmlWithCurl($url)
    {
        $this->info("XML içeriği çekiliyor: {$url}");

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $errno = curl_errno($ch);

        curl_close($ch);

        if ($errno) {
            $this->error("Curl hatası: {$error} (Kod: {$errno})");
            Log::error("Aynet XML çekme hatası: {$error} (Kod: {$errno})");
            return null;
        }

        if ($httpCode !== 200) {
            $this->error("HTTP hatası: {$httpCode}");
            Log::error("Aynet XML HTTP hatası: {$httpCode}");
            return null;
        }

        if (empty($response)) {
            $this->error("Boş yanıt alındı");
            Log::error("Aynet XML boş yanıt");
            return null;
        }

        $this->info("XML içeriği başarıyla çekildi. Boyut: " . strlen($response) . " byte");

        // XML içeriğini kontrol et
        if (strpos($response, '<?xml') === false && strpos($response, '<Urun>') === false) {
            $this->warn("Yanıt XML formatında olmayabilir. İlk 100 karakter: " . substr($response, 0, 100));
            Log::warning("Aynet XML yanıtı XML formatında olmayabilir: " . substr($response, 0, 100));
        }

        return $response;
    }

    /**
     * Birden fazla seçeneği olan ürünleri ve stok kodlarını listeler
     */
    private function listProductsWithMultipleVariants($xml)
    {
        $this->info("Birden fazla seçeneği olan ürünler listeleniyor...");

        $multipleVariantCount = 0;

        // Ürünleri bul
        $products = $this->findProducts($xml);

        if (empty($products)) {
            $this->warn("Listelenecek ürün bulunamadı!");
            return;
        }

        foreach ($products as $xmlProduct) {
            // Sadece Apple markalı ürünleri kontrol et
            if ((string)$xmlProduct->Marka !== 'Apple') {
                continue;
            }

            // Seçenek sayısını kontrol et
            $secenekCount = 0;
            if (isset($xmlProduct->UrunSecenek) && isset($xmlProduct->UrunSecenek->Secenek)) {
                $secenekCount = count($xmlProduct->UrunSecenek->Secenek);
            }

            if ($secenekCount > 1) {
                $multipleVariantCount++;
                $urunAdi = (string)$xmlProduct->UrunAdi;
                $urunKartiID = (string)$xmlProduct->UrunKartiID;

                $this->info("Ürün: {$urunAdi} (ID: {$urunKartiID})");
                $this->info("Seçenek Sayısı: {$secenekCount}");

                $this->info("Stok Kodları:");
                foreach ($xmlProduct->UrunSecenek->Secenek as $secenek) {
                    $stokKodu = (string)$secenek->StokKodu;
                    $satisFiyati = (float)$secenek->SatisFiyati;
                    $this->info("  - {$stokKodu} (Fiyat: {$satisFiyati} TL)");
                }

                $this->info("----------------------------------------");
            }
        }

        $this->info("Toplam {$multipleVariantCount} ürünün birden fazla seçeneği bulunuyor.");
    }

    private function createProduct($xmlProduct, $brand, $productName)
    {
        $product = new Product();
        $product->name = $productName;
        $product->brand_id = $brand->id;
        $product->product_type_id = 1;
        $product->status = (string)$xmlProduct->Aktif === 'Evet' ? 'published' : 'draft';
        $product->is_archived = true;
        $product->attribute_data = [
            'name' => new TranslatedText(collect([
                'tr' => new Text($productName),
            ])),
            'excerpt' => new TranslatedText(collect([
                'tr' => new Text((string)$xmlProduct->OnYazi),
            ])),
            'description' => new TranslatedText(collect([
                'tr' => new Text((string)$xmlProduct->Aciklama),
            ])),
        ];
        $product->save();

        // Meta bilgisi ekle
        $product->meta()->create([
            'key' => 'aynet_product_id',
            'value' => (string)$xmlProduct->UrunKartiID,
        ]);

        // Ürün URL'i oluştur
        $product->defaultUrl()->create([
            'slug' => Str::slug($productName) . '-aynet',
            'language_id' => 2,
            'element_type' => 'Lunar\Models\Product',
            'default' => true,
            'element_id' => $product->id,
        ]);

        // Kategorileri ekle
        $this->addProductToCategories($product, $xmlProduct);

        // Ürün varyantlarını oluştur
        $this->createProductVariants($product, $xmlProduct);

        // Resimleri ekle
        $this->addProductImages($product, $xmlProduct);

        $this->info("Ürün oluşturuldu: {$productName}");

        return $product;
    }

    /**
     * Ürünü kategorilere ekler
     */
    private function addProductToCategories($product, $xmlProduct)
    {
        try {
            // Genel Aynet kategorisini bul veya oluştur
            $aynetCategory = $this->findOrCreateCategory('Aynet');

            if (!$aynetCategory) {
                $this->warn("Aynet kategorisi oluşturulamadı, kategori ekleme işlemi atlanıyor.");
                return;
            }

            // Ürüne özel kategori adını oluştur
            $urunKategori = (string)$xmlProduct->Kategori;
            $categoryName = "Aynet {$urunKategori}";

            // Ürüne özel kategoriyi bul veya oluştur
            $productCategory = $this->findOrCreateCategory($categoryName, $aynetCategory->id);

            if (!$productCategory) {
                $this->warn("Ürün kategorisi oluşturulamadı, sadece Aynet kategorisine ekleniyor.");
                $product->collections()->syncWithoutDetaching([$aynetCategory->id]);
                $this->info("Ürün Aynet kategorisine eklendi.");
                return;
            }

            // Ürünü kategorilere ekle
            $product->collections()->syncWithoutDetaching([$aynetCategory->id, $productCategory->id]);
            $this->info("Ürün kategorilere eklendi: Aynet, {$categoryName}");
        } catch (\Exception $e) {
            $this->error("Kategori ekleme hatası: " . $e->getMessage());
            Log::error("Kategori ekleme hatası: " . $e->getMessage());
            Log::error($e->getTraceAsString());
        }
    }

    /**
     * Kategoriyi bulur veya oluşturur
     */
    private function findOrCreateCategory($name, $parentId = null)
    {
        try {
            // Önce kategoriyi bulmaya çalış
            $category = \App\Models\Lunar\Collection::whereNull('deleted_at')
                ->where('collection_name', $name)
                ->first();

            if (!$category) {
                // Kategori yoksa oluştur
                $category = new \App\Models\Lunar\Collection();
                $category->collection_group_id = 1;
                $category->type = 'static';
                $category->title = $name; // title alanını ayarla
                $category->attribute_data = [
                    'name' => new TranslatedText(collect([
                        'tr' => new Text($name),
                    ])),
                ];

                $category->is_visible = false;
                $category->sort = 'custom';
                $category->sub_category_note = '';

                // Nested set model için değerleri ayarla
                $maxRgt = \App\Models\Lunar\Collection::max('_rgt') ?? 0;
                $category->_lft = $maxRgt + 1;
                $category->_rgt = $maxRgt + 2;

                // Eğer üst kategori varsa, onu ayarla
                if ($parentId) {
                    $parentCategory = \App\Models\Lunar\Collection::find($parentId);
                    if ($parentCategory) {
                        $category->parent_id = $parentId;
                    }
                }

                $category->save();

                // Kategori URL'i oluştur
                $category->defaultUrl()->create([
                    'slug' => Str::slug($name),
                    'language_id' => 2,
                    'element_type' => 'Lunar\Models\Collection',
                    'default' => true,
                    'element_id' => $category->id,
                ]);

                $this->info("Yeni kategori oluşturuldu: {$name}");
            }

            return $category;
        } catch (\Exception $e) {
            $this->error("Kategori oluşturma hatası: " . $e->getMessage() . " Satır: " .  $e->getLine());
            Log::error("Kategori oluşturma hatası: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            return null;
        }
    }

    private function updateProduct($product, $xmlProduct, $brand, $productName)
    {
        $product->name = $productName;
        //$product->brand_id = $brand->id;
        $product->status = (string)$xmlProduct->Aktif === 'Evet' ? 'published' : 'draft';
        $product->attribute_data = [
            'name' => new TranslatedText(collect([
                'tr' => new Text($productName),
            ])),
            'excerpt' => new TranslatedText(collect([
                'tr' => new Text((string)$xmlProduct->OnYazi),
            ])),
            'description' => new TranslatedText(collect([
                'tr' => new Text((string)$xmlProduct->Aciklama),
            ])),
        ];
        $product->save();

        // Ürün varyantlarını güncelle
        $this->createProductVariants($product, $xmlProduct);

        $this->info("Ürün güncellendi: {$productName}");

        return $product;
    }

    private function createProductVariants($product, $xmlProduct)
    {
        // Seçenek var mı kontrol et
        if (!isset($xmlProduct->UrunSecenek) || !isset($xmlProduct->UrunSecenek->Secenek)) {
            $this->warn("Ürün için seçenek bulunamadı: {$product->name}");
            return;
        }

        // XML'den gelen VaryasyonID'leri topla
        $xmlVariationIds = [];
        foreach ($xmlProduct->UrunSecenek->Secenek as $secenek) {
            $xmlVariationIds[] = (string)$secenek->VaryasyonID;
        }

        // Her seçenek için varyant oluştur veya güncelle
        foreach ($xmlProduct->UrunSecenek->Secenek as $secenek) {
            $stokKodu = (string)$secenek->StokKodu;
            $varyasyonID = (string)$secenek->VaryasyonID;
            $satisFiyati = (float)$secenek->SatisFiyati * 100; // Kuruş cinsinden
            $stokAdedi = (int)$secenek->StokAdedi;

            // EkSecenekOzellik kontrolü - baştan yap
            $shouldSkipVariant = false;

            if (isset($secenek->EkSecenekOzellik) && isset($secenek->EkSecenekOzellik->Ozellik)) {
                foreach ($secenek->EkSecenekOzellik->Ozellik as $ozellik) {
                    $tanim = (string)$ozellik['Tanim'];
                    $deger = (string)$ozellik['Deger'];

                    if ($tanim === 'Ek garanti' && $deger !== 'Standart (2 Yıl Garanti)') {
                        $shouldSkipVariant = true;
                        $this->info("Varyant atlandı (ek garanti farklı): {$stokKodu} - Garanti: {$deger}");
                        break;
                    }
                }
            }

            // Varyant meta bilgisini kontrol et
            $meta = \App\Models\Meta::where('key', 'aynet_variation_id')
                ->where('value', $varyasyonID)
                ->where('metaable_type', 'App\Models\Lunar\ProductVariant')
                ->first();

            $variant = null;
            if ($meta) {
                // Meta verisinden varyant ID'sini al ve doğrudan ProductVariant modeli üzerinden sorgula
                $variant = ProductVariant::find($meta->metaable_id);
            }

            // Eğer ek garanti standart değilse bu varyantı hiç işleme
            if ($shouldSkipVariant) {

                // Mevcut varyant var - ek kontrol yap (eski kayıtları temizlemek için)
                $shouldDeleteVariant = false;

                if (isset($secenek->EkSecenekOzellik) && isset($secenek->EkSecenekOzellik->Ozellik)) {
                    foreach ($secenek->EkSecenekOzellik->Ozellik as $ozellik) {
                        $tanim = (string)$ozellik['Tanim'];
                        $deger = (string)$ozellik['Deger'];

                        if ($tanim === 'Ek garanti' && $deger !== 'Standart (2 Yıl Garanti)') {
                            $shouldDeleteVariant = true;
                            break;
                        }
                    }
                }

                if ($shouldDeleteVariant && $variant) {
                    // Eski varyantı soft delete ile sil (veritabanında kalıntı temizliği)
                    $variant->prices()->update(['deleted_at' => now()]); // İlişkili fiyatları soft delete
                    $variant->update(['deleted_at' => now()]); // Varyantı soft delete
                    $meta->update(['deleted_at' => now()]); // Meta bilgisini soft delete
                    $this->info("Varyant silindi (ek garanti farklı): {$stokKodu} - Garanti: {$deger}");
                    continue; // Bu varyant silindiği için fiyat güncelleme işlemini atla
                }

                continue;
            }

            if (!$variant) {
                // Yeni varyant oluştur (sadece standart garantili olanlar buraya gelir)
                $variant = new ProductVariant();
                $variant->product_id = $product->id;
                $variant->tax_class_id = 1;
                $variant->sku = $stokKodu;
                $variant->purchasable = 'in_stock';
                $variant->stock = $stokAdedi;
                $variant->save();

                // Meta bilgisi ekle
                \App\Models\Meta::create([
                    'key' => 'aynet_variation_id',
                    'value' => $varyasyonID,
                    'metaable_type' => 'App\Models\Lunar\ProductVariant',
                    'metaable_id' => $variant->id,
                ]);

                $this->info("Yeni varyant oluşturuldu: {$stokKodu} - Fiyat: {$satisFiyati} kuruş");
            } else {

                // Mevcut varyantı güncelle (sadece standart garantili olanlar buraya gelir)
                $variant->sku = $stokKodu;
                $variant->purchasable = 'in_stock';
                $variant->stock = $stokAdedi;
                $variant->save();

                $this->info("Varyant güncellendi: {$stokKodu} - Fiyat: {$satisFiyati} kuruş");
            }

            // Varyant fiyatını ekle veya güncelle
            $this->createOrUpdatePrices($variant, $satisFiyati);
        }

        // XML'de artık bulunmayan varyantları tespit et ve soft delete yap
        $this->removeObsoleteVariants($product, $xmlVariationIds);
    }

    /**
     * XML'de artık bulunmayan varyantları tespit eder ve soft delete yapar
     */
    private function removeObsoleteVariants($product, $xmlVariationIds)
    {
        // Bu ürüne ait varyant ID'lerini al
        $productVariantIds = ProductVariant::where('product_id', $product->id)
            ->whereNull('deleted_at')
            ->pluck('id')
            ->toArray();

        if (empty($productVariantIds)) {
            return; // Bu ürüne ait varyant yok
        }

        // Bu ürüne ait aynet_variation_id meta kayıtlarını al
        $existingMetas = \App\Models\Meta::where('key', 'aynet_variation_id')
            ->where('metaable_type', 'App\Models\Lunar\ProductVariant')
            ->whereIn('metaable_id', $productVariantIds)
            ->whereNull('deleted_at')
            ->get(['metaable_id', 'value']);

        // Meta'da bulunan variation ID'leri
        $existingVariationIds = $existingMetas->pluck('value')->toArray();

        // XML'de olmayan ama meta'da olan variation ID'leri (silinecekler)
        $obsoleteVariationIds = array_diff($existingVariationIds, $xmlVariationIds);

        if (empty($obsoleteVariationIds)) {
            return; // Silinecek varyant yok
        }

        $removedCount = 0;

        // Silinecek variation ID'lere ait meta kayıtlarını ve varyantları bul
        $metasToDelete = $existingMetas->whereIn('value', $obsoleteVariationIds);

        foreach ($metasToDelete as $meta) {
            $variant = ProductVariant::find($meta->metaable_id);

            if ($variant) {
                // Varyantı soft delete ile sil
                $variant->prices()->update(['deleted_at' => now()]); // İlişkili fiyatları soft delete
                $variant->update(['deleted_at' => now()]); // Varyantı soft delete

                // Meta kaydını soft delete
                \App\Models\Meta::where('id', $meta->id)->update(['deleted_at' => now()]);

                $removedCount++;
                $this->info("Eski varyant silindi (XML'de artık yok): {$variant->sku} - VaryasyonID: {$meta->value}");
            }
        }

        if ($removedCount > 0) {
            $this->info("Toplam {$removedCount} eski varyant temizlendi: {$product->name}");
        }
    }

    private function createOrUpdatePrices($variant, $basePrice)
    {
        $priceService = new AynetXmlPriceService();

        // 1, 3, 6, 12, 18, 2, 24, 36 kiralama ayları için fiyatlar
        foreach ([1, 2, 3, 4, 5, 6, 7, 8] as $subscriptionMonthsId) {
            // Servis üzerinden operasyonel fiyatı hesapla
            $calculatedPrice = $priceService->getOperationalPrice($basePrice / 100, $subscriptionMonthsId);

            $variant->prices()->updateOrCreate(
                [
                    'currency_id' => 1,
                    'priceable_type' => 'Lunar\Models\ProductVariant',
                    'subscription_months_id' => $subscriptionMonthsId,
                ],
                [
                    'price' => $calculatedPrice * 100, // Kuruş cinsinden kaydet
                ]
            );
        }
    }

    private function addProductImages($product, $xmlProduct)
    {
        // Resimler var mı kontrol et
        if (!isset($xmlProduct->Resimler) || !isset($xmlProduct->Resimler->Resim)) {
            $this->warn("Ürün için resim bulunamadı: {$product->name}");
            return;
        }

        foreach ($xmlProduct->Resimler->Resim as $imageUrl) {
            if (!empty((string)$imageUrl)) {
                $this->addImageToProduct($product, (string)$imageUrl);
            }
        }
    }

    private function addImageToProduct($product, $imageUrl)
    {
        try {
            $originalFileName = basename(parse_url($imageUrl, PHP_URL_PATH));
            $extension = pathinfo($originalFileName, PATHINFO_EXTENSION);

            $existingQueue = DB::connection('mysql-lunar-non-prefix')->table('media_queues')
                ->where('url', $imageUrl)
                ->where('product_id', $product->id)
                ->first();

            if ($existingQueue) {
                $this->info("Resim zaten kuyruğa eklenmiş: {$imageUrl}");
                return;
            }

            DB::connection('mysql-lunar-non-prefix')->table('media_queues')->insert([
                'url' => $imageUrl,
                'format' => $extension ?: 'jpg',
                'product_id' => $product->id,
                'status' => 'pending',
                'created_at' => now(),
                'updated_at' => now()
            ]);

            $this->info("Resim kuyruğa eklendi: {$imageUrl}");
        } catch (\Exception $e) {
            Log::warning("Resim kuyruğa eklenirken hata: {$e->getMessage()}");
            $this->warn("Resim işleme hatası: {$imageUrl} - {$e->getMessage()}");
        }
    }
}
