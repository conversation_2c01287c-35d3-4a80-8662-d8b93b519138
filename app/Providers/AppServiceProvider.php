<?php

namespace App\Providers;

use App\Support\Kiralabunu;
use <PERSON>zhanSalleh\FilamentShield\Facades\FilamentShield;
use Filament\Facades\Filament;
use Filament\Forms\Components\Select;
use Illuminate\Support\ServiceProvider;
use RyanChandler\FilamentNavigation\Facades\FilamentNavigation;
use Illuminate\Support\Facades\Gate;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(Kiralabunu::class, function ($app) {
            return new Kiralabunu($app['request']);
        });

        if ($this->app->environment('test')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // Implicitly grant "Super Admin" role all permissions
        // This works in the app by using gate-related functions like auth()->user->can() and @can()
        Gate::before(function ($user, $ability) {
            return $user->hasRole('Super Admin') ? true : null;
        });

        Filament::serving(function () {
            Filament::registerTheme(
                mix('css/filament.css'),
            );

            Filament::registerNavigationGroups([
                'Sipariş Yönetimi',
                'Skorlama',
                'Kurumsal Siparişler',
                'Stok Yönetimi',
                'Ürün Yönetimi',
                'Tahsilat Yönetimi',
            ]);

            FilamentNavigation::addItemType('Ana Kategori', [
                Select::make('category_id')
                    ->label('Bağlı Kategori')
                    ->searchable()
                    ->preload()
                    ->options(function () {
                        return \App\Models\Lunar\Collection::whereNull('parent_id')->get()->pluck('getName', 'id');
                    }),
            ]);

            FilamentNavigation::addItemType('Alt Kategori', [
                Select::make('category_id')
                    ->label('Bağlı Kategori')
                    ->searchable()
                    ->preload()
                    ->options(function () {
                        return \App\Models\Lunar\Collection::whereNotNull('parent_id')->get()->pluck('getName', 'id');
                    }),
            ]);

            FilamentShield::configurePermissionIdentifierUsing(
                fn($resource) => str($resource::getModel())
                    ->afterLast('\\')
                    ->lower()
                    ->toString()
            );
        });
    }
}
