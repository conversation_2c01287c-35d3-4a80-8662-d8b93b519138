<?php

namespace App\Providers;

use App\Repositories\Cart\CartRepository;
use App\Repositories\Cart\CartRepositoryEloquent;
use App\Repositories\Cart\GuestCartRepository;
use App\Repositories\Cart\GuestCartRepositoryEloquent;
use App\Repositories\Category\CategoryRepository;
use App\Repositories\Category\CategoryRepositoryEloquent;
use App\Repositories\Order\OrderRepository;
use App\Repositories\Order\OrderRepositoryEloquent;
use App\Repositories\Product\ProductRepository;
use App\Repositories\Product\ProductRepositoryEloquent;
use App\Repositories\ProductOption\ProductOptionRepository;
use App\Repositories\ProductOption\ProductOptionRepositoryEloquent;
use App\Repositories\ProductOptionType\ProductOptionTypeRepository;
use App\Repositories\ProductOptionType\ProductOptionTypeRepositoryEloquent;
use App\Repositories\ProductPrice\ProductPriceRepository;
use App\Repositories\ProductPrice\ProductPriceRepositoryEloquent;
use App\Repositories\User\UserRepository;
use App\Repositories\User\UserRepositoryEloquent;
use App\Repositories\UserAddress\UserAddressRepository;
use App\Repositories\UserAddress\UserAddressRepositoryEloquent;
use Illuminate\Support\ServiceProvider;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Repository bindings.
     *
     * @var array
     */
    public const BINDINGS = [
        CartRepository::class => CartRepositoryEloquent::class,
        GuestCartRepository::class => GuestCartRepositoryEloquent::class,
        UserRepository::class => UserRepositoryEloquent::class,
        UserAddressRepository::class => UserAddressRepositoryEloquent::class,
        OrderRepository::class => OrderRepositoryEloquent::class,
        CategoryRepository::class => CategoryRepositoryEloquent::class,
        ProductRepository::class => ProductRepositoryEloquent::class,
        ProductOptionRepository::class => ProductOptionRepositoryEloquent::class,
        ProductOptionTypeRepository::class => ProductOptionTypeRepositoryEloquent::class,
        ProductPriceRepository::class => ProductPriceRepositoryEloquent::class,
    ];

    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        foreach (self::BINDINGS as $abstract => $concrete) {
            $this->app->bind($abstract, $concrete);
        }
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
