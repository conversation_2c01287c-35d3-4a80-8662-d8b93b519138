<?php

namespace App\Providers;

use App\Models\Lunar\Collection;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Ryan<PERSON>handler\FilamentNavigation\Models\Navigation;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        'Illuminate\Http\Client\Events\RequestSending' => [
            'App\Listeners\LogResponseSending',
        ],
        'Illuminate\Http\Client\Events\ResponseReceived' => [
            'App\Listeners\LogResponseReceived',
        ],
        \App\Events\ScoringCompleted::class => [
            \App\Listeners\SendResultToSourceListener::class,
        ],
        \App\Events\FindexFileReceivedFromBrowserAutomation::class => [
            \App\Listeners\SendFindexFileToSkorlabunu::class,
        ],
        \App\Events\FindexAutomationStatusUpdated::class => [
            \App\Listeners\FindexAutomationStatusUpdatedListener::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        Collection::observe(\App\Observers\CollectionObserver::class);
        Navigation::observe(\App\Observers\NavigationObserver::class);
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     *
     * @return bool
     */
    public function shouldDiscoverEvents()
    {
        return false;
    }
}
