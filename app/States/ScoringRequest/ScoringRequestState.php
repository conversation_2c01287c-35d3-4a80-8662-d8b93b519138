<?php

namespace App\States\ScoringRequest;

use Spa<PERSON>\ModelStates\State;
use Spatie\ModelStates\StateConfig;

abstract class ScoringRequestState extends State
{
    public static function config(): StateConfig
    {
        return parent::config()
            ->default(PendingState::class)
            ->allowTransition(PendingState::class, SentToRedisState::class)
            ->allowTransition(PendingState::class, SystemEnteredState::class)
            ->allowTransition(PendingState::class, UserInfoErrorState::class)
            ->allowTransition(PendingState::class, UserContactErrorState::class)
            ->allowTransition(SentToRedisState::class, ScoredState::class)
            ->allowTransition(SentToRedisState::class, ManuallyProcessedState::class)
            ->allowTransition(SentToRedisState::class, UserInfoErrorState::class)
            ->allowTransition(SentToRedisState::class, UserContactErrorState::class)
            ->allowTransition(ScoredState::class, ApprovedState::class)
            ->allowTransition(ScoredState::class, RejectedState::class)
            ->allowTransition(ApprovedState::class, WebhookSentApprovedState::class)
            ->allowTransition(RejectedState::class, WebhookSentRejectedState::class)
            ->allowTransition(ManuallyProcessedState::class, PendingState::class)
            ->allowTransition(ManuallyProcessedState::class, RejectedState::class)
            ->allowTransition(ManuallyProcessedState::class, WebhookSentApprovedState::class)
            ->allowTransition(ManuallyProcessedState::class, WebhookSentRejectedState::class)
            ->allowTransition(SystemEnteredState::class, ManuallyProcessedState::class)
            ->allowTransition(UserInfoErrorState::class, ManuallyProcessedState::class)
            ->allowTransition(UserInfoErrorState::class, RejectedState::class)
            ->allowTransition(UserContactErrorState::class, ManuallyProcessedState::class)
            ->allowTransition(UserContactErrorState::class, RejectedState::class);
    }
}
