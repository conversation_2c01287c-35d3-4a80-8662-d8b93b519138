<?php

namespace App\States\Order;

use Spatie\ModelStates\State;
use Spatie\ModelStates\StateConfig;

abstract class OrderState extends State
{
    public static function config(): StateConfig
    {
        return parent::config()
            ->default(OrderReceived::class)
            //->allowTransition(OrderReceived::class, OrderApproved::class)
            ->allowTransition(OrderReceived::class, OrderDocumentWaiting::class)
            ->allowTransition(OrderReceived::class, OrderEvaluation::class)
            ->allowTransition(OrderReceived::class, OrderCancelled::class)
            ->allowTransition(OrderReceived::class, OrderUserCantBeReached::class)
            ->allowTransition(OrderEvaluation::class, OrderDocumentWaiting::class)
            ->allowTransition(OrderEvaluation::class, OrderStudentDocumentWaiting::class)
            ->allowTransition(OrderEvaluation::class, OrderApproved::class)
            ->allowTransition(OrderEvaluation::class, OrderCancelled::class)
            ->allowTransition(OrderEvaluation::class, OrderDenied::class)
            ->allowTransition(OrderEvaluation::class, OrderUserCantBeReached::class)
            ->allowTransition(OrderEvaluation::class, Investigation::class)
            //            ->allowTransition(OrderUserCantBeReached::class, OrderEvaluation::class) // Müşteriye ulaşılamadıktan sonra siparişlerin ödemesi iptal edildiği ve mail gönderildiği için bu geçiş iptal edildi.
            ->allowTransition(OrderDenied::class, OrderEvaluation::class)
            ->allowTransition(OrderCancelled::class, OrderEvaluation::class)
            ->allowTransition(OrderDocumentWaiting::class, OrderEvaluation::class)
            ->allowTransition(OrderDocumentWaiting::class, Investigation::class)
            ->allowTransition(OrderStudentDocumentWaiting::class, OrderEvaluation::class)
            ->allowTransition(OrderStudentDocumentWaiting::class, OrderDenied::class)
            ->allowTransition(OrderStudentDocumentWaiting::class, OrderCancelled::class)
            ->allowTransition(OrderApproved::class, OrderShipped::class)
            ->allowTransition(OrderApproved::class, OrderEvaluation::class)
            ->allowTransition(OrderApproved::class, OrderCancelled::class) // Berfin onaylandıktan sonra siparişlerin iptal edilebileceğini söylediği için eklendi.
            ->allowTransition(OrderApproved::class, OrderProductNotSuitable::class)
            ->allowTransition(OrderRenting::class, OrderCompleted::class)
            ->allowTransition(OrderRenting::class, OrderPaused::class)
            ->allowTransition(OrderPaused::class, OrderRenting::class)
            //->allowTransition(OrderCompleted::class, OrderRenting::class)
            ->allowTransition(OrderProductNotSuitable::class, OrderApproved::class)
            ->allowTransition(OrderProductNotSuitable::class, OrderCancelled::class)
            ->allowTransition([OrderApproved::class, OrderShipped::class, OrderCompleted::class], OrderRefunded::class)
            ->allowTransition([OrderApproved::class, OrderShipped::class, OrderCompleted::class], OrderAtLegalPursuit::class)
            ->allowTransition(OrderReceived::class, OrderCancelled::class);
    }

    public function getLabel(): string
    {
        return $this->name;
    }
}
