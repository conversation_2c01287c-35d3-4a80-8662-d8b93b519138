<?php

namespace App\States\OrderTransactionCustomerContact;

use <PERSON><PERSON>\ModelStates\State;
use Spa<PERSON>\ModelStates\StateConfig;

abstract class CustomerContactState extends State
{
    public static function config(): StateConfig
    {
        return parent::config()
            ->default(NotContacted::class)
            ->allowTransition([NotContacted::class, Talked::class, LineOff::class, CantReached::class], Talked::class)
            ->allowTransition([NotContacted::class, Talked::class, LineOff::class, CantReached::class], LineOff::class)
            ->allowTransition([NotContacted::class, Talked::class, LineOff::class, CantReached::class], CantReached::class);
    }

    public function getLabel(): string
    {
        return $this->name;
    }
}
