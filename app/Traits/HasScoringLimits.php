<?php

namespace App\Traits;

use App\Models\ScoringLimit;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HasScoringLimits
{
    /**
     * Get all scoring limits for this entity
     *
     * @return HasMany
     */
    public function scoringLimits(): HasMany
    {
        // For User model, match by user_id
        if ($this->getTable() === 'users') {
            return $this->hasMany(ScoringLimit::class, 'user_id');
        }
        
        // For ScoringRequest model, match by scoring_request_id
        if ($this->getTable() === 'scoring_requests') {
            return $this->hasMany(ScoringLimit::class, 'scoring_request_id');
        }
        
        // Default fallback
        return $this->hasMany(ScoringLimit::class);
    }

    /**
     * Get the current valid scoring limit
     *
     * @return ScoringLimit|null
     */
    public function getCurrentScoringLimit(): ?ScoringLimit
    {
        // For User model, check meta for current scoring limit ID
        if ($this->getTable() === 'users' && method_exists($this, 'meta')) {
            $limitId = $this->meta()
                ->where('key', 'current_scoring_limit_id')
                ->first()?->value;
            
            if ($limitId) {
                $limit = ScoringLimit::find($limitId);
                if ($limit && $limit->isValid()) {
                    return $limit;
                }
            }
        }
        
        // Fallback to latest valid limit
        return $this->scoringLimits()
            ->valid()
            ->orderBy('created_at', 'desc')
            ->first();
    }

    /**
     * Check if entity has a valid scoring limit
     *
     * @return bool
     */
    public function hasValidScoringLimit(): bool
    {
        return $this->getCurrentScoringLimit() !== null;
    }

    /**
     * Get available limit amount
     *
     * @return float
     */
    public function getAvailableLimit(): float
    {
        $limit = $this->getCurrentScoringLimit();
        return $limit ? $limit->remaining_limit : 0.0;
    }

    /**
     * Get approved limit amount
     *
     * @return float
     */
    public function getApprovedLimit(): float
    {
        $limit = $this->getCurrentScoringLimit();
        return $limit ? $limit->approved_limit : 0.0;
    }

    /**
     * Update remaining limit
     *
     * @param float $amount Amount to deduct (positive) or add (negative)
     * @return bool
     */
    public function updateRemainingLimit(float $amount): bool
    {
        $limit = $this->getCurrentScoringLimit();
        
        if (!$limit) {
            return false;
        }
        
        if ($amount > 0) {
            // Deduct from limit
            return $limit->useLimit($amount);
        } else {
            // Restore to limit
            return $limit->restoreLimit(abs($amount));
        }
    }

    /**
     * Set current scoring limit in meta (for User model)
     *
     * @param ScoringLimit $scoringLimit
     * @return void
     */
    public function setCurrentScoringLimit(ScoringLimit $scoringLimit): void
    {
        if ($this->getTable() === 'users' && method_exists($this, 'meta')) {
            // Update or create meta entry
            $this->meta()->updateOrCreate(
                ['key' => 'current_scoring_limit_id'],
                ['value' => $scoringLimit->id]
            );
        }
    }

    /**
     * Get the latest scoring limit (regardless of validity)
     *
     * @return ScoringLimit|null
     */
    public function getLatestScoringLimit(): ?ScoringLimit
    {
        return $this->scoringLimits()
            ->orderBy('created_at', 'desc')
            ->first();
    }

    /**
     * Get all valid scoring limits
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getValidScoringLimits()
    {
        return $this->scoringLimits()
            ->valid()
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get scoring limit by TCKN (static method for finding by TCKN)
     *
     * @param string $tckn
     * @return ScoringLimit|null
     */
    public static function getScoringLimitByTckn(string $tckn): ?ScoringLimit
    {
        return ScoringLimit::getLatestValidByTckn($tckn);
    }

    /**
     * Check if a TCKN has a valid scoring limit
     *
     * @param string $tckn
     * @return bool
     */
    public static function tcknHasValidScoringLimit(string $tckn): bool
    {
        return ScoringLimit::where('tckn', $tckn)
            ->valid()
            ->exists();
    }
}