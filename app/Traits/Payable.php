<?php

namespace App\Traits;

use App\Exceptions\Card\CardRemoveException;
use App\Facades\IyzipayFacade;
use App\Models\CreditCard;
use App\Models\OrderTransaction;
use App\Models\PaymentTransaction;
use App\Models\Subscription;
use App\Models\Transaction;
use App\StorableClasses\BillFields;
use App\StorableClasses\Plan;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

trait Payable
{

    /**
     * @param $value
     */
    public function setBillFieldsAttribute(BillFields $value)
    {
        $this->attributes['bill_fields'] = (string)$value;
    }

    /**
     * @param $value
     *
     * @return object
     */
    public function getBillFieldsAttribute($value)
    {
        if (empty($value)) {
            return $value;
        }

        return (new \JsonMapper())->map(json_decode($value), new BillFields());
    }

    /**
     * Credit card relationship for the payable model
     *
     * @return HasMany
     */
    public function creditCards(): HasMany
    {
        return $this->hasMany(CreditCard::class, 'billable_id');
    }

    /**
     * Transaction relationship for the payable model
     * TODO: Bu ilişkiyi güncelle
     *
     * @return HasMany
     */
    public function transactions(): HasMany
    {
        // Bu ilişi OrderTransaction modeline bağlanmalı
        return $this->hasMany(Transaction::class, 'billable_id');
    }

    /**
     * Payable can has many subscriptions
     *
     * @return HasMany
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class, 'billable_id');
    }

    /**
     * Add credit card for payable
     *
     * @param array $attributes
     * @return CreditCard
     */
    public function addCreditCard(array $attributes = []): CreditCard
    {
        return IyzipayFacade::addCreditCard($this, $attributes);
    }

    /**
     * Remove credit card credentials from the payable
     *
     * @param CreditCard $creditCard
     * @return bool
     * @throws CardRemoveException
     */
    public function removeCreditCard(CreditCard $creditCard): bool
    {
        if (!$this->creditCards->contains($creditCard)) {
            throw new CardRemoveException('This card does not belong to member!');
        }

        return IyzipayFacade::removeCreditCard($creditCard);
    }

    /**
     * Single payment for the payable
     *
     * @param Collection $products
     * @param string $currency
     * @param int $installment
     * @param bool $subscription
     * @return Transaction
     */
    public function pay(Collection $products, CreditCard $cc, $currency = 'TRY', $installment = 1, $subscription = false): Transaction
    {
        return IyzipayFacade::singlePayment($this, $cc, $products, $currency, $installment, $subscription);
    }

    /**
     * Subscribe to a plan.
     * @param Plan $plan
     */
    public function subscribe(Plan $plan): void
    {
        Model::unguard();

        $this->subscriptions()->save(
            new Subscription([
                'next_charge_amount' => $plan->price,
                'currency' => $plan->currency,
                'next_charge_at' => Carbon::now()->addDays($plan->trialDays)->startOfDay(),
                'plan' => $plan
            ])
        );

        // Dublicate işleme sebep oluyor idi
//        $this->paySubscription();

        Model::reguard();
    }

    /**
     * Check if payable subscribe to a plan
     *
     * @param Plan $plan
     * @return bool
     */
    public function isSubscribeTo(Plan $plan): bool
    {
        foreach ($this->subscriptions as $subscription) {
            if ($subscription->plan == $plan) {
                return $subscription->next_charge_at > Carbon::today()->startOfDay();
            }
        }

        return false;
    }

    /**
     * Payment for the subscriptions of payable
     */
    public function paySubscription($order_id)
    {

        $subscription = $this->subscriptions->last();

        $plan = $subscription->plan;
        $transaction = $this->pay(collect([$plan]), $plan->currency, 1, true);
        $transaction->subscription()->associate($subscription);
        $transaction->order_id = $order_id;
        $transaction->save();

        $subscription->delete();

//        foreach ($this->subscriptions as $subscription) {
//            if ($subscription->canceled() || $subscription->next_charge_at < Carbon::today()->startOfDay()) {
//                continue;
//            }
//
//            if ($subscription->next_charge_amount > 0) {
//                $transaction = $this->pay(collect([$subscription->plan]), $subscription->plan->currency, 1, true);
//                $transaction->subscription()->associate($subscription);
//                $transaction->save();
//            }
//
//            $subscription->next_charge_at = $subscription->next_charge_at->addMonths(($subscription->plan->interval == 'yearly') ? 12 : 1);
//            $subscription->save();
//        }
    }

    public function payTransaction(OrderTransaction $orderTransaction)
    {
        $order = $orderTransaction->order;
        $plan = new Plan();
        $plan->name($order->user->fullName . "/" . $order->order_number . ' TK' . $orderTransaction->id)
            ->price($orderTransaction->amount)
            ->attributes($order->only(['id', 'user_id', 'coupon_id', 'order_number']));

        try {
            $transaction = $this->pay(collect([$plan]), $orderTransaction->creditCard, $plan->currency, 1, true);
            $transaction->order_id = $order->id;
            $transaction->save();

            $orderTransaction->bank_last_message = '';
            $orderTransaction->note = 'Son 4 hanesi ' . $orderTransaction->creditCard->number . ' olan karttan iyzico üzerinden ödeme alındı.';
            $orderTransaction->payment_status_id = 1; // Ödendi
            $orderTransaction->last_payment_check = now();
            $orderTransaction->save();

            $pt = new PaymentTransaction();
            $pt->amount = $orderTransaction->amount;
            $pt->order_id = $orderTransaction->order_id;
            $pt->order_transaction_id = $orderTransaction->id;
            $pt->payment_status_id = 1;
            $pt->card_id = $orderTransaction->card_id;
            $pt->payment_type = 'IYZICO';
            $pt->bank_last_message = '';
            $pt->save();
        } catch (\Exception $e) {
            $orderTransaction->bank_last_message = $e->getMessage();
            $orderTransaction->last_payment_check = now();
            $orderTransaction->save();

            $pt = new PaymentTransaction();
            $pt->amount = $orderTransaction->amount;
            $pt->order_id = $orderTransaction->order_id;
            $pt->order_transaction_id = $orderTransaction->id;
            $pt->payment_status_id = 3;
            $pt->card_id = $orderTransaction->card_id;;
            $pt->payment_type = 'IYZICO';
            $pt->bank_last_message = $e->getMessage();
            $pt->save();

            throw new \Exception($e->getMessage());
        }
    }

    /**
     * Check payable can have bill fields.
     *
     * @return bool
     */
    public function isBillable(): bool
    {
        return !empty($this->bill_fields);
    }
}
