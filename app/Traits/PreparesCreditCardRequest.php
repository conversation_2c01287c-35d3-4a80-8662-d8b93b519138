<?php

namespace App\Traits;

use App\Exceptions\Card\CardRemoveException;
use App\Exceptions\Card\CardSaveException;
use App\Exceptions\Fields\CreditCardFieldsException;
use App\Models\CreditCard;
use Illuminate\Support\Facades\Validator;
use Iyzipay\Model\Card;
use Iyzipay\Model\CardInformation;
use Iyzipay\Options;
use Iyzipay\Request\CreateCardRequest;
use Iyzipay\Request\DeleteCardRequest;

trait PreparesCreditCardRequest
{


    /**
     * @param $attributes
     * @throws CreditCardFieldsException
     */
    private function validateCreditCardAttributes($attributes): void
    {
        $v = Validator::make($attributes, [
            'alias' => 'nullable',
            'holder' => 'required',
            'number' => 'required|digits_between:16,16',
            'month' => 'numeric|required|digits:2|max:12',
            'year' => 'integer|required|digits:4|min:2023',
            'cvc' => 'required|digits_between:3,4',
        ], [], [
            'alias' => 'Kart <PERSON>',
            'holder' => 'Ad Soyad',
            'number' => 'Kart Numarası',
            'month' => 'Son Kullanma Tarihi (Ay)',
            'year' => 'Son Kullanma Tarihi (Yıl)',
            'cvc' => 'CVV',
        ]);

        if ($v->fails()) {
            throw new CreditCardFieldsException(implode(',', $v->errors()->all()));
        }
    }

    /**
     * Prepares credit card on iyzipay.
     *
     * @param $payable
     * @param $attributes
     * @return Card
     * @throws CardSaveException
     */
    private function createCardOnIyzipay($payable, $attributes): Card
    {
        $cardRequest = $this->createCardRequest($payable, $attributes);
        try {
            $card = Card::create($cardRequest, $this->getOptions());
        } catch (\Exception $e) {
            logger('createCardOnIyzipay HATA ' . $e->getMessage());
            throw new CardSaveException();
        }

        unset($cardRequest);
        if ($card->getStatus() != 'success') {
            throw new CardSaveException($card->getErrorMessage());
        }
        return $card;
    }

    /**
     * Prepare card request class for iyzipay.
     *
     * @param $payable
     * @param $attributes
     * @return CreateCardRequest
     */
    private function createCardRequest($payable, $attributes): CreateCardRequest
    {
        $cardRequest = new CreateCardRequest();
        $cardRequest->setLocale($this->getLocale());
        $cardRequest->setEmail($payable->bill_fields->email);

        if (!empty($payable->iyzipay_key)) {
            $cardRequest->setCardUserKey($payable->iyzipay_key);
        }

        $cardRequest->setCard($this->createCardInformation($attributes));

        return $cardRequest;
    }

    /**
     * Removes a card on iyzipay
     *
     * @param CreditCard $creditCard
     * @throws CardRemoveException
     */
    private function removeCardOnIyzipay(CreditCard $creditCard): void
    {
        try {
            $result = Card::delete($this->removeCardRequest($creditCard), $this->getOptions());
        } catch (\Exception $e) {
            throw new CardRemoveException();
        }

        if ($result->getStatus() != 'success') {
            throw new CardRemoveException($result->getErrorMessage());
        }
    }

    /**
     * Prepares remove card request class for iyzipay.
     *
     * @param CreditCard $creditCard
     * @return DeleteCardRequest
     */
    private function removeCardRequest(CreditCard $creditCard): DeleteCardRequest
    {
        $removeRequest = new DeleteCardRequest();
        $removeRequest->setCardUserKey($creditCard->owner->iyzipay_key);
        $removeRequest->setCardToken($creditCard->token);
        $removeRequest->setLocale($this->getLocale());

        return $removeRequest;
    }

    /**
     * Prepares card information class for iyzipay
     *
     * @param $attributes
     * @return CardInformation
     */
    private function createCardInformation($attributes): CardInformation
    {
        $cardInformation = new CardInformation();
        $cardInformation->setCardAlias(isset($attributes['alias']) ? $attributes['alias'] : null);
        $cardInformation->setCardHolderName($attributes['holder']);
        $cardInformation->setCardNumber($attributes['number']);
        $cardInformation->setExpireMonth($attributes['month']);
        $cardInformation->setExpireYear($attributes['year']);

        return $cardInformation;
    }

    abstract protected function getLocale(): string;

    abstract protected function getOptions(): Options;
}
