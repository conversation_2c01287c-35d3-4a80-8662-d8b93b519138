<?php

namespace App\Filament\Pages;

use App\Models\OrderTransaction;
use App\States\Order\OrderAtLegalPursuit;
use Filament\Pages\Page;
use Illuminate\Database\Eloquent\Builder;

class OverdueOrderTransactionsPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $title = 'Gecikmiş Ödeme Planları';
    protected static ?string $slug = 'overdue-order-transactions';
    protected static ?string $navigationGroup = 'Tahsilat Yönetimi';
    protected static ?int $navigationSort = 2;
    protected static string $view = 'filament.pages.overdue-order-transactions-page';

    public $selectedTab = 'tab1';
    public $activeTabClass = 'text-gray-900 selected-tab';
    public $inactiveTabClass = 'text-gray-500 hover:text-gray-700 border-b-4 border-orange-900';

    public function changeSelectedTab($tab)
    {
        $this->selectedTab = $tab;
    }

    public static function getNavigationBadge(): ?string
    {
        return OrderTransaction::query()
            ->where('payment_status_id', 2)
            ->where('due_date', '<', now()->endOfDay())
            ->whereHas('order', function (Builder $query) {
                $query->where('status', '!=', OrderAtLegalPursuit::class);
            })
            ->whereHas('order.user', function (Builder $query) {
                $query->where('is_company', false);
            })
            ->groupBy('order_id')
            ->get()
            ->count();
    }
}
