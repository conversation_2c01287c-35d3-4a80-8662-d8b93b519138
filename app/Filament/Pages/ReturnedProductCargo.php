<?php

namespace App\Filament\Pages;

use App\Models\SupportRequest;
use App\States\SupportRequest\SupportProductWaiting;
use Filament\Pages\Page;

class ReturnedProductCargo extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $title = 'İade/Tamir Kargo';
    protected static ?string $slug = 'iade-kargo-yonetimi';
    protected static ?string $navigationGroup = 'Sipariş Yönetimi';
    protected static ?int $navigationSort = 5;
    protected static string $view = 'filament.pages.returned-product-cargo';

    public $selectedTab = 'tab1';
    public $activeTabClass = 'text-gray-900 selected-tab';
    public $inactiveTabClass = 'text-gray-500 hover:text-gray-700 border-b-4 border-orange-900';

    public function changeSelectedTab($tab)
    {
        $this->selectedTab = $tab;
    }

    protected static function getNavigationBadge(): ?string
    {
        return SupportRequest::whereNotIn('cs_sp_status', [\App\States\SupportRequestCustomerService\SupportCompleted::class, \App\States\SupportRequestCustomerService\SupportDenied::class, \App\States\SupportRequestCustomerService\SupportCancelled::class])
            ->whereIn('support_requests.status', [
                SupportProductWaiting::class,
            ])->count();
    }
}
