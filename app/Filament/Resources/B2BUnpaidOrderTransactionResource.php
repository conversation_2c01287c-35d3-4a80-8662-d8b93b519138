<?php

namespace App\Filament\Resources;

use App\Filament\Resources\B2BUnpaidOrderTransactionResource\Pages;
use App\Models\OrderTransaction;
use App\Models\PaymentStatus;
use App\States\Order\OrderAtLegalPursuit;
use App\States\OrderTransactionCustomerContact\CantReached;
use App\States\OrderTransactionCustomerContact\LineOff;
use App\States\OrderTransactionCustomerContact\NotContacted;
use App\States\OrderTransactionCustomerContact\Talked;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class B2BUnpaidOrderTransactionResource extends Resource
{
    protected static ?string $model = OrderTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Gecikmiş Ödemeler';
    protected static ?string $label = 'Gecikmiş Ödeme';
    protected static ?string $pluralLabel = 'Gecikmiş Ödemeler';
    protected static ?string $navigationGroup = 'Kurumsal Siparişler';
    protected static ?string $slug = 'b2b-unpaid-order-transactions';
    protected static ?int $navigationSort = 6;

    protected static function getNavigationBadge(): ?string
    {
        return static::getModel()::select([
            DB::raw('MIN(order_transactions.id) as id'),
            'order_transactions.order_id'
        ])
            ->where('payment_status_id', 2)
            ->leftJoin('orders', 'order_transactions.order_id', '=', 'orders.id')
            ->leftJoin('b2b_orders', 'orders.id', '=', 'b2b_orders.order_id')
            ->whereRaw('
                 COALESCE(DATE_ADD(order_transactions.due_date, INTERVAL b2b_orders.payment_due_days DAY), NOW()) <= NOW()
            ')
            ->whereHas('order', function ($query) {
                $query->where('status', '!=', OrderAtLegalPursuit::class);
            })
            ->whereHas('order.user', function ($query) {
                $query->where('is_company', true);
            })
            ->groupBy('order_transactions.order_id')
            ->get()->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Ödeme Planı Bilgileri')
                    ->schema([

                        Forms\Components\Placeholder::make('order_number')
                            ->label('Sipariş Numarası')
                            ->content(fn(?OrderTransaction $record): ?string => $record->order->order_number),

                        Forms\Components\Placeholder::make('due_date')
                            ->label('Kira Vade Tarihi')
                            ->content(fn(?OrderTransaction $record): ?string => $record->due_date->format('d.m.Y')),

                        Forms\Components\Placeholder::make('last_payment_check')
                            ->label('Son Ödeme Kontrol Tarihi')
                            ->content(fn(?OrderTransaction $record): ?string => $record->last_payment_check?->format('d.m.Y')),

                        Forms\Components\Placeholder::make('order.user.phone')
                            ->label('Telefon Numarası')
                            ->content(fn(?OrderTransaction $record): ?string => $record->order->user->phone),

                        Forms\Components\Placeholder::make('order.user.email')
                            ->label('E-Posta Adresi')
                            ->content(fn(?OrderTransaction $record): ?string => $record->order->user->email),

                        Forms\Components\Select::make('payment_status_id')
                            ->label('Ödeme Durumu')
                            ->disabled()
                            ->options(PaymentStatus::all()->pluck('name', 'id'))
                            ->required(),

                        Forms\Components\TextInput::make('amount')
                            ->label('Kiralama Tutarı')
                            ->numeric()
                            ->disabled()
                            ->mask(
                                fn(Forms\Components\TextInput\Mask $mask) => $mask
                                    ->numeric()
                                    ->decimalPlaces(2)
                                    ->decimalSeparator('.')
                            )
                            ->required(),

                        Forms\Components\Select::make('customer_contact_status')
                            ->label('Müşteri İletişim Durumu')
                            ->options([
                                CantReached::class => 'Ulaşılamadı',
                                LineOff::class => 'Hat Kapalı',
                                NotContacted::class => 'İletişime Geçilmedi',
                                Talked::class => 'Ulaşıldı',
                            ])
                            ->required(),
                    ])
                    ->columns(3),
                Forms\Components\Section::make('Ödeme Planı Notu')
                    ->schema([
                        Forms\Components\RichEditor::make('note')
                            ->label('Not')
                            ->disableToolbarButtons([
                                'attachFiles',
                                'codeBlock',
                            ])
                            ->maxLength(65535),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->visible(fn(): bool => auth()->user()->hasRole('Super Admin'))
                    ->size('sm')
                    ->sortable(),
                Tables\Columns\TextColumn::make('order.order_number')
                    ->label('Sipariş Numarası')
                    ->size('sm')
                    ->fontFamily('mono')
                    ->copyable()
                    ->copyMessage('Sipariş numarası kopyalandı')
                    ->searchable(isIndividual: true),
                Tables\Columns\TextColumn::make('order.user.full_name_db')
                    ->size('sm')
                    ->searchable(isIndividual: true)
                    ->wrap()
                    ->label('Müşteri Adı'),

                // vade gün
                Tables\Columns\TextColumn::make('payment_due_days')
                    ->size('sm')
                    ->label('Vade Günü')
                    ->getStateUsing(function (OrderTransaction $record): string {
                        if (!$record->order->b2bOrder) {
                            logger()->error('B2B Unpaid Order Transaction: ' . $record->order_id . ' has no b2b order');
                            return 'Hesaplanamadı';
                        }
                        return $record->order->b2bOrder->payment_due_days . ' Gün';
                    }),
                Tables\Columns\TextColumn::make('order.status_text')
                    ->size('sm')
                    ->label('Sipariş Durumu')
                    ->wrap(),
                // Tables\Columns\TextColumn::make('due_date')
                //     ->size('sm')
                //     ->label('Kira Vade Tarihi')
                //     ->alignEnd()
                //     ->date(),
                Tables\Columns\TextColumn::make('last_payed_rents_date')
                    ->size('sm')
                    ->label('Son Tahsilat Tarihi')
                    ->alignEnd()
                    ->date(),
                Tables\Columns\TextColumn::make('delayed_rent_count_in_months')
                    ->size('sm')
                    ->getStateUsing(function (OrderTransaction $record): string {
                        return $record->delayed_rent_count_in_months . ' Ay';
                    })
                    ->label('Gecikmiş Ay Sayısı')
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('amount')
                    ->size('sm')
                    ->label('Aylık Tutar')
                    ->formatStateUsing(fn(string $state): string => number_format($state, 2, ',', '.') . ' ₺')
                    ->fontFamily('mono')
                    ->alignRight(),
                Tables\Columns\TextColumn::make('total_delayed_amount')
                    ->size('sm')
                    ->label('Gecikmiş Tutar')
                    ->formatStateUsing(fn(string $state): string => number_format($state, 2, ',', '.') . ' ₺')
                    ->fontFamily('mono')
                    ->alignRight(),
                Tables\Columns\TextColumn::make('order.created_at')
                    ->size('sm')
                    ->label('Sipariş Tarihi')
                    ->alignEnd()
                    ->date(),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListB2BUnpaidOrderTransactions::route('/'),
            'create' => Pages\CreateB2BUnpaidOrderTransaction::route('/create'),
            //            'edit' => Pages\EditB2BUnpaidOrderTransaction::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->select([
                DB::raw('MIN(order_transactions.id) as id'),
                'order_transactions.order_id',
                'order_transactions.due_date',
                'order_transactions.payment_status_id',
                'order_transactions.amount',
                'order_transactions.card_id',
                'order_transactions.last_payment_check',
                'order_transactions.note',
                'order_transactions.created_at',
                'order_transactions.updated_at',
                'order_transactions.deleted_at',
                'order_transactions.customer_contact_status'
            ])
            ->leftJoin('orders', 'order_transactions.order_id', '=', 'orders.id')
            ->leftJoin('b2b_orders', 'orders.id', '=', 'b2b_orders.order_id')
            ->where('payment_status_id', 2)
            ->whereRaw('
                 COALESCE(DATE_ADD(order_transactions.due_date, INTERVAL b2b_orders.payment_due_days DAY), NOW()) <= NOW()
            ')
            ->whereHas('order', function ($query) {
                $query->where('status', '!=', OrderAtLegalPursuit::class);
            })
            ->whereHas('order.user', function ($query) {
                $query->where('is_company', true);
            })
            ->groupBy('order_transactions.order_id');
    }
}
