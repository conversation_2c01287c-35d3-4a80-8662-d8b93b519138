<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductStockReportResource\Pages;
use App\Filament\Resources\ProductStockReportResource\RelationManagers;
use App\Models\Lunar\ProductVariant;
use App\Models\ProductStock;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class ProductStockReportResource extends Resource
{
    protected static ?string $model = ProductStock::class;
    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Envanter';
    protected static ?string $pluralLabel = 'Envanter';
    protected static ?string $navigationGroup = 'Stok Yönetimi';
    protected static ?string $label = 'Envanter';
    protected static ?string $slug = 'envanter-raporu';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('inventory.name')
                    ->searchable(isIndividual: true)
                    ->label('Depo Adı'),
                Tables\Columns\TextColumn::make('product.product.name')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        $suitableProductVariants = ProductVariant::where('name', 'like', '%' . $search . '%')->pluck('id');
                        return $query->whereIn('product_id', $suitableProductVariants);
                    }, isIndividual: true)
                    ->label('Ürün Adı'),
                Tables\Columns\TextColumn::make('product.getVaariant')->label('Varyant'),
                Tables\Columns\TextColumn::make('sn')
                    ->label('S/N')
                    ->searchable(isIndividual: true),
                Tables\Columns\TextColumn::make('orderItem.order.order_number')
                    ->label('Sipariş No'),
                Tables\Columns\TextColumn::make('orderItem.order.created_at')
                    ->label('Sipariş Tarihi')
                    ->dateTime(),
                Tables\Columns\TextColumn::make('orderItem.order.statusText')
                    ->label('Sipariş Statüsü'),
                Tables\Columns\TextColumn::make('purchase_price')
                    ->label('Satın Alma Fiyatı')
                    ->formatStateUsing(fn($state) => number_format($state, 2, ',', '.') . ' ₺')
                    ->searchable(isIndividual: true)
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('supplier_vkn')
                    ->searchable(isIndividual: true)
                    ->label('Tedarikçi VKN')
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('invoice_number')
                    ->searchable(isIndividual: true)
                    ->label('Fatura No')
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('entried_at')
                    ->label('Depoya Eklenme Tarihi')
                    ->dateTime(),
                Tables\Columns\IconColumn::make('is_out_of_order')
                    ->boolean()
                    ->alignCenter()
                    ->label('Arızalı'),
                Tables\Columns\IconColumn::make('is_reserved')
                    ->boolean()
                    ->alignCenter()
                    ->label('Rezerve'),
                Tables\Columns\IconColumn::make('is_fixture')
                    ->boolean()
                    ->alignCenter()
                    ->label('Demirbaş'),
                Tables\Columns\TextColumn::make('owner_name')
                    ->searchable(isIndividual: true)
                    ->label('Ürün Sahibi'),
            ])
            ->filters([
                //
            ])
            ->actions([
//                Tables\Actions\EditAction::make(),
                Tables\Actions\ActionGroup::make([
                    Action::make('Ürün Varyant Değiştirme')
                        ->label(fn(ProductStock $record): ?string => 'Varyant Değiştirme')
                        ->color('danger')
                        ->icon('heroicon-o-switch-horizontal')
                        ->mountUsing(fn(Forms\ComponentContainer $form, ProductStock $record) => $form->fill([
                            'main_product_id' => $record->product->product->id,
                        ]))
                        ->action(function (ProductStock $record, array $data): ProductStock {
                            return $record->changeProductWithVariant($data);
                        })
                        ->form([
                                Forms\Components\Select::make('product_id')
                                    ->label('Varyant')
                                    ->options(fn(callable $get) => \App\Models\Lunar\ProductVariant::where('product_id', $get('main_product_id'))->get()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required(),
                            ]
                        )
                        ->requiresConfirmation(),

                    Action::make('Ürün Sil')
                        ->label(fn(ProductStock $record): ?string => 'Ürün Sil')
                        ->visible(fn(ProductStock $record): bool => $record->inventory_id === 1)
                        ->color('danger')
                        ->icon('heroicon-o-trash')
                        ->action(function (ProductStock $record, array $data): ProductStock {
                            $record->delete();
                            return $record;
                        })
                        ->requiresConfirmation(),
                ]),
            ])
            ->bulkActions([
                ExportBulkAction::make()
            ]);
    }


    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery();
//            ->withoutGlobalScopes([
//                SoftDeletingScope::class,
//            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductStockReports::route('/'),
            'create' => Pages\CreateProductStockReport::route('/create'),
//            'edit' => Pages\EditProductStockReport::route('/{record}/edit'),
        ];
    }
}
