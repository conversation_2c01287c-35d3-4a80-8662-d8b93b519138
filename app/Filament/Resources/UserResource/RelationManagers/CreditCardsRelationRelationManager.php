<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Table;
use Filament\Tables;

class CreditCardsRelationRelationManager extends RelationManager
{
    protected static string $relationship = 'creditCardsRelation';
    protected static ?string $label = 'Kullanıcı Kredi Kartları';

    protected static ?string $recordTitleAttribute = 'id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id'),
                Tables\Columns\TextColumn::make('alias')
                    ->label('Kart Adı'),
                Tables\Columns\TextColumn::make('holder')
                    ->label('Kart Sahibi'),
                Tables\Columns\TextColumn::make('bin_number')
                    ->label('Bin Numarası'),
                Tables\Columns\TextColumn::make('number')
                    ->label('Son 4 Hane'),
                Tables\Columns\TextColumn::make('month')
                    ->label('Ay'),
                Tables\Columns\TextColumn::make('year')
                    ->label('Yıl'),
                Tables\Columns\TextColumn::make('card_type')
                    ->label('Kart Tipi'),
            ])
            ->filters([])
            ->headerActions([])
            ->actions([
                //                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn() => auth()->id() == 1),
            ])
            ->bulkActions([])
            ->defaultSort('id', 'desc');
    }
}
