<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use App\Models\Order;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Table;
use Filament\Tables;

class OrdersRelationManager extends RelationManager
{
    protected static string $relationship = 'ordersByTCKNAndGsm';

    protected static ?string $label = 'Kullanıcı Siparişleri';
    protected static ?string $recordTitleAttribute = 'id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_number')
                    ->label('Sipariş Numarası')
                    ->url(fn(Order $record): string => route('filament.resources.all-orders.view', ['record' => $record]))
                    ->openUrlInNewTab(),
//                Tables\Columns\TextColumn::make('payment_method')
//                    ->label('Ödeme Metodu'),
                Tables\Columns\TextColumn::make('total')
                    ->label('Kira Bedeli')
                    ->formatStateUsing(fn(string $state): string => $state . ' ₺')
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('status_text')
                    ->label('Statü'),

//                Tables\Columns\TextColumn::make('ip_address')
//                    ->label('IP'),
//                Tables\Columns\TextColumn::make('coupon_id')
//                    ->label('Kupon'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Oluşturulma Tarihi')
                    ->dateTime()
                    ->alignEnd(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
//                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
//                Tables\Actions\EditAction::make(),
//                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
//                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }
}
