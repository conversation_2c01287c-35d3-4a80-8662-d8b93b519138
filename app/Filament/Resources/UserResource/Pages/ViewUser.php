<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Pages\Actions;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ViewRecord;

class ViewUser extends ViewRecord
{
    protected static string $resource = UserResource::class;

    protected function getActions(): array
    {
        return [
            Action::make('resetPassword')
                ->label('<PERSON><PERSON>re Sıfırla')
                ->color('danger')
                ->action(function (array $data) {
                    try {
                        $this->record->resetPassword($data);
                        Notification::make()
                            ->title('Başarılı')
                            ->body('Müşteri şifresi başarıyla değiştirildi')
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Hata')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
                ->form([
                    Forms\Components\TextInput::make('password')
                        ->required()
                        ->password()
                        ->label('Yeni Şifre'),

                    Forms\Components\TextInput::make('password_confirmation')
                        ->password()
                        ->required()
                        ->label('Yeni Şifre Tekrarı'),
                ])
                ->requiresConfirmation(),

            Actions\EditAction::make(),
        ];
    }
}
