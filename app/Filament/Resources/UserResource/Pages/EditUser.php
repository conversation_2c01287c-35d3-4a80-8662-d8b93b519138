<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use App\Jobs\SendFindexToIncluder;
use App\Jobs\SendFindexToSkorlabunu;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected function getActions(): array
    {
        return [
            Actions\ViewAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // if (isset($data['blocked_at'])) {
        //     $data['blocked_at'] = $data['blocked_at'] ? now() : null;
        // }

        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        if (isset($this->data['findex_document']) && !empty($this->data['findex_document']) && $record->findex_document !== head($this->data['findex_document'])) {
            SendFindexToIncluder::dispatch(head($this->data['findex_document']), $this->record->id);
            SendFindexToSkorlabunu::dispatch(head($this->data['findex_document']), $this->record->id);
        }

        $record->update($data);
        return $record;
    }
}
