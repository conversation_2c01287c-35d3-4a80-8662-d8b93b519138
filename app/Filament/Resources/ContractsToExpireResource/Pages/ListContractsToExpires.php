<?php

namespace App\Filament\Resources\ContractsToExpireResource\Pages;

use App\Filament\Resources\ContractsToExpireResource;
use Filament\Pages\Actions;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Model;
use Closure;

class ListContractsToExpires extends ListRecords
{
    protected static string $resource = ContractsToExpireResource::class;

    protected function getTableRecordClassesUsing(): ?Closure
    {
        return function (Model $record) {
            return match (true) {
                $record->order->last_due_date <= now() => "bg-red-300", // Son Ödeme Planı Tarihi Geçti ise KIRMIZI
                $record->contract_expired_at <= now() => "bg-orange-300", // Sözleşme Bitiş Tarihi Geçti ise TURUNCU
                default => '',
                //                default => throw new \Exception('Unexpected match value ' . $record->id),
            };
        };
    }

    protected function getActions(): array
    {
        return [];
    }
}
