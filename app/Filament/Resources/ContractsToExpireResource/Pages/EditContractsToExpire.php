<?php

namespace App\Filament\Resources\ContractsToExpireResource\Pages;

use App\Filament\Resources\ContractsToExpireResource;
use App\Models\Order\OrderItem;
use App\Models\OrderItems;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Filament\Forms;
use Illuminate\Database\Eloquent\Model;
use function Symfony\Component\Translation\t;


class EditContractsToExpire extends EditRecord
{
    protected static string $resource = ContractsToExpireResource::class;

    protected function getActions(): array
    {
        return [

            Action::make('Siparişe Git')
                ->label('Siparişe Git')
                ->color('primary')
                ->url(route('filament.resources.all-orders.view', $this->record->order->id), true),

            Action::make('Sözleşmeyi Uzat')
                ->label('Sözleşmeyi Uzat')
                ->color('danger')
                //                ->mountUsing(fn(Forms\ComponentContainer $form, OrderItem $record) => dd($record))
                //->action(action: fn(array $data) => $this->record->order->migrateTempOrderTransactionsToOrderTransactions())
                //                ->action(function (Model $record, array $data) {
                //                    dd($record);
                //                    $this->record->order->migrateTempOrderTransactionsToOrderTransactions($data['id']);
                //                    return redirect()->route('filament.resources.contracts-to-expire-1-3-6-12-months.index');
                //                })
                ->action(function (array $data) {
                    $this->record->order->migrateTempOrderTransactionsToOrderTransactions($this->record->id);
                    return redirect()->route('filament.resources.contracts-to-expire-1-3-6-12-months.index');
                })
                //                ->action(fn(OrderItem $record, array $data) => dd($record, $data))
                ->disabled(function () {
                    return $this->record->order->orderItems()->count() > 1;
                })
                ->form([])
                ->requiresConfirmation(),

            Action::make('Tüm Ürünlerin Sözleşmesini Uzat')
                ->label('Tüm Ürünlerin Sözleşmesini Uzat')
                ->color('danger')
                ->disabled(function () {
                    return $this->record->order->orderItems()->count() < 2;
                })
                ->action(function (array $data) {
                    $this->record->order->migrateTempOrderTransactionsToOrderTransactionsWithAllOrderItems($this->record->order->id);
                    return redirect()->route('filament.resources.contracts-to-expire-1-3-6-12-months.index');
                }),
        ];
    }
}
