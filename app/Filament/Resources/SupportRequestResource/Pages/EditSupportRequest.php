<?php

namespace App\Filament\Resources\SupportRequestResource\Pages;

use App\Filament\Resources\SupportRequestResource;
use App\States\SupportRequest\SupportProductWaiting;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditSupportRequest extends EditRecord
{
    protected static string $resource = SupportRequestResource::class;

    protected function getActions(): array
    {
        return [
//            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        if ($data['status'] == SupportProductWaiting::class) {
            $data['cargo_return_code'] = $record->order->sendReturnCargoCode($record->product);
        }

        $record->update($data);
        return $record;
    }
}
