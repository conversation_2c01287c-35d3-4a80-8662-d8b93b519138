<?php

namespace App\Filament\Resources;

use App\Filament\Resources\InventoryResource\Pages;
use App\Filament\Resources\InventoryResource\RelationManagers;
use App\Filament\Resources\InventoryResource\RelationManagers\InventoryItemsRelationManager;
use App\Models\Inventory;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;

class InventoryResource extends Resource
{
    protected static ?string $navigationIcon = 'heroicon-o-office-building';
    protected static ?string $navigationLabel = 'Depolar';
    protected static ?string $pluralLabel = 'Depolar';
    protected static ?string $navigationGroup = 'Stok Yönetimi';
    protected static ?int $navigationSort = 2;
    protected static ?string $label = 'Depo';

    protected static function getNavigationBadge(): ?string
    {
        return null;
        //        return static::getModel()::count();
    }

    protected static ?string $model = Inventory::class;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Depo Adı')
                    ->hintIcon('heroicon-o-office-building')
                    ->helperText('Depo adını eksiksiz yazınız'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable(isIndividual: true)
                    ->label('Depo Adı'),
            ])
            ->filters([]);
    }

    public static function getRelations(): array
    {
        return [
            InventoryItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInventories::route('/'),
            'create' => Pages\CreateInventory::route('/create'),
            'edit' => Pages\EditInventory::route('/{record}/edit'),
        ];
    }
}
