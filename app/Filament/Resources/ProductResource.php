<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Pages;
use App\Filament\Resources\ProductResource\RelationManagers\AttributesRelationManager1;
use App\Models\Lunar\ProductVariant;
use Filament\Forms;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Database\Eloquent\Builder;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class ProductResource extends Resource
{
    protected static ?string $model = ProductVariant::class;

    protected static ?string $navigationIcon = 'heroicon-o-desktop-computer';
    protected static ?string $navigationLabel = 'Ürün Varyantları';
    protected static ?string $label = 'Ürün';
    protected static ?string $pluralLabel = 'Ürün Varyantları';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationGroup = 'Ürün Yönetimi';

    protected static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                Forms\Components\Card::make()
                    ->schema([
                        //                        Forms\Components\TextInput::make('name')
                        //                            ->label('Ürün Adı')
                        //                            ->required(),
                        //
                        //                        Forms\Components\TextInput::make('barcode')
                        //                            ->label('KB Ürün Kodu')
                        //                            ->required(),
                        //
                        //                        BelongsToSelect::make('category_id')
                        //                            ->label('Kategori')
                        //                            ->relationship('category', 'name')
                        //                            ->required(),
                        //
                        //                        BelongsToSelect::make('brand_id')
                        //                            ->label('Marka')
                        //                            ->relationship('brand', 'name')
                        //                            ->required(),
                    ]),

                Forms\Components\Card::make()
                    ->schema([
                        SpatieMediaLibraryFileUpload::make('media')
                            ->collection('featured')
                            ->multiple()
                            ->maxFiles(5),
                    ])
                    ->columns(1),

                Forms\Components\Card::make()
                    ->schema([

                        Forms\Components\TextInput::make('slug')
                            ->label('Link')
                            ->required(),

                        Textarea::make('description')
                            ->label('Açıklama')
                            ->required(),

                        Toggle::make('in_service')
                            ->inline()
                            ->onIcon('heroicon-s-lightning-bolt')
                            ->offIcon('heroicon-s-user')
                            ->required(),
                    ]),

                Forms\Components\Card::make()
                    ->schema([
                        Repeater::make('variants')->relationship()->schema(
                            [
                                Forms\Components\TextInput::make('sku')
                                    ->label('SKU')
                                    ->required(),

                                Forms\Components\TextInput::make('stock_amount')
                                    ->label('Stok Miktarı')
                                    ->required(),

                                TextInput::make('stock_alert')
                                    ->label('Min Stok Adeti')
                                    ->required(),

                                TextInput::make("price")->label("Fiyat")->required(),

                                Forms\Components\Card::make()
                                    ->schema([
                                        Select::make('attributes')
                                            ->label('Attributeler')
                                            ->multiple()
                                            ->preload()
                                            ->relationship('attributes', 'name'),
                                    ]),
                            ]
                        )->defaultItems(1)->columnSpan("full")
                    ]),

            ])
            ->columns([
                'sm' => 3,
                'lg' => null,
            ]);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['sku', 'product.wp_product_id', 'teknosa_id'];
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->size('sm')
                    ->searchable(isIndividual: true)
                    ->label('ID'),

                Tables\Columns\TextColumn::make('product.name')
                    ->searchable(isIndividual: true)
                    ->sortable()
                    ->copyable()
                    ->wrap()
                    ->size('sm')
                    ->label('Adı'),

                Tables\Columns\TextColumn::make('getVaariant')
                    ->sortable()
                    ->size('sm')
                    ->label('Varyant'),

                Tables\Columns\TextColumn::make('purchasable')
                    ->formatStateUsing(fn($state) => $state == 'in_stock' ? 'Stoklu' : 'Stoksuz Satış')
                    ->size('sm')
                    ->label('Stok Satılabilirlik'),

                Tables\Columns\TextColumn::make('stock')
                    ->size('sm')
                    ->label('Stok'),

                Tables\Columns\TextColumn::make('sku')
                    ->searchable(isIndividual: true)
                    ->sortable()
                    ->size('sm')
                    ->wrap()
                    ->label('KB SKU'),

                Tables\Columns\TextColumn::make('product.brand.name')
                    ->searchable()
                    ->sortable()
                    ->size('sm')
                    ->label('Marka'),

                Tables\Columns\TextColumn::make('product.wp_product_id')
                    ->searchable()
                    ->sortable()
                    ->size('sm')
                    ->label('WP ID'),

                IconColumn::make('product.is_active')
                    ->size('sm')
                    ->label('Aktif')
                    ->boolean(),

                // Tables\Columns\TextColumn::make('teknosa_id')
                //     ->searchable()
                //     ->sortable()
                //     ->size('sm')
                //     ->label('Teknosa Oracle ID'),

                //                BooleanColumn::make('is_service_product')
                //                    ->label('Servis Ürünü')
                //                    ->trueIcon('heroicon-o-badge-check')
                //                    ->falseIcon('heroicon-o-x-circle'),

            ])
            ->filters([
                SelectFilter::make('brand')
                    ->label('Marka')
                    ->options(function () {
                        return \App\Models\Lunar\Brand::query()
                            ->orderBy('name')
                            ->pluck('name', 'id');
                    })
                    ->searchable()
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn(Builder $query, $brandId): Builder => $query->whereHas('product', function ($q) use ($brandId) {
                                $q->where('brand_id', $brandId);
                            })
                        );
                    }),

                TernaryFilter::make('status')
                    ->label('Aktif mi')
                    ->nullable()
                    ->queries(
                        true: fn(Builder $query) => $query->whereHas('product', fn($q) => $q->where('status', 'published')),
                        false: fn(Builder $query) => $query->whereHas('product', fn($q) => $q->where('status', 'draft')),
                        blank: fn(Builder $query) => $query,
                    ),

                SelectFilter::make('purchasable')
                    ->label('Stok Satılabilirlik')
                    ->options([
                        'in_stock' => 'Stoklu',
                        'always' => 'Stoksuz Satış',
                    ]),

                Filter::make('stock')
                    ->label('Stok Adet')
                    ->form([
                        Forms\Components\TextInput::make('stock_from')
                            ->label('Min Stok')
                            ->numeric(),
                        Forms\Components\TextInput::make('stock_to')
                            ->label('Max Stok')
                            ->numeric(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['stock_from'],
                                fn(Builder $query, $stock): Builder => $query->where('stock', '>=', $stock),
                            )
                            ->when(
                                $data['stock_to'],
                                fn(Builder $query, $stock): Builder => $query->where('stock', '<=', $stock),
                            );
                    })
            ])
            ->actions([])
            ->bulkActions(
                [ExportBulkAction::make()]
            )
            ->defaultSort('id', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //            AttributesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListItems::route('/'),
            //            'create' => Pages\CreateItem::route('/create'),
            //            'edit' => Pages\EditItem::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([])
            ->whereHas('product', function ($query) {
                $query->where('is_archived', false);
            });
        //            ->where('id', '<=', 2200);
    }
}
