<?php

namespace App\Filament\Resources\InventoryResource\Pages;

use App\Filament\Resources\InventoryResource;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ListRecords;

class ListInventories extends ListRecords
{
    protected static string $resource = InventoryResource::class;

    protected function getActions(): array
    {
        return [

            Action::make('create')
                ->label('Depo Oluştur')
                ->url(fn(): string => route('filament.resources.inventories.create'))
                ->color('success')
                ->button(),

            Action::make('servis')
                ->label('Servis Deposu')
                ->url(fn(): string => route('filament.resources.inventories.edit', ['record' => env('SERVICE_INVENTORY_ID')]))
                ->color('success')
                ->button(),

            Action::make('ariza')
                ->label('Zayii Deposu')
                ->url(fn(): string => route('filament.resources.inventories.edit', ['record' => 2528]))
                ->color('danger')
                ->button(),

            Action::make('ara')
                ->label('Ara Depo')
                ->url(fn(): string => route('filament.resources.inventories.edit', ['record' => env('INTERMEDIATE_INVENTORY_ID')]))
                ->color('primary')
                ->button(),
        ];
    }
}
