<?php

namespace App\Filament\Resources\InventoryResource\RelationManagers;

use App\Models\Inventory;
use App\Models\Lunar\ProductVariant;
use App\Models\ProductStock;
use Filament\Forms;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Actions\Position;
use pxlrbt\FilamentExcel\Actions\Tables\ExportAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use App\Exports\InventoryExport;

class InventoryItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'inventoryItems';
    protected static ?string $title = "Depoda Yer <PERSON>";

    protected static ?string $recordTitleAttribute = 'id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema(
                Forms\Components\Section::make(fn(?ProductStock $record): ?string => $record->product->product->getName . ' Ürünü Güncelleme')
                    ->schema([
                        Toggle::make('is_out_of_order')->inline()
                            ->label('Arızalı'),
                        Toggle::make('is_reserved')->inline()
                            ->label('Rezerve'),
                        Toggle::make('is_fixture')->inline()
                            ->label('Demirbaş'),
                        Forms\Components\Select::make('inventory_id')
                            ->label('Depo Transfer')
                            ->preload()
                            ->searchable()
                            ->options(Inventory::query()->pluck('name', 'id')),
                    ])
                    ->columns(3),
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('product.product.name')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        $suitableProductVariants = ProductVariant::where('name', 'like', '%' . $search . '%')->pluck('id');
                        return $query->whereIn('product_id', $suitableProductVariants);
                    }, isIndividual: true)
                    ->label('Ürün Adı'),
                Tables\Columns\TextColumn::make('product.getVaariant')->label('Varyant'),
                Tables\Columns\TextColumn::make('sn')
                    ->label('S/N')
                    ->searchable(isIndividual: true),
                Tables\Columns\TextColumn::make('entried_at')
                    ->sortable()
                    ->label('Depoya Eklenme Tarihi')
                    ->dateTime(),
                Tables\Columns\IconColumn::make('is_out_of_order')
                    ->sortable()
                    ->boolean()
                    ->label('Arızalı'),
                Tables\Columns\IconColumn::make('is_reserved')
                    ->sortable()
                    ->boolean()
                    ->label('Rezerve'),
                Tables\Columns\IconColumn::make('is_fixture')
                    ->boolean()
                    ->sortable()
                    ->label('Demirbaş'),
                Tables\Columns\TextColumn::make('owner_name')
                    ->label('Ürün Sahibi'),
            ])
            ->filters([])
            ->headerActions([
                ExportAction::make()
                    ->exports([
                        InventoryExport::make()
                            ->fromTable()
                            // ->queue()
                            ->withFilename(fn($livewire) => date('Y-m-d') . '-' .
                                Str::slug($livewire->getOwnerRecord()->name) .
                                '-urunler-listesi')
                    ])
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing(function (array $data, ProductStock $record): array {
                        if (isset($data['inventory_id']) && $record->inventory_id != $data['inventory_id']) {
                            $data['entried_at'] = now();
                        }
                        return $data;
                    }),
            ])
            ->bulkActions([])
            ->defaultSort('entried_at', 'desc');
    }

    protected function getTableActionsPosition(): ?string
    {
        return Position::BeforeCells;
    }
}
