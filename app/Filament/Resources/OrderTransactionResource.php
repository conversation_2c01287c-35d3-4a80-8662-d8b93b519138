<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OrderTransactionResource\Pages;
use App\Filament\Resources\OrderTransactionResource\RelationManagers;
use App\Models\OrderTransaction;
use App\Models\PaymentStatus;
use App\States\OrderTransactionCustomerContact\CantReached;
use App\States\OrderTransactionCustomerContact\LineOff;
use App\States\OrderTransactionCustomerContact\NotContacted;
use App\States\OrderTransactionCustomerContact\Talked;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class OrderTransactionResource extends Resource
{
    protected static ?string $model = OrderTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    protected static ?string $navigationLabel = 'Tüm Ödeme Planları';
    protected static ?string $label = 'Tüm Ödeme Planları';
    protected static ?string $navigationGroup = 'Tahsilat Yönetimi';
    protected static ?string $slug = 'all-order-transactions';
    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Ödeme Planı Bilgileri')
                    ->schema([
                        Forms\Components\Placeholder::make('order_number')
                            ->label('Sipariş Numarası')
                            ->content(fn(?OrderTransaction $record): ?string => $record->order->order_number),
                        Forms\Components\Placeholder::make('order_status')
                            ->label('Sipariş Durumu')
                            ->content(fn(?OrderTransaction $record): ?string => $record->order->status_text),
                        Forms\Components\Placeholder::make('customer_name')
                            ->label('Müşteri Adı')
                            ->content(fn(?OrderTransaction $record): ?string => $record->order->user->full_name_db),
                        Forms\Components\Placeholder::make('due_date')
                            ->label('Kira Vade Tarihi')
                            ->content(fn(?OrderTransaction $record): ?string => $record->due_date->format('d.m.Y')),
                        Forms\Components\Placeholder::make('last_payment_check')
                            ->label('Son Ödeme Kontrol Tarihi')
                            ->content(fn(?OrderTransaction $record): ?string => $record->last_payment_check?->format('d.m.Y')),
                        Forms\Components\TextInput::make('amount')
                            ->label('Kiralama Tutarı')
                            ->numeric()
                            ->disabled()
                            ->mask(
                                fn(Forms\Components\TextInput\Mask $mask) => $mask
                                    ->numeric()
                                    ->decimalPlaces(2)
                                    ->decimalSeparator('.')
                            )
                            ->required(),
                        Forms\Components\Select::make('payment_status_id')
                            ->label('Ödeme Durumu')
                            ->options(PaymentStatus::all()->pluck('name', 'id'))
                            ->required(),
                        Forms\Components\Select::make('customer_contact_status')
                            ->label('Müşteri İletişim Durumu')
                            ->options([
                                CantReached::class => 'Ulaşılamadı',
                                LineOff::class => 'Hat Kapalı',
                                NotContacted::class => 'İletişime Geçilmedi',
                                Talked::class => 'Ulaşıldı',
                            ])
                            ->required(),

                    ])
                    ->columns(3),
                Forms\Components\Section::make('Ödeme Planı Notu')
                    ->schema([
                        Forms\Components\RichEditor::make('note')
                            ->label('Not')
                            ->disableToolbarButtons([
                                'attachFiles',
                                'codeBlock',
                            ])
                            ->maxLength(65535),
                        //                        Forms\Components\TextInput::make('card_id')
                        //                            ->required(),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->visible(fn(): bool => auth()->user()->hasRole('Super Admin'))
                    ->size('sm')
                    ->sortable(),
                Tables\Columns\TextColumn::make('order.order_number')
                    ->searchable(isIndividual: true)
                    ->size('sm')
                    ->fontFamily('mono')
                    ->label('Sip. No.')
                    ->alignLeft()
                    ->sortable(),
                Tables\Columns\TextColumn::make('order.user.full_name_db')
                    ->searchable(isIndividual: true)
                    ->size('sm')
                    ->wrap()
                    ->label('Müşteri Adı'),
                Tables\Columns\TextColumn::make('order.user.phone')
                    ->searchable(isIndividual: true, query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('order', function ($query) use ($search) {
                            return $query->whereHas('user', function ($query) use ($search) {
                                return $query->where('phone', 'like', "%{$search}%");
                            });
                        });
                    })
                    ->size('sm')
                    ->label('Müşteri Tel'),
                Tables\Columns\TextColumn::make('paymentStatus.name')
                    ->label('Ödeme Durumu')
                    ->size('sm'),
                Tables\Columns\TextColumn::make('order.status_text')
                    ->wrap()
                    ->label('Sip. Statü')
                    ->size('sm'),
                Tables\Columns\TextColumn::make('payment_type')
                    ->label('Ödeme Tipi')
                    ->formatStateUsing(fn($state) => $state === '1' ? '' : $state)
                    ->size('sm'),
                Tables\Columns\TextColumn::make('due_date')
                    ->label('Vade Tarihi')
                    ->date()
                    ->size('sm')
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_payment_check')
                    ->label('Son Çekim Denemesi')
                    ->date()
                    ->size('sm'),
                Tables\Columns\TextColumn::make('order.orderProductList')
                    ->label('Ürün Adı')
                    ->size('sm')
                    ->html(),
                Tables\Columns\TextColumn::make('plan')
                    ->getStateUsing(function (OrderTransaction $record): null|string {
                        return $record->order?->orderItems->map(fn($item) => $item->planObj->name)->implode('<br>');
                    })
                    ->alignEnd()
                    ->label('Kira Sür.')
                    ->tooltip('Kiralama Süresi')
                    ->size('sm')
                    ->html(),
                Tables\Columns\TextColumn::make('amount')
                    ->formatStateUsing(fn($state) => number_format($state, 2, ',', '.') . '₺')
                    ->label('Aylık Tutar')
                    ->alignEnd()
                    ->fontFamily('mono')
                    ->size('sm'),
                Tables\Columns\TextColumn::make('order.created_at')
                    ->label('Sipariş Tarihi')
                    ->date()
                    ->size('sm'),
                Tables\Columns\TextColumn::make('customer_contact_status_label')
                    ->label('Müşteri İletişim Durumu')
                    ->size('sm'),

                //                Tables\Columns\TextColumn::make('last_payment_check')
                //                    ->dateTime(),
                //                Tables\Columns\TextColumn::make('note'),
                //
                //                Tables\Columns\TextColumn::make('card_id'),
                //                Tables\Columns\TextColumn::make('created_at')
                //                    ->dateTime(),
                //                Tables\Columns\TextColumn::make('updated_at')
                //                    ->dateTime(),
            ])
            ->filters([
                //                Tables\Filters\TrashedFilter::make(),
                Tables\Filters\Filter::make('payment_status_id')
                    ->form([
                        Forms\Components\Select::make('payment_status_id')
                            ->label('Tahsilat Statüsü')
                            ->options(PaymentStatus::pluck('name', 'id'))
                        //->placeholder(fn($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['payment_status_id'],
                                fn(Builder $query, $statu): Builder => $query->where('payment_status_id', $statu),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['payment_status_id'] ?? null) {
                            $indicators['payment_status_id'] = 'Tahsilat Durumu ' . PaymentStatus::find($data['payment_status_id'])->name . ' olarak filtrelenmiştir.';
                        }

                        return $indicators;
                    }),
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Ödene Planı Başlangıç Tarihi')
                            ->placeholder(fn($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Ödene Planı Bitiş Tarihi')
                            ->placeholder(fn($state): string => now()->format('M d, Y')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('due_date', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('due_date', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['created_from'] ?? null) {
                            $indicators['created_from'] = 'Şu tarihten itibaren ' . Carbon::parse($data['created_from'])->toFormattedDateString();
                        }
                        if ($data['created_until'] ?? null) {
                            $indicators['created_until'] = 'Şu tarihe kadar ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                        }

                        return $indicators;
                    }),
            ])
            ->actions([])
            ->bulkActions([
                ExportBulkAction::make()
            ])
            ->defaultSort('due_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\OrderTransactionRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrderTransactions::route('/'),
            'create' => Pages\CreateOrderTransaction::route('/create'),
            'view' => Pages\ViewOrderTransaction::route('/{record}'),
            'edit' => Pages\EditOrderTransaction::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return static::getModel()::query()
            ->withoutGlobalScopes([])
            ->select('order_transactions.*')
            ->join('orders', 'order_transactions.order_id', '=', 'orders.id')
            ->join('users', 'orders.user_id', '=', 'users.id')
            ->where('users.is_company', false);


        //->with(['order' => fn($query) => $query->whereIn('status', [OrderApproved::class, OrderAtLegalPursuit::class, OrderCompleted::class, OrderRenting::class, OrderShipped::class])]);
        //->whereIn('orders.status', [OrderApproved::class, OrderAtLegalPursuit::class, OrderCompleted::class, OrderRenting::class, OrderShipped::class]);

        //->orderBy('order_transactions.id', 'desc');
    }
}
