<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PriceResource\Pages;
use App\Filament\Resources\PriceResource\RelationManagers;
use App\Models\Lunar\Price;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Illuminate\Database\Eloquent\Builder;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class PriceResource extends Resource
{
    protected static ?string $model = Price::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Kiralama Fiyatları Toplu Güncelleme';
    protected static ?string $label = 'Fiyat Tanımı';
    protected static ?string $pluralLabel = 'Fiyat Tanımlamaları';
    protected static ?string $navigationGroup = 'Toplu İşlemler';
    protected static ?int $navigationSort = 1;

    // If you want to use left join on eloquent query, you need to set this to spesific id.
    protected static ?string $recordRouteKeyName = 'prices.id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(fn(?Price $record): ?string => $record->product->product->getName . ' ' . $record->subscriptionMonths->name . ' Kiralama Bedeli Güncelleme')
                    ->schema([

                        Forms\Components\TextInput::make('nonObjectPrice')
                            ->label('Kiralama Bedeli')
                            //->default(fn(?Price $record): ?int => $record->price->value)
                            ->required()
                            ->maxLength(255),

                        //                        Forms\Components\Placeholder::make('created_at')
                        //                            ->label('Sipaariş Tarihi')
                        //                            ->content(fn(?Order $record): ?string => $record?->created_at->timezone('Europe/Istanbul')->format('d.m.Y H:i:s'))
                        //                            ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateOrder)),

                    ])
                    ->columns(4),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->searchable(isIndividual: true)
                    ->sortable()
                    ->label('ID'),

                Tables\Columns\TextColumn::make('product.product.id')
                    ->searchable(isIndividual: true)
                    ->label('Ürün ID'),

                Tables\Columns\TextColumn::make('product.product.wp_product_id')
                    ->searchable(isIndividual: true)
                    ->label('WP Ürün ID'),

                Tables\Columns\TextColumn::make('product.product.getName')
                    ->label('Adı'),

                Tables\Columns\TextColumn::make('product.getVaariant')
                    ->label('Varyant'),

                Tables\Columns\TextColumn::make('product.purchasable')
                    ->label('Stoklu Satış'),

                Tables\Columns\TextColumn::make('product.product.category')
                    ->getStateUsing(function (Price $record): null|string {
                return $record->product->product->collections->map(fn($item) => $item->collection_name)->implode('<br>');
                    })
                    ->label('Kategoriler')
                    ->html(),

                Tables\Columns\TextColumn::make('product.sku')
                    ->searchable(isIndividual: true)
                    ->sortable()
                    ->label('KB SKU')
                    ->limit(20),

                Tables\Columns\TextColumn::make('product.product.brand.name')
                    ->searchable(isIndividual: true)
                    ->sortable()
                    ->label('Marka'),

                IconColumn::make('product.is_active')
                    ->label('Varyant Aktif')
                    ->boolean(),

                IconColumn::make('product.product.is_active')
                    ->label('Ürün Aktif')
                    ->boolean(),

                Tables\Columns\TextColumn::make('subscriptionMonths.name')
                    ->searchable()
                    ->sortable()
                    ->label('Kiralama Ay'),

                Tables\Columns\TextColumn::make('price')
                    ->label('Kira Bedeli')
                    ->formatStateUsing(fn(string $state): string => $state / 100 . ' ₺')
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('compare_price')
                    ->label('İndirimli Kira Bedeli')
                    ->formatStateUsing(fn(string $state): string => $state / 100 . ' ₺')
                    ->alignEnd(),
            ])
            ->filters([
                Tables\Filters\Filter::make('status')
                    ->form([
                        Forms\Components\Select::make('status')
                            ->label('Ürün Satış Statüsü')
                            ->options([
                                'draft' => 'Draft',
                                'published' => 'Published',
                            ]),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['status'],
                                fn(Builder $query, $status): Builder => $query->whereHas('product.product', function ($query) use ($status) {
                                    $query->where('status', $status);
                                }),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['status'] ?? null) {
                            $indicators['status'] = $data['status'] == 'published' ? 'Yayınlanmış ürünler olarak filtrelenmiştir.' : 'Taslak ürünler olarak filtrelenmiştir.';
                        }

                        return $indicators;
                    }),

                Tables\Filters\Filter::make('purchasable')
                    ->form([
                        Forms\Components\Select::make('purchasable')
                            ->label('Stoklu Satış')
                            ->options([
                                'in_stock' => 'Stok Takipli Satış',
                                'always' => 'Stok Takipsiz Satış',
                            ]),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['purchasable'],
                                fn(Builder $query, $purchasable): Builder => $query->whereHas('product.product', function ($query) use ($purchasable) {
                                    $query->where('purchasable', $purchasable);
                                }),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['purchasable'] ?? null) {
                            $indicators['purchasable'] = $data['purchasable'] == 'in_stock' ? 'Stok Takipli Satış olarak filtrelenmiştir.' : 'Stok Takipsiz Satış olarak filtrelenmiştir.';
                        }

                        return $indicators;
                    }),
            ])
            ->actions([])
            ->bulkActions([
                ExportBulkAction::make()
            ])
            ->defaultSort('id', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPrices::route('/'),
            'create' => Pages\CreatePrice::route('/create'),
            'edit' => Pages\EditPrice::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                //                SoftDeletingScope::class,
            ])
            ->whereNull('prices.deleted_at')
            ->whereHas('product.product', function ($query) {
                return $query
                    // ->where('status', 'published')
                    ->where('is_archived', false);
            });
    }
}
