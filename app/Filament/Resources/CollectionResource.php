<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CollectionResource\Pages;
use App\Filament\Resources\CollectionResource\RelationManagers;
use App\Models\Lunar\Collection;
use Filament\Forms;
use Filament\Forms\Components\RichEditor;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Livewire\TemporaryUploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Builder;

class CollectionResource extends Resource
{
    protected static ?string $model = Collection::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';

    protected static ?string $navigationLabel = 'Kategoriler';
    protected static ?string $label = 'Kategoriler';
    protected static ?string $pluralLabel = 'Kategoriler';
    protected static ?int $navigationSort = 3;
    //protected static ?string $slug = 'urunler';
    protected static ?string $navigationGroup = 'Ürün Yönetimi';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Kategori Bilgileri')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('Title')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('meta_description')
                            ->label('Meta Description')
                            ->required()
                            ->maxLength(255),
                        RichEditor::make('sub_category_note')
                            ->label('Alt Kategori Notu')
                            ->enableToolbarButtons($buttons = ['h1'])
                            ->columnSpan(2), // Enable toolbar buttons. See below for options.
                Forms\Components\Toggle::make('is_visible')
                    ->label('Görünür Kategori')
                    ->default(true),
                Forms\Components\FileUpload::make('icon')
                            ->image()
                            ->disk('s3')
                            ->directory('collections')
                            ->label('Icon')
                            ->imageResizeTargetWidth(100)
                            ->imageResizeTargetHeight(100)
                            ->visibility('public')
                            ->getUploadedFileUrlUsing(function ($record) {
                                return $record->icon ? Storage::disk('s3')->temporaryUrl($record->icon, now()->addMinutes(5)) : null;
                            })
                            ->getUploadedFileNameForStorageUsing(function (TemporaryUploadedFile $file, $record): string {
                                return (string) str($file->getClientOriginalName())->slug()->prepend($record->id . '-')->append('.' . $file->getClientOriginalExtension());
                            })
                            ->columnSpan(2),
                    ])
                    ->columns(2),
                //->columnSpan(['lg' => fn(?Collection $record) => $record === null ? 3 : 2]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->searchable()
                    ->label('ID'),
                Tables\Columns\TextColumn::make('collection_name')
                    ->sortable()
                    ->searchable(isIndividual: true)
                    ->label('Adı'),
                Tables\Columns\IconColumn::make('is_visible')
                    ->label('Görünür Kategori')
                    ->boolean(),
                Tables\Columns\TextColumn::make('title')
                    ->searchable(isIndividual: true)
                    ->sortable()
                    ->label('Title'),
                Tables\Columns\TextColumn::make('meta_description')
                    ->sortable()
                    ->searchable(isIndividual: true)
                    ->label('Meta Description'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCollections::route('/'),
            'create' => Pages\CreateCollection::route('/create'),
            'edit' => Pages\EditCollection::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereNull('deleted_at')
            ->orderBy('sort', 'asc');
    }
}
