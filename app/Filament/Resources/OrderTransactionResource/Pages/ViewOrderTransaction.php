<?php

namespace App\Filament\Resources\OrderTransactionResource\Pages;

use App\Filament\Resources\OrderTransactionResource;
use App\Models\OrderTransaction;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Database\Eloquent\Model;

class ViewOrderTransaction extends ViewRecord
{
    protected static string $resource = OrderTransactionResource::class;

    protected function getActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            OrderTransactionResource\Widgets\PaymentNote::class,
        ];
    }

    protected function resolveRecord($key): Model
    {
        $record = OrderTransaction::where('order_transactions.id', $key)->first();

        if ($record === null) {
            $this->redirect($this->getResource()::getUrl('index'));
        }

        return $record;
    }
}
