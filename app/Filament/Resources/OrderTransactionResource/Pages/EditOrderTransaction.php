<?php

namespace App\Filament\Resources\OrderTransactionResource\Pages;

use App\Filament\Resources\OrderTransactionResource;
use App\Models\OrderTransaction;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Spatie\ModelStates\Exceptions\TransitionNotFound;

class EditOrderTransaction extends EditRecord
{
    protected static string $resource = OrderTransactionResource::class;

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        if ($record->customer_contact_status != $data['customer_contact_status']) {
            try {
                $customerContactStatusClass = new $data['customer_contact_status'](OrderTransaction::class);
                $record->order->paymentNotes()->create([
                    'content' => 'Müşteri ile iletişim durumu değiştirildi: ' . $customerContactStatusClass->getLabel(),
                    'editor_id' => auth()->id(),
                ]);
            } catch (TransitionNotFound $e) {
                Notification::make()
                    ->title('Ödeme durumu bu şek<PERSON>tirilemez.')
                    ->danger()
                    ->send();

                $this->halt();

                return $record->fresh();
            }
        }

        $record->update($data);
        return $record;
    }

    protected function getActions(): array
    {
        return [
            Actions\ViewAction::make(),
        ];
    }

    protected function resolveRecord($key): Model
    {
        $record = OrderTransaction::where('order_transactions.id', $key)->first();

        if ($record === null) {
            $this->redirect($this->getResource()::getUrl('index'));
        }

        return $record;
    }
}
