<?php

namespace App\Filament\Resources\ScoringRequestResource\Pages;

use App\Filament\Resources\ScoringRequestResource;
use Filament\Resources\Pages\ListRecords;
use Filament\Pages\Actions;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use pxlrbt\FilamentExcel\Actions\Pages\ExportAction;

class ListScoringRequests extends ListRecords
{
    protected static string $resource = ScoringRequestResource::class;

    protected function getActions(): array
    {
        return [
            ExportAction::make()
                ->exports([
                    ExcelExport::make()
                        ->fromTable()
                        ->withFilename(date('Y-m-d') . '-scoring-requests-export')
                ])
                ->label('Excel\'e Aktar')
                ->icon('heroicon-o-document-download')
                ->visible(fn() => auth()->user()->hasRole('Super Admin')),
                
            Actions\CreateAction::make()
                ->label('Yeni Skorlama Talebi Oluştur')
                ->visible(fn() => auth()->user()->hasPermissionTo('create-scoring-requests'))
                ->icon('heroicon-o-plus'),
        ];
    }
}
