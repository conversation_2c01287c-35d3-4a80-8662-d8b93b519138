<?php

namespace App\Filament\Resources\ScoringRequestResource\Pages;

use App\Events\ScoringCompleted;
use App\Filament\Resources\ScoringRequestResource;
use App\Jobs\SendScoringToRedisJob;
use App\Services\ScoringResultService;
use App\States\ScoringRequest\ManuallyProcessedState;
use App\Models\ScoringLimit;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Http;

class EditScoringRequest extends EditRecord
{
    protected static string $resource = ScoringRequestResource::class;

    protected function getFormSchema(): array
    {
        $baseSchema = parent::getFormSchema();
        
        // Add scoring limit section before the actions section
        $scoringLimitSection = Forms\Components\Section::make('Mevcut Skor Limiti Bilgileri')
            ->description('Kullanıcının ve TCKN sahibinin mevcut skor limiti durumu')
            ->schema([
                // User Scoring Limit
                Forms\Components\Fieldset::make('Kullanıcı Skor Limiti')
                    ->schema([
                        Forms\Components\Placeholder::make('user_limit_status')
                            ->label('Durum')
                            ->content(function ($record) {
                                if (!$record || !$record->scoringSource || !$record->scoringSource->user_id) {
                                    return 'Kullanıcı bulunamadı';
                                }
                                
                                $user = \App\Models\User::find($record->scoringSource->user_id);
                                if (!$user) {
                                    return 'Kullanıcı bulunamadı';
                                }
                                
                                $limit = $user->getCurrentScoringLimit();
                                if (!$limit) {
                                    return 'Aktif limit yok';
                                }
                                
                                $daysRemaining = $limit->getDaysRemaining();
                                $status = $limit->isValid() ? 'Aktif' : 'Süresi Dolmuş';
                                
                                return "{$status} ({$daysRemaining} gün kaldı)";
                            })
                            ->helperText(fn($record) => $record && $record->scoringSource && $record->scoringSource->user_id ? 
                                'Kullanıcı ID: ' . $record->scoringSource->user_id : null),
                        
                        Forms\Components\Placeholder::make('user_approved_limit')
                            ->label('Onaylı Limit')
                            ->content(function ($record) {
                                if (!$record || !$record->scoringSource || !$record->scoringSource->user_id) {
                                    return '-';
                                }
                                
                                $user = \App\Models\User::find($record->scoringSource->user_id);
                                if (!$user) {
                                    return '-';
                                }
                                
                                $limit = $user->getCurrentScoringLimit();
                                return $limit ? number_format($limit->approved_limit, 2, ',', '.') . ' ₺' : '-';
                            }),
                        
                        Forms\Components\Placeholder::make('user_remaining_limit')
                            ->label('Kalan Limit')
                            ->content(function ($record) {
                                if (!$record || !$record->scoringSource || !$record->scoringSource->user_id) {
                                    return '-';
                                }
                                
                                $user = \App\Models\User::find($record->scoringSource->user_id);
                                if (!$user) {
                                    return '-';
                                }
                                
                                $limit = $user->getCurrentScoringLimit();
                                if (!$limit) {
                                    return '-';
                                }
                                
                                $percentage = $limit->approved_limit > 0 
                                    ? round(($limit->remaining_limit / $limit->approved_limit) * 100) 
                                    : 0;
                                
                                return number_format($limit->remaining_limit, 2, ',', '.') . ' ₺ (' . $percentage . '%)';
                            }),
                        
                        Forms\Components\Placeholder::make('user_score')
                            ->label('Skor')
                            ->content(function ($record) {
                                if (!$record || !$record->scoringSource || !$record->scoringSource->user_id) {
                                    return '-';
                                }
                                
                                $user = \App\Models\User::find($record->scoringSource->user_id);
                                if (!$user) {
                                    return '-';
                                }
                                
                                $limit = $user->getCurrentScoringLimit();
                                return $limit ? $limit->score : '-';
                            }),
                        
                        Forms\Components\Placeholder::make('user_valid_until')
                            ->label('Geçerlilik Tarihi')
                            ->content(function ($record) {
                                if (!$record || !$record->scoringSource || !$record->scoringSource->user_id) {
                                    return '-';
                                }
                                
                                $user = \App\Models\User::find($record->scoringSource->user_id);
                                if (!$user) {
                                    return '-';
                                }
                                
                                $limit = $user->getCurrentScoringLimit();
                                return $limit ? $limit->valid_until->format('d.m.Y H:i') : '-';
                            }),
                    ])
                    ->columns(5),
                
                // TCKN Scoring Limit
                Forms\Components\Fieldset::make('TCKN Skor Limiti')
                    ->schema([
                        Forms\Components\Placeholder::make('tckn_limit_status')
                            ->label('Durum')
                            ->content(function ($record) {
                                if (!$record || !$record->tckn) {
                                    return 'TCKN bulunamadı';
                                }
                                
                                $limit = ScoringLimit::getLatestValidByTckn($record->tckn);
                                if (!$limit) {
                                    return 'Aktif limit yok';
                                }
                                
                                $daysRemaining = $limit->getDaysRemaining();
                                $status = $limit->isValid() ? 'Aktif' : 'Süresi Dolmuş';
                                
                                return "{$status} ({$daysRemaining} gün kaldı)";
                            })
                            ->helperText(fn($record) => $record ? 'TCKN: ' . $record->tckn : null),
                        
                        Forms\Components\Placeholder::make('tckn_approved_limit')
                            ->label('Onaylı Limit')
                            ->content(function ($record) {
                                if (!$record || !$record->tckn) {
                                    return '-';
                                }
                                
                                $limit = ScoringLimit::getLatestValidByTckn($record->tckn);
                                return $limit ? number_format($limit->approved_limit, 2, ',', '.') . ' ₺' : '-';
                            }),
                        
                        Forms\Components\Placeholder::make('tckn_remaining_limit')
                            ->label('Kalan Limit')
                            ->content(function ($record) {
                                if (!$record || !$record->tckn) {
                                    return '-';
                                }
                                
                                $limit = ScoringLimit::getLatestValidByTckn($record->tckn);
                                if (!$limit) {
                                    return '-';
                                }
                                
                                $percentage = $limit->approved_limit > 0 
                                    ? round(($limit->remaining_limit / $limit->approved_limit) * 100) 
                                    : 0;
                                
                                return number_format($limit->remaining_limit, 2, ',', '.') . ' ₺ (' . $percentage . '%)';
                            }),
                        
                        Forms\Components\Placeholder::make('tckn_score')
                            ->label('Skor')
                            ->content(function ($record) {
                                if (!$record || !$record->tckn) {
                                    return '-';
                                }
                                
                                $limit = ScoringLimit::getLatestValidByTckn($record->tckn);
                                return $limit ? $limit->score : '-';
                            }),
                        
                        Forms\Components\Placeholder::make('tckn_valid_until')
                            ->label('Geçerlilik Tarihi')
                            ->content(function ($record) {
                                if (!$record || !$record->tckn) {
                                    return '-';
                                }
                                
                                $limit = ScoringLimit::getLatestValidByTckn($record->tckn);
                                return $limit ? $limit->valid_until->format('d.m.Y H:i') : '-';
                            }),
                    ])
                    ->columns(5),
            ])
            ->collapsible()
            ->collapsed(false);
        
        // Additional Data section for Super Admin only
        $additionalDataSection = Forms\Components\Section::make('Ek Veri Detayları (Additional Data)')
            ->description('Skorlama talebiyle ilgili tüm ek veriler')
            ->schema([
                Forms\Components\Placeholder::make('additional_data_display')
                    ->label('')
                    ->content(function ($record) {
                        if (!$record || !$record->additional_data || empty($record->additional_data)) {
                            return 'Ek veri bulunmamaktadır.';
                        }
                        
                        // Format the JSON data in a readable way
                        $html = $this->formatJsonAsHtml($record->additional_data);
                        
                        // Wrap in a styled container
                        return new \Illuminate\Support\HtmlString(
                            '<div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 font-mono text-sm">' . 
                            $html . 
                            '</div>'
                        );
                    })
                    ->extraAttributes(['class' => 'prose max-w-none'])
            ])
            ->visible(fn() => auth()->user() && auth()->user()->hasRole('Super Admin'))
            ->collapsible()
            ->collapsed(true);
        
        // Insert the sections after the first section (basic info)
        if (count($baseSchema) > 0) {
            array_splice($baseSchema, 1, 0, [$scoringLimitSection, $additionalDataSection]);
        } else {
            $baseSchema[] = $scoringLimitSection;
            $baseSchema[] = $additionalDataSection;
        }
        
        return $baseSchema;
    }

    protected function getActions(): array
    {
        return [
            Actions\Action::make('manual_approve')
                ->label('Manuel Onayla')
                ->color('success')
                ->icon('heroicon-o-check')
                ->visible(fn() => $this->canManuallyProcess())
                ->requiresConfirmation()
                ->modalHeading('Skorlama Talebini Manuel Onayla')
                ->modalSubheading('Bu işlem skorlama talebini manuel olarak onaylayacak ve Findex skorlamasını beklemeyecektir.')
                ->form([
                    Forms\Components\Textarea::make('manual_note')
                        ->label('Manuel İşlem Notu')
                        ->placeholder('Manuel onay sebebi ve detayları...')
                        ->rows(3)
                        ->maxLength(500)
                ])
                ->action(function (array $data): void {
                    $record = $this->record;
                    try {
                        // Manuel işlem state'ine geçir
                        $record->status->transitionTo(ManuallyProcessedState::class);

                        // Sonra onaylanmış state'ine geçir
                        // $record->status->transitionTo(ApprovedState::class);

                        // ScoringResult kaydını oluştur (manuel işlemde skor null)
                        app(ScoringResultService::class)->createResult(
                            scoringRequest: $record,
                            score: null,
                            isApproved: true,
                            approvedAmount: $record->requested_amount
                            // findexJournalId parametresi, varsayılan değeri null olduğu için çıkarıldı.
                            // processedAt parametresi, metod içinde null ise now() kullanıldığı ve
                            // sizin de now() gönderme niyetiniz olduğu için çıkarıldı.
                        );

                        // Manuel işlem bilgilerini güncelle
                        $record->update([
                            'manual_processed_by' => auth()->id(),
                            'manual_processed_at' => now(),
                            'manual_approved_amount' => $record->requested_amount,
                            'additional_data' => array_merge($record->additional_data ?? [], [
                                'manual_note' => $data['manual_note'] ?? null,
                                'manual_approval_reason' => 'admin_manual_approval'
                            ])
                        ]);

                        // Event tetikle (webhook gönderimi için)
                        ScoringCompleted::dispatch($record);

                        Log::info('Skorlama talebi manuel olarak onaylandı', [
                            'ulid' => $record->ulid,
                            'approved_amount' => $record->requested_amount,
                            'processed_by' => auth()->user()->name ?? auth()->user()->email,
                            'note' => $data['manual_note'] ?? null
                        ]);

                        $record->notes()->create([
                            'content' => 'Manuel onaylandı. Onaylanan tutar: ' . number_format($record->requested_amount, 2, ',', '.') . ' ₺. Not: ' . $data['manual_note'] ?? null,
                            'editor_id' => auth()->id()
                        ]);

                        Notification::make()
                            ->title('Skorlama Talebi Manuel Onaylandı')
                            ->body("Talep #{$record->ulid} başarıyla onaylandı. Onaylanan tutar: " . number_format($record->requested_amount, 2, ',', '.') . ' ₺')
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        Log::error('Manuel onay işlemi sırasında hata', [
                            'ulid' => $record->ulid,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);

                        Notification::make()
                            ->title('Hata Oluştu')
                            ->body('Manuel onay işlemi sırasında bir hata oluştu: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Actions\Action::make('manual_reject')
                ->label('Manuel Reddet')
                ->color('danger')
                ->icon('heroicon-o-x')
                ->visible(fn() => $this->canManuallyProcess())
                ->requiresConfirmation()
                ->modalHeading('Skorlama Talebini Manuel Reddet')
                ->modalSubheading('Bu işlem skorlama talebini manuel olarak reddedecek ve Findex skorlamasını beklemeyecektir.')
                ->form([
                    Forms\Components\Textarea::make('rejection_reason')
                        ->label('Red Nedeni')
                        ->placeholder('Manuel red sebebi ve detayları...')
                        ->required()
                        ->rows(3)
                        ->maxLength(500),
                    Forms\Components\Select::make('rejection_category')
                        ->label('Red Kategorisi')
                        ->options([
                            'insufficient_income' => 'Yetersiz Gelir',
                            'high_risk_profile' => 'Yüksek Risk Profili',
                            'incomplete_documents' => 'Eksik Belgeler',
                            'policy_violation' => 'Politika İhlali',
                            'other' => 'Diğer'
                        ])
                        ->required()
                ])
                ->action(function (array $data): void {
                    $record = $this->record;
                    try {
                        // Manuel işlem state'ine geçir
                        $record->status->transitionTo(ManuallyProcessedState::class);

                        // Sonra reddedilmiş state'ine geçir
                        //                        $record->status->transitionTo(RejectedState::class);

                        // ScoringResult kaydını oluştur (manuel redde skor null, onay false, tutar null)
                        app(ScoringResultService::class)->createResult(
                            scoringRequest: $record,
                            score: null,
                            isApproved: false,
                            approvedAmount: 0,
                        );

                        // Manuel işlem bilgilerini güncelle
                        $record->update([
                            'manual_processed_by' => auth()->id(),
                            'manual_processed_at' => now(),
                            'manual_approved_amount' => null,
                            'additional_data' => array_merge($record->additional_data ?? [], [
                                'manual_rejection_reason' => $data['rejection_reason'],
                                'manual_rejection_category' => $data['rejection_category'],
                                'manual_rejection_processed_by' => auth()->user()->name ?? auth()->user()->email
                            ])
                        ]);

                        // Event tetikle (webhook gönderimi için)
                        ScoringCompleted::dispatch($record);

                        Log::info('Skorlama talebi manuel olarak reddedildi', [
                            'ulid' => $record->ulid,
                            'rejection_reason' => $data['rejection_reason'],
                            'rejection_category' => $data['rejection_category'],
                            'processed_by' => auth()->user()->name ?? auth()->user()->email
                        ]);

                        Notification::make()
                            ->title('Skorlama Talebi Manuel Reddedildi')
                            ->body("Talep #{$record->ulid} reddedildi. Neden: {$data['rejection_reason']}")
                            ->danger()
                            ->send();
                    } catch (\Exception $e) {
                        Log::error('Manuel red işlemi sırasında hata', [
                            'ulid' => $record->ulid,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);

                        Notification::make()
                            ->title('Hata Oluştu')
                            ->body('Manuel red işlemi sırasında bir hata oluştu: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Actions\Action::make('partial_approve')
                ->label('Kısmen Onayla')
                ->color('warning')
                ->icon('heroicon-o-exclamation')
                ->visible(fn() => $this->canManuallyProcess())
                ->requiresConfirmation()
                ->modalHeading('Skorlama Talebini Kısmen Onayla')
                ->modalSubheading('Bu işlem skorlama talebini talep edilen tutardan daha düşük bir tutarla onaylayacaktır.')
                ->form([
                    Forms\Components\TextInput::make('approved_amount')
                        ->label('Onaylanan Tutar')
                        ->numeric()
                        ->required()
                        ->default(fn($record) => round($record->requested_amount * 1, 2))
                        ->minValue(1)
                        ->maxValue(fn($record) => $record->requested_amount - 1)
                        ->step(0.01)
                        ->suffix('₺')
                        ->helperText('Kısmi onayda tutar talep edilen tutardan düşük olmalıdır.'),
                    Forms\Components\Textarea::make('partial_reason')
                        ->label('Kısmi Onay Nedeni')
                        ->placeholder('Kısmi onay sebebi ve detayları...')
                        ->required()
                        ->rows(3)
                        ->maxLength(500)
                ])
                ->action(function (array $data): void {
                    $record = $this->record;
                    try {
                        // Manuel işlem state'ine geçir
                        $record->status->transitionTo(ManuallyProcessedState::class);

                        // Sonra onaylanmış state'ine geçir
                        //                        $record->status->transitionTo(ApprovedState::class);

                        // Kısmi onayda tutar ve oran
                        $approvedAmount = $data['approved_amount'];
                        $ratio = round(($approvedAmount / $record->requested_amount) * 100, 2);

                        // ScoringResult kaydını oluştur (kısmi onayda skor null, onay true, tutar kısmi)
                        app(ScoringResultService::class)->createResult(
                            scoringRequest: $record,
                            score: null,
                            isApproved: true,
                            approvedAmount: $approvedAmount
                        );

                        // Manuel işlem bilgilerini güncelle
                        $record->update([
                            'manual_processed_by' => auth()->id(),
                            'manual_processed_at' => now(),
                            'manual_approved_amount' => $approvedAmount,
                            'additional_data' => array_merge($record->additional_data ?? [], [
                                'partial_approval_reason' => $data['partial_reason'],
                                'is_partial_approval' => true,
                                'requested_vs_approved_ratio' => $ratio
                            ])
                        ]);

                        // Event tetikle (webhook gönderimi için)
                        ScoringCompleted::dispatch($record);

                        Log::info('Skorlama talebi kısmen onaylandı', [
                            'ulid' => $record->ulid,
                            'requested_amount' => $record->requested_amount,
                            'approved_amount' => $approvedAmount,
                            'approval_ratio' => $ratio . '%',
                            'processed_by' => auth()->user()->name ?? auth()->user()->email,
                            'reason' => $data['partial_reason']
                        ]);

                        Notification::make()
                            ->title('Skorlama Talebi Kısmen Onaylandı')
                            ->body("Talep #{$record->ulid} kısmen onaylandı. Onaylanan: " . number_format($approvedAmount, 2, ',', '.') . " ₺ (%{$ratio})")
                            ->warning()
                            ->send();
                    } catch (\Exception $e) {
                        Log::error('Kısmi onay işlemi sırasında hata', [
                            'ulid' => $record->ulid,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);

                        Notification::make()
                            ->title('Hata Oluştu')
                            ->body('Kısmi onay işlemi sırasında bir hata oluştu: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Actions\Action::make('reset_to_pending')
                ->label('Pending\'e Döndür')
                ->color('secondary')
                ->icon('heroicon-o-arrow-left')
                ->visible(fn() => auth()->user()->hasRole('Super Admin') && $this->canResetToPending())
                ->requiresConfirmation()
                ->modalHeading('Skorlama Talebini Pending Durumuna Döndür')
                ->modalSubheading('Bu işlem skorlama talebini tekrar pending durumuna döndürecek ve Findex skorlaması için yeniden işleme alacaktır.')
                ->action(function (array $data): void {
                    $record = $this->record;
                    try {
                        // Additional data'yı temizle ama orijinal veriyi koru
                        $cleanedAdditionalData = $record->additional_data ?? [];
                        unset(
                            $cleanedAdditionalData['manual_note'],
                            $cleanedAdditionalData['manual_approval_reason'],
                            $cleanedAdditionalData['manual_rejection_reason'],
                            $cleanedAdditionalData['manual_rejection_category'],
                            $cleanedAdditionalData['partial_approval_reason'],
                            $cleanedAdditionalData['is_partial_approval'],
                            $cleanedAdditionalData['requested_vs_approved_ratio']
                        );

                        // Manuel işlem alanlarını temizle
                        $record->update([
                            'manual_processed_by' => null,
                            'manual_processed_at' => null,
                            'manual_approved_amount' => null,
                            'additional_data' => $cleanedAdditionalData
                        ]);

                        // Pending state'ine döndür
                        $record->status->transitionTo(\App\States\ScoringRequest\PendingState::class);

                        Log::info('Skorlama talebi pending durumuna döndürüldü', [
                            'ulid' => $record->ulid,
                            'reset_by' => auth()->user()->name ?? auth()->user()->email
                        ]);

                        Notification::make()
                            ->title('Talep Pending Durumuna Döndürüldü')
                            ->body("Talep #{$record->ulid} tekrar işleme alınmak üzere pending durumuna döndürüldü.")
                            ->info()
                            ->send();
                    } catch (\Exception $e) {
                        Log::error('Pending\'e döndürme işlemi sırasında hata', [
                            'ulid' => $record->ulid,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);

                        Notification::make()
                            ->title('Hata Oluştu')
                            ->body('Pending durumuna döndürme işlemi sırasında bir hata oluştu: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Actions\Action::make('resend_to_redis')
                ->label('Skorlama Talebini Redise Tekrar Gönder')
                ->color('secondary')
                ->icon('heroicon-o-refresh')
                ->requiresConfirmation()
                ->modalHeading('Skorlama Talebini Redis\'e Tekrar Gönder')
                ->modalSubheading('Bu işlem skorlama talebini Redis\'e tekrar gönderecektir.')
                ->action(function (): void {
                    $record = $this->record;
                    try {
                        // Redis connection
                        $redis = app()->environment('testing') ? Redis::connection('testing') : Redis::connection();

                        // Redis'te var mı kontrol et
                        $customersListKey = 'kb-findex-ba:customers';
                        $existingCustomersJson = $redis->get($customersListKey);
                        $customersArray = $existingCustomersJson ? json_decode($existingCustomersJson, true) : [];

                        // Bu ULID ile kayıt var mı kontrol et
                        $existsInRedis = false;
                        foreach ($customersArray as $customer) {
                            if (isset($customer['id']) && $customer['id'] === $record->ulid) {
                                $existsInRedis = true;
                                break;
                            }
                        }

                        if ($existsInRedis) {
                            Notification::make()
                                ->title('Uyarı')
                                ->body("Bu skorlama talebi (#{$record->ulid}) zaten Redis'te mevcut.")
                                ->warning()
                                ->send();

                            Log::info('Skorlama talebi zaten Redis\'te mevcut', [
                                'ulid' => $record->ulid,
                                'checked_by' => auth()->user()->name ?? auth()->user()->email
                            ]);

                            return;
                        }

                        // Redis'te yoksa, önce redis_sent_at'i null yap
                        $record->update([
                            'redis_sent_at' => null
                        ]);

                        // SendScoringToRedisJob'ı tetikle
                        SendScoringToRedisJob::dispatch($record->id);

                        Log::info('Skorlama talebi Redis\'e tekrar gönderildi', [
                            'ulid' => $record->ulid,
                            'sent_by' => auth()->user()->name ?? auth()->user()->email
                        ]);

                        $record->notes()->create([
                            'content' => 'Skorlama talebi Redis\'e tekrar gönderildi.',
                            'editor_id' => auth()->id()
                        ]);

                        Notification::make()
                            ->title('Başarılı')
                            ->body("Skorlama talebi (#{$record->ulid}) Redis'e tekrar gönderildi.")
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        Log::error('Redis\'e tekrar gönderme işlemi sırasında hata', [
                            'ulid' => $record->ulid,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);

                        Notification::make()
                            ->title('Hata Oluştu')
                            ->body('Redis\'e gönderme işlemi sırasında bir hata oluştu: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Actions\Action::make('mock_webhook')
                ->label('Mock Skorlama Sonucu')
                ->color('secondary')
                ->icon('heroicon-o-beaker')
                ->visible(fn() => $this->canReceiveMockWebhook())
                ->requiresConfirmation()
                ->modalHeading('Mock Skorlama Webhook Gönder')
                ->modalSubheading('Bu işlem, skorlama sisteminden gelecek webhook\'u simüle edecektir.')
                ->form([
                    Forms\Components\Radio::make('mock_type')
                        ->label('Mock Türü')
                        ->options([
                            'approved' => 'Onaylandı (Tam Tutar)',
                            'rejected' => 'Reddedildi',
                            'partial' => 'Kısmi Onay',
                        ])
                        ->default('approved')
                        ->required()
                        ->reactive()
                        ->descriptions([
                            'approved' => 'Skor ≥350, talep edilen tutarın tamamı onaylanır',
                            'rejected' => 'Skor <350, talep reddedilir',
                            'partial' => 'Skor ≥350, belirtilen tutar kadar onaylanır',
                        ])
                        ->inline(false),

                    Forms\Components\TextInput::make('score')
                        ->label('Skor (0-500)')
                        ->numeric()
                        ->required()
                        ->minValue(0)
                        ->maxValue(500)
                        ->step(1)
                        ->default(fn($get) => match ($get('mock_type')) {
                            'approved' => 400,
                            'rejected' => 200,
                            'partial' => 375,
                            default => 400
                        })
                        ->reactive()
                        ->helperText(fn($get) => match ($get('mock_type')) {
                            'approved' => 'Onay için varsayılan skor (350+)',
                            'rejected' => 'Red için varsayılan skor (<350)',
                            'partial' => 'Kısmi onay için varsayılan skor (350+)',
                            default => '350 ve üzeri skorlar onaylanır'
                        }),

                    Forms\Components\TextInput::make('partial_amount')
                        ->label('Kısmi Onay Tutarı (₺)')
                        ->numeric()
                        ->visible(fn($get) => $get('mock_type') === 'partial')
                        ->required(fn($get) => $get('mock_type') === 'partial')
                        ->default(fn($record) => round($record->requested_amount * 0.7, 2))
                        ->minValue(1)
                        ->maxValue(fn($record) => $record->requested_amount - 1)
                        ->step(0.01)
                        ->suffix('₺')
                        ->helperText(fn($record) => 'Maksimum: ' . number_format($record->requested_amount, 2, ',', '.') . ' ₺'),

                    Forms\Components\Textarea::make('rejection_reason')
                        ->label('Red Nedeni (Opsiyonel)')
                        ->visible(fn($get) => $get('mock_type') === 'rejected')
                        ->placeholder('Mock red nedeni...')
                        ->rows(2)
                        ->maxLength(500)
                        ->helperText('Test amaçlı red nedeni'),

                    Forms\Components\TextInput::make('findex_journal_id')
                        ->label('Findex Journal ID (Opsiyonel)')
                        ->placeholder('TEST-' . uniqid())
                        ->maxLength(255)
                        ->helperText('Test amaçlı Findex Journal ID'),

                    Forms\Components\Toggle::make('use_current_time')
                        ->label('Şu anki zamanı kullan')
                        ->default(true)
                        ->reactive(),

                    Forms\Components\DateTimePicker::make('processed_at')
                        ->label('İşlenme Zamanı')
                        ->visible(fn($get) => !$get('use_current_time'))
                        ->default(now())
                ])
                ->action(function (array $data): void {
                    $record = $this->record;
                    try {
                        // Mock türüne göre skor ve payload hazırla
                        $mockType = $data['mock_type'];
                        $score = (int) $data['score'];

                        // Mock türüne göre skoru override et (güvenlik için)
                        if ($mockType === 'rejected' && $score >= 350) {
                            $score = 200; // Red için skoru 350'nin altına çek
                        } elseif (in_array($mockType, ['approved', 'partial']) && $score < 350) {
                            $score = 375; // Onay türleri için skoru 350'nin üzerine çık
                        }

                        // Webhook payload hazırla
                        $payload = [
                            'ulid' => $record->ulid,
                            'score' => $score,
                            'findex_journal_id' => $data['findex_journal_id'] ?: 'MOCK-' . uniqid(),
                            'processed_at' => $data['use_current_time'] ? now()->toIso8601String() : $data['processed_at']
                        ];

                        // Kısmi onay için özel işlem (ScoringResultWebhookController'ın işlenmesinden sonra manuel override)
                        $isPartialApproval = $mockType === 'partial';

                        // Local webhook endpoint'ine POST isteği gönder
                        $response = Http::post(url('/api/scoring/result'), $payload);

                        if ($response->successful()) {
                            $responseData = $response->json();

                            // Kısmi onay için manuel override işlemi
                            if ($isPartialApproval && isset($data['partial_amount'])) {
                                $record->refresh(); // En güncel veriyi al

                                if ($record->scoringResult) {
                                    $record->scoringResult->update([
                                        'approved_amount' => (float) $data['partial_amount']
                                    ]);

                                    // Additional data'ya kısmi onay bilgisini ekle
                                    $additionalData = $record->additional_data ?? [];
                                    $additionalData['mock_partial_approval'] = [
                                        'original_amount' => $record->requested_amount,
                                        'partial_amount' => (float) $data['partial_amount'],
                                        'ratio' => round(($data['partial_amount'] / $record->requested_amount) * 100, 2)
                                    ];
                                    $record->update(['additional_data' => $additionalData]);
                                }
                            }

                            Log::info('Mock skorlama webhook\'u başarıyla gönderildi', [
                                'ulid' => $record->ulid,
                                'mock_type' => $mockType,
                                'score' => $score,
                                'partial_amount' => $data['partial_amount'] ?? null,
                                'rejection_reason' => $data['rejection_reason'] ?? null,
                                'mock_by' => auth()->user()->name ?? auth()->user()->email,
                                'response' => $responseData
                            ]);

                            // Mock türüne göre not oluştur
                            $noteContent = match ($mockType) {
                                'approved' => sprintf('Mock skorlama: Tam onay. Skor: %d', $score),
                                'rejected' => sprintf(
                                    'Mock skorlama: Red. Skor: %d%s',
                                    $score,
                                    !empty($data['rejection_reason']) ? ', Neden: ' . $data['rejection_reason'] : ''
                                ),
                                'partial' => sprintf(
                                    'Mock skorlama: Kısmi onay. Skor: %d, Onaylanan: %s ₺',
                                    $score,
                                    number_format($data['partial_amount'] ?? 0, 2, ',', '.')
                                ),
                                default => sprintf('Mock skorlama sonucu. Skor: %d', $score)
                            };

                            $record->notes()->create([
                                'content' => $noteContent,
                                'editor_id' => auth()->id()
                            ]);

                            // Mock türüne göre bildirim oluştur
                            $notificationBody = match ($mockType) {
                                'approved' => sprintf(
                                    'Tam onay simüle edildi. Skor: %d - Onaylanan: %s ₺',
                                    $score,
                                    number_format($responseData['data']['approved_amount'] ?? 0, 2, ',', '.')
                                ),
                                'rejected' => sprintf('Red simüle edildi. Skor: %d', $score),
                                'partial' => sprintf(
                                    'Kısmi onay simüle edildi. Skor: %d - Onaylanan: %s ₺',
                                    $score,
                                    number_format($data['partial_amount'] ?? 0, 2, ',', '.')
                                ),
                                default => 'Mock skorlama tamamlandı'
                            };

                            Notification::make()
                                ->title('Mock Webhook Başarılı')
                                ->body($notificationBody)
                                ->success()
                                ->send();

                            // Sayfayı yenile
                            $this->redirect($this->getResource()::getUrl('edit', ['record' => $record]));
                        } else {
                            $errorMessage = $response->json()['message'] ?? 'Bilinmeyen hata';

                            Log::error('Mock skorlama webhook\'u başarısız', [
                                'ulid' => $record->ulid,
                                'mock_type' => $mockType,
                                'score' => $score,
                                'status' => $response->status(),
                                'response' => $response->body(),
                                'mock_by' => auth()->user()->name ?? auth()->user()->email
                            ]);

                            Notification::make()
                                ->title('Mock Webhook Başarısız')
                                ->body('Webhook simülasyonu başarısız: ' . $errorMessage)
                                ->danger()
                                ->send();
                        }
                    } catch (\Exception $e) {
                        Log::error('Mock webhook gönderimi sırasında hata', [
                            'ulid' => $record->ulid,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);

                        Notification::make()
                            ->title('Hata Oluştu')
                            ->body('Mock webhook gönderimi sırasında bir hata oluştu: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
        ];
    }

    /**
     * Kaydın manuel olarak işlenip işlenemeyeceğini kontrol et
     */
    private function canManuallyProcess(): bool
    {
        // Record null ise false döndür
        if (!$this->record || !$this->record->status) {
            return false;
        }

        // Sadece pending, sent_to_redis veya scored durumlarında manuel işlem yapılabilir
        $allowedStates = [
            \App\States\ScoringRequest\PendingState::class,
            \App\States\ScoringRequest\SentToRedisState::class,
            \App\States\ScoringRequest\ScoredState::class
        ];

        return in_array(get_class($this->record->status), $allowedStates);
    }

    /**
     * Kaydın pending'e döndürülüp döndürülemeyeceğini kontrol et
     */
    private function canResetToPending(): bool
    {
        // Record null ise false döndür
        if (!$this->record) {
            return false;
        }

        // Manuel işlenmiş kayıtlar pending'e döndürülebilir
        return $this->record->isManuallyProcessed();
    }

    /**
     * Kaydın mock webhook alıp alamayacağını kontrol et
     */
    private function canReceiveMockWebhook(): bool
    {
        // Record null ise false döndür
        if (!$this->record || !$this->record->status) {
            return false;
        }

        // Zaten skorlanmış kayıtlar mock webhook alamaz
        if ($this->record->scoringResult) {
            return false;
        }

        // Manuel işlenmiş kayıtlar mock webhook alamaz
        if ($this->record->isManuallyProcessed()) {
            return false;
        }

        // Sadece pending veya sent_to_redis durumlarında mock webhook alınabilir
        $allowedStates = [
            \App\States\ScoringRequest\PendingState::class,
            \App\States\ScoringRequest\SentToRedisState::class,
            // \App\States\ScoringRequest\ApprovedState::class,
        ];

        return in_array(get_class($this->record->status), $allowedStates);
    }
    
    /**
     * Format JSON data as HTML for display
     */
    private function formatJsonAsHtml($data, $indent = 0): string
    {
        $html = '';
        $spacing = str_repeat('&nbsp;', $indent * 4);
        
        if (is_array($data) || is_object($data)) {
            foreach ($data as $key => $value) {
                if (is_array($value) || is_object($value)) {
                    $html .= "<div>{$spacing}<strong>{$key}:</strong></div>";
                    $html .= $this->formatJsonAsHtml($value, $indent + 1);
                } else {
                    $displayValue = is_null($value) ? '<em>null</em>' : 
                                   (is_bool($value) ? ($value ? 'true' : 'false') : 
                                   htmlspecialchars((string)$value));
                    $html .= "<div>{$spacing}<strong>{$key}:</strong> {$displayValue}</div>";
                }
            }
        } else {
            $displayValue = is_null($data) ? '<em>null</em>' : 
                           (is_bool($data) ? ($data ? 'true' : 'false') : 
                           htmlspecialchars((string)$data));
            $html .= "<div>{$spacing}{$displayValue}</div>";
        }
        
        return $html;
    }
}
