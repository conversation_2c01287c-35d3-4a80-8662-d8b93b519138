<?php

namespace App\Filament\Resources\ProductResource\Pages;

use App\Filament\Resources\ProductResource;
use App\Models\Lunar\Product;
use App\Models\Lunar\ProductVariant;
use App\Models\ProductNonCast;
use Filament\Resources\Pages\ListRecords;
use Konnco\FilamentImport\Actions\ImportAction;
use Konnco\FilamentImport\Actions\ImportField;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use pxlrbt\FilamentExcel\Actions\Pages\ExportAction;

class ListItems extends ListRecords
{
    protected static string $resource = ProductResource::class;

    protected function getActions(): array
    {
        return [

            ExportAction::make()
                ->exports([
                    ExcelExport::make()
                        ->fromTable()
                        ->queue()
                        ->withFilename(date('Y-m-d') . '-products-variants-export')
                ]),

            ImportAction::make()
                ->fields([
                    ImportField::make('id')
                        ->label('ID')
                        ->helperText('Kiralabunu V2 Fiyat Güncelleme Export ID'),

                    //                    ImportField::make('urun_id')
                    //                        ->label('Ürün ID')
                    //                        ->helperText('Kiralabunu V2 Ürün ID'),

                    //                    ImportField::make('name')
                    //                        ->label('Ürün Adı'),

                    //                    ImportField::make('excerpt')
                    //                        ->label('Ürün Kısa Açıklama'),

                    ImportField::make('teknosa_id')
                        ->label('Teknosa Oracle ID'),


                ])
                //                ->mutateBeforeCreate(function ($row) {
                //                    //$row['price'] = (int)Str::of($row['price'])->remove(' ₺')->__toString() * 100;
                //
                //                    return $row;
                //                })
                ->handleRecordCreation(function ($data) {

                    // excel üzrinden veri id si varyant ama burada ürün id si üzerinden iş yapıyoruz
                    //$p = ProductNonCast::find($data['id']);

                    if (data_get($data, 'teknosa_id') == null || data_get($data, 'teknosa_id') == '#N/A') {
                        ray('teknosa_id null', $data['id']);
                        return ProductVariant::find($data['id']);
                    }

                    ray('teknosa_id null değil', $data['id']);
                    $p = ProductVariant::find($data['id']);
                    $p->update([
                        'teknosa_id' => $data['teknosa_id'],
                    ]);

                    //                    $dd = json_decode($p->attribute_data);
                    //
                    //                    ray($data['id']);
                    //
                    //                    $data = [
                    ////                            'name' => new TranslatedText(collect([
                    ////                                'tr' => new Text(optional($dd)->name?->tr)
                    ////                            ])),
                    ////                            'excerpt' => new TranslatedText(collect([
                    ////                                'tr' => new Text($data['excerpt'] ?? optional($dd)->excerpt?->tr)
                    ////                            ])),
                    ////                            'description' => new TranslatedText(collect([
                    ////                                'tr' => new Text(optional($dd)->description?->tr)
                    ////                            ]))
                    //                        'name' => [
                    //                            'value' => [
                    //                                'tr' => optional($dd)->name?->value->tr
                    //                            ],
                    //                            'field_type' => 'Lunar\FieldTypes\TranslatedText',
                    //                        ],
                    //                        'excerpt' => [
                    //                            'value' => [
                    //                                'tr' => optional($dd)->excerpt?->value->tr
                    //                            ],
                    //                            'field_type' => 'Lunar\FieldTypes\TranslatedText',
                    //                        ],
                    //                        'description' => [
                    //                            'value' => [
                    //                                'tr' => optional($dd)->description?->value->tr
                    //                            ],
                    //                            'field_type' => 'Lunar\FieldTypes\TranslatedText',
                    //                        ],
                    //                    ];

                    //                    $p->update([
                    //                        'attribute_data' => json_encode($data),
                    //                    ]);

                    return $p;
                })
        ];
    }
}
