<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContractsToExpirePlan5Resource\Pages;
use App\Filament\Resources\ContractsToExpirePlan5Resource\RelationManagers;
use App\Models\Order\OrderItem;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;

class ContractsToExpirePlan5Resource extends Resource
{
    protected static ?string $model = OrderItem::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Sözleşmesi Güncellenecekler';
    protected static ?string $label = 'Sözleşmesi Güncellenecekler';
    protected static ?string $pluralLabel = 'Sözleşmesi Güncellenecekler';
    protected static ?string $navigationGroup = 'Tahsilat Yönetimi';
    protected static ?string $slug = 'contracts-to-expire-18-months';
    protected static ?int $navigationSort = 8;
    protected static bool $shouldRegisterNavigation = false;


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order.id')
                    ->searchable(isIndividual: true)
                    ->sortable()
                    ->label('ID'),
                Tables\Columns\TextColumn::make('order.user.full_name')
                    ->searchable(['first_name', 'last_name'], isIndividual: true)
                    ->label('Kullanıcı'),
                Tables\Columns\TextColumn::make('order.order_number')
                    ->searchable(isIndividual: true)
                    ->label('Sipariş Numarası'),
//                Tables\Columns\TextColumn::make('payment_method')
//                    ->label('Ödeme Metodu'),
                Tables\Columns\TextColumn::make('order.total')
                    ->label('Kira Bedeli')
                    ->formatStateUsing(fn(string $state): string => $state . ' ₺')
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('product.product.getName')
//                    ->searchable(query: function (Builder $query, string $search): Builder {
//                        return $query
//                            ->with('product.product')
//                            ->where('last_name', 'like', "%{$search}%");
//                    })
                    ->label('Ürün Adı')
                    ->html(),

                Tables\Columns\TextColumn::make('planObj.name')
                    ->alignEnd()
                    ->label('Kiralama Süresi')
                    ->html(),

                Tables\Columns\TextColumn::make('order2.statusText')
                    ->label('Statü'),

                Tables\Columns\TextColumn::make('contract_expired_at')
                    ->label('Sözleşme Bitiş Tarihi')
                    ->dateTime()
                    ->sortable()
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('order.created_at')
                    ->label('Sipariş Tarihi')
                    ->sortable()
                    ->dateTime()
                    ->alignEnd(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContractsToExpirePlan5s::route('/'),
            'create' => Pages\CreateContractsToExpirePlan5::route('/create'),
            'edit' => Pages\EditContractsToExpirePlan5::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
            ])
            ->whereBetween('order_items.contract_expired_at', [now()->subYears(10), now()->addMonth()])
            ->where('order_items.plan', '=', 5);
//            ->whereHas('product.product', function ($query) {
//                return $query->where('status', 'published');
//            });
    }
}
