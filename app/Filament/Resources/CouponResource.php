<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CouponResource\Pages;
use App\Filament\Resources\CouponResource\RelationManagers\ProductsRelationManager;
use App\Models\Coupon;
use App\Models\Lunar\SubscriptionMonths;
use Closure;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;

class CouponResource extends Resource
{
    protected static ?string $model = Coupon::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Kuponlar';
    protected static ?string $label = 'Kupon';
    protected static ?string $pluralLabel = 'Kuponlar';
    protected static ?string $navigationGroup = 'Pazarlama';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Sipariş Bilgileri')
                    ->schema([
                        Forms\Components\TextInput::make('code')
                            ->label('Kupon Kodu')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('description')
                            ->label('Açıklama')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Select::make('type')
                            ->label('Tip')
                            ->options(
                                ['fixed' => 'Sabit', 'rate' => 'Yüzde']
                            )
                            ->required(),
                        Forms\Components\TextInput::make('value')
                            ->label('Değer')
                            ->required(),
                        Forms\Components\TextInput::make('limit')
                            ->label('Kullanım Sınırı')
                            ->numeric()
                            ->minValue(1)
                            ->required(),
                        Forms\Components\TextInput::make('min_cart_amount')
                            ->label('Min. Sepet Tutarı'),
                        Forms\Components\TextInput::make('max_cart_amount')
                            ->label('Max. Sepet Tutarı'),
                        Forms\Components\Toggle::make('published')
                            ->label('Yayında')
                            ->required(),
                        Forms\Components\DateTimePicker::make('start_date')
                            ->label('Başlangıç Tarihi')
                            ->required(),
                        Forms\Components\DateTimePicker::make('due_date')
                            ->label('Bitiş Tarihi')
                            ->required(),
                        Forms\Components\Toggle::make('is_new_user_coupon')
                            ->label('Yeni Kullanıcı Kuponu'),
                        Forms\Components\Toggle::make('is_category_coupon')
                            ->label('Kategori Kuponu'),
                        Forms\Components\Toggle::make('is_product_coupon')
                            ->label('Ürün Kuponu'),
                        Forms\Components\Toggle::make('is_disabled_on_discounted_products')
                            ->label('İndirimli Ürünlerde Kullanılamaz'),
                        Forms\Components\TextInput::make('customer_usage_limit')
                            ->label('Müşteri Kullanım Sınırı')
                            ->numeric()
                            ->minValue(1)
                            ->required(),
                        Forms\Components\Select::make('effected_months')
                            ->label('Kupon İlk Kaç Aya Etkili')
                            ->options(
                                SubscriptionMonths::get()->pluck('name', 'id')
                            ),
                        Forms\Components\Toggle::make('is_subscription_months_rules_enabled')
                            ->label('Abonelik Ay Kuralları Etkin')
                            ->reactive(),
                        Forms\Components\Select::make('rule_operator')
                            ->label('Kural Operatörü')
                            ->options(
                                [
                                    '=' => '=', '<' => '<', '>' => '>', '<=' => '<=', '>=' => '>=',
                                ]
                            )
                            ->visible(fn(Closure $get) => $get('is_subscription_months_rules_enabled') === true),
                        Forms\Components\Select::make('subscription_months_id')
                            ->label('Değer')
                            ->options(
                                SubscriptionMonths::get()->pluck('name', 'id')
                            )
                            ->visible(fn(Closure $get) => $get('is_subscription_months_rules_enabled') === true),

                    ])
                    ->columns(4)
                    ->columnSpan(['lg' => fn(?Coupon $record) => $record === null ? 3 : 2]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->searchable()
                    ->sortable()
                    ->label('Kupon Kodu'),
                Tables\Columns\TextColumn::make('type')
                    ->label('Tip'),
                Tables\Columns\TextColumn::make('value')
                    ->label('Değer')->sortable()->alignEnd(),
                Tables\Columns\TextColumn::make('limit')
                    ->label('Kullanım Sınırı')->sortable()->alignEnd(),
                Tables\Columns\TextColumn::make('min_cart_amount')
                    ->label('Min. Sepet Tutarı')->sortable()->alignEnd(),
                Tables\Columns\TextColumn::make('max_cart_amount')
                    ->label('Max. Sepet Tutarı')->sortable()->alignEnd(),
                Tables\Columns\IconColumn::make('published')
                    ->boolean()
                    ->label('Yayında'),
                Tables\Columns\TextColumn::make('start_date')
                    ->dateTime()
                    ->label('Başlangıç Tarihi'),
                Tables\Columns\TextColumn::make('customer_usage_limit')
                    ->label('Müşteri Kulllanım Limiti')->sortable()->alignEnd(),
                Tables\Columns\IconColumn::make('is_new_user_coupon')
                    ->boolean()
                    ->label('Yeni Kullanıcı Kuponu'),
                Tables\Columns\TextColumn::make('due_date')
                    ->dateTime()
                    ->label('Bitiş Tarihi'),
                Tables\Columns\TextColumn::make('used_total')
                    ->label('Kullanım Sayısı')->sortable()->alignEnd(),
                Tables\Columns\TextColumn::make('user_total')
                    ->label('Kullanıcı Sayısı')->sortable()->alignEnd(),
                Tables\Columns\TextColumn::make('description')
                    ->searchable()
                    ->label('Açıklama')
                    ->limit(50),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->label('Oluşturulma Tarihi'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            ProductsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCoupons::route('/'),
            'create' => Pages\CreateCoupon::route('/create'),
            'edit' => Pages\EditCoupon::route('/{record}/edit'),
        ];
    }
}
