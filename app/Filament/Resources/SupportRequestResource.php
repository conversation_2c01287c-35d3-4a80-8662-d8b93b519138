<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SupportRequestResource\Pages;
use App\Filament\Resources\SupportRequestResource\RelationManagers;
use App\Models\SupportRequest;
use App\Models\SupportRequestType;
use App\States\SupportRequest\SupportCompleted;
use App\States\SupportRequest\SupportProductAtService;
use App\States\SupportRequest\SupportProductControl;
use App\States\SupportRequest\SupportProductWaiting;
use App\States\SupportRequest\SupportReceived;
use App\States\SupportRequest\SupportTimeout;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;

class SupportRequestResource extends Resource
{
    protected static ?string $model = SupportRequest::class;
    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Destek Kayıtları';
    protected static ?string $label = 'Destek Kayıtları';
    protected static ?string $pluralLabel = 'Destek Kayıtları';
    protected static ?string $navigationGroup = 'Kullanıcı Yönetimi';

    protected static function getNavigationBadge(): ?string
    {
        return static::getModel()::whereNotIn('cs_sp_status', [\App\States\SupportRequestCustomerService\SupportCompleted::class, \App\States\SupportRequestCustomerService\SupportDenied::class, \App\States\SupportRequestCustomerService\SupportCancelled::class])->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Destek Talep Bilgileri')
                    ->schema([
                        Forms\Components\Placeholder::make('supportProduct')
                            ->label('Ürün Adı')
                            ->content(fn(?SupportRequest $record): ?string => $record?->supportProduct),

                        Forms\Components\Placeholder::make('user_id')
                            ->label('Kullanıcı')
                            ->content(fn(?SupportRequest $record): ?string => $record?->user->full_name),
                        Forms\Components\Placeholder::make('support_requests_type_id')
                            ->label('Destek Tipi')
                            ->content(fn(?SupportRequest $record): ?string => $record?->supportRequestType->name),

                        Forms\Components\TextInput::make('order_id')
                            ->label('Sipariş No')
                            //->st(fn(?SupportRequest $record): ?string => $record?->order->order_number)
                            ->disabled(true)
                            ->suffixAction(fn(?string $state): Action => Action::make('visit')
                                ->icon('heroicon-s-external-link')
                                ->url(
                                    "/admin/all-orders/{$state}",
                                    shouldOpenInNewTab: true,
                                )
                            ),

                        Forms\Components\Textarea::make('customer_message')
                            ->label('Müşteri Mesajı')
                            ->disabled(),

                        Forms\Components\DateTimePicker::make('created_at')
                            ->label('Talep Oluşturulma Tarihi')
                            ->disabled(),

                        Forms\Components\Placeholder::make('statusText')
                            ->label('Durum')
                            ->content(fn(?SupportRequest $record): ?string => $record?->statusText),

                        Forms\Components\Select::make('status')
                            ->label('Destek Durumu')
                            ->options([
                                SupportReceived::class => 'Talep Alındı',
                                SupportProductWaiting::class => 'Ürün Bekleniyor',
                                SupportProductControl::class => 'İnceleme Süreci',
                                SupportProductAtService::class => 'Servis Süreci',
                                SupportCompleted::class => 'Çözüldü',
                                SupportTimeout::class => 'Ürün istenilen sürede ulaşmadı',
                            ])
                            ->required(),

                        Forms\Components\Select::make('cs_sp_status')
                            ->label('Müşteri Hizmetleri Destek Durumu')
                            ->options([
                                \App\States\SupportRequestCustomerService\SupportReceived::class => 'Talep Alındı',
                                \App\States\SupportRequestCustomerService\SupportProcessed::class => 'İşleme Alındı',
                                \App\States\SupportRequestCustomerService\SupportApproved::class => 'Onaylandı',
                                \App\States\SupportRequestCustomerService\SupportCompleted::class => 'Çözüldü',
                                \App\States\SupportRequestCustomerService\SupportDenied::class => 'Reddedildi',
                                \App\States\SupportRequestCustomerService\SupportCancelled::class => 'İptal Edildi',
                            ])
                            ->required(),
                    ])
                    ->columns(3)
                    ->columnSpan(['lg' => fn(?SupportRequest $record) => $record === null ? 3 : 2]),

//                Forms\Components\Section::make('HelpScout')
//                    ->schema([
//                        Forms\Components\Placeholder::make('supportProduct')
//                            ->label('Yazışma')
//                            ->content(fn(?SupportRequest $record): ?string => $record?->getHelpScoutThread),
//                    ])
//                    ->collapsible()
//                    ->columns(3)
//                    ->columnSpan(['lg' => fn(?SupportRequest $record) => $record === null ? 3 : 2]),

                \App\Forms\Components\HelpScout::make('HelpScout')
//                    ->schema([
//                        Forms\Components\Placeholder::make('supportProduct')
//                            ->label('Yazışma')
//                            ->content(fn(?SupportRequest $record): ?string => $record?->getHelpScoutThread),
//                    ])
                    ->setHelpScoutThread(fn(SupportRequest $record): ?string => $record?->getHelpScoutThread)
                    ->setLink(fn(SupportRequest $record): ?string => $record?->getHelpScoutLink)
                    ->collapsible()
                    ->columnSpan(['lg' => 3]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->searchable(isIndividual: true),
                Tables\Columns\TextColumn::make('supportProduct')
                    ->label('Ürün Adı'),
                Tables\Columns\TextColumn::make('user.full_name')
                    ->searchable(['first_name', 'last_name'], isIndividual: true)
                    ->label('Kullanıcı'),
                Tables\Columns\TextColumn::make('supportRequestType.name')
                    ->label('Destek Tipi'),
                Tables\Columns\TextColumn::make('customer_message')
                    ->label('Mesaj')
                    ->limit(50),
                Tables\Columns\TextColumn::make('statusText')
                    ->label('Durum'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Oluşturulma Tarihi')
                    ->dateTime(),
            ])
            ->filters([
                Tables\Filters\Filter::make('cs_sp_status')
                    ->form([
                        Forms\Components\Select::make('cs_sp_status')
                            ->label('MHZ Talep Statüsü')
                            ->options([
                                \App\States\SupportRequestCustomerService\SupportReceived::class => 'Talep Alındı',
                                \App\States\SupportRequestCustomerService\SupportProcessed::class => 'İşleme Alındı',
                                \App\States\SupportRequestCustomerService\SupportApproved::class => 'Onaylandı',
                                \App\States\SupportRequestCustomerService\SupportCompleted::class => 'Çözüldü',
                                \App\States\SupportRequestCustomerService\SupportDenied::class => 'Reddedildi',
                                \App\States\SupportRequestCustomerService\SupportCancelled::class => 'İptal Edildi',
                            ]),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['cs_sp_status'],
                                fn(Builder $query, $statu): Builder => $query->where('cs_sp_status', $statu),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['cs_sp_status'] ?? null) {
                            $indicators['cs_sp_status'] = 'MHZ Talep Statüsü ' . get_order_status_text($data['cs_sp_status']) . ' olarak filtrelenmiştir.';
                        }
                        return $indicators;
                    }),
                Tables\Filters\Filter::make('status')
                    ->form([
                        Forms\Components\Select::make('status')
                            ->label('Talep Statüsü')
                            ->options([
                                SupportReceived::class => 'Talep Alındı',
                                SupportProductWaiting::class => 'Ürün Bekleniyor',
                                SupportProductControl::class => 'İnceleme Süreci',
                                SupportProductAtService::class => 'Servis Süreci',
                                SupportCompleted::class => 'Çözüldü',
                            ]),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['status'],
                                fn(Builder $query, $statu): Builder => $query->where('status', $statu),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['status'] ?? null) {
                            $indicators['status'] = 'Talep Statüsü ' . get_order_status_text($data['status']) . ' olarak filtrelenmiştir.';
                        }
                        return $indicators;
                    }),
                Tables\Filters\Filter::make('support_requests_type_id')
                    ->form([
                        Forms\Components\Select::make('status')
                            ->label('Destek Tipi')
                            ->options(SupportRequestType::all()->pluck('name', 'id')->toArray()),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['status'],
                                fn(Builder $query, $statu): Builder => $query->where('support_requests_type_id', $statu),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['status'] ?? null) {
                            $indicators['status'] = 'Destek Tipi ' . SupportRequestType::find($data['status'])->name . ' olarak filtrelenmiştir.';
                        }
                        return $indicators;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
//                Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('id', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSupportRequests::route('/'),
            'create' => Pages\CreateSupportRequest::route('/create'),
            'edit' => Pages\EditSupportRequest::route('/{record}/edit'),
        ];
    }
}
