<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductListResource\Pages;
use App\Filament\Resources\ProductListResource\RelationManagers;
use App\Models\Lunar\Product;
use Filament\Forms;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Database\Eloquent\Builder;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class ProductListResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-desktop-computer';
    protected static ?string $navigationLabel = 'Ürünler';
    protected static ?string $label = 'Ürün';
    protected static ?string $pluralLabel = 'Ürün';
    protected static ?int $navigationSort = 2;
    protected static ?string $slug = 'urunler';
    protected static ?string $navigationGroup = 'Ürün Yönetimi';

    protected static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_archived', false)->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Ürün Özellikleri')
                    ->schema([
                        Repeater::make('productFeatures')
                            ->label('Ürün Özellikleri')
                            ->relationship()
                            ->schema([
                                TextInput::make('key')->label('Başlık')->required(),
                                TextInput::make('value')->label('Değer')->required(),
                            ])
                            ->columns(2)
                            ->orderable(),
                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => 1]),
                Forms\Components\Section::make('SEO')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('Title')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('meta_description')
                            ->label('Meta Description')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('meta_keywords')
                            ->label('Meta Keywords')
                            ->maxLength(255),
                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => 1]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->searchable(isIndividual: true)
                    ->size('sm')
                    ->searchable(isIndividual: true)
                    ->label('ID'),

                Tables\Columns\TextColumn::make('name')
                    ->sortable()
                    ->searchable(isIndividual: true)
                    ->copyable()
                    ->wrap()
                    ->size('sm')
                    ->label('Adı'),

                Tables\Columns\TextColumn::make('sku')
                    ->searchable(isIndividual: true)
                    ->sortable()
                    ->size('sm')
                    ->label('KB SKU'),

                Tables\Columns\TextColumn::make('brand.name')
                    ->searchable()
                    ->sortable()
                    ->size('sm')
                    ->label('Marka'),

                Tables\Columns\TextColumn::make('wp_product_id')
                    ->searchable()
                    ->sortable()
                    ->size('sm')
                    ->label('WP ID'),

                IconColumn::make('is_active')
                    ->size('sm')
                    ->label('Aktif')
                    ->boolean(),

                IconColumn::make('is_archived')
                    ->size('sm')
                    ->label('Arşiv')
                    ->boolean(),

                Tables\Columns\TextColumn::make('is_installment')
                    ->formatStateUsing(fn($state) => $state ? 'Vadeli' : 'Operasyonel')
                    ->size('sm')
                    ->label('Ödeme Tipi'),

                Tables\Columns\TextColumn::make('meta_description')
                    ->wrap()
                    ->searchable()
                    ->sortable()
                    ->size('sm')
                    ->label('Meta Description'),

                Tables\Columns\TextColumn::make('meta_keywords')
                    ->wrap()
                    ->searchable()
                    ->sortable()
                    ->size('sm')
                    ->label('Meta Keywords'),

                Tables\Columns\TextColumn::make('title')
                    ->wrap()
                    ->searchable()
                    ->sortable()
                    ->size('sm')
                    ->label('Title'),

                Tables\Columns\TextColumn::make('defaultUrl.slug')
                    ->formatStateUsing(fn(?string $state): ?string => $state ? '/urun/' . $state : null)
                    ->searchable()
                    ->wrap()
                    ->copyable()
                    ->sortable()
                    ->size('sm')
                    ->label('URL'),

            ])
            ->filters([
                SelectFilter::make('brand')
                    ->label('Marka')
                    ->relationship('brand', 'name')
                    ->searchable(),

                TernaryFilter::make('status')
                    ->label('Aktif mi')
                    ->nullable()
                    ->queries(
                        true: fn(Builder $query) => $query->where('status', 'published'),
                        false: fn(Builder $query) => $query->where('status', 'draft'),
                        blank: fn(Builder $query) => $query,
                    ),

                TernaryFilter::make('in_stock')
                    ->label('Stok Satılabilirlik')
                    ->nullable()
                    ->queries(
                        true: fn(Builder $query) => $query->where('in_stock', true),
                        false: fn(Builder $query) => $query->where('in_stock', false),
                        blank: fn(Builder $query) => $query,
                    ),

                TernaryFilter::make('is_archived')
                    ->label('Arşiv Ürün')
                    ->nullable()
                    ->queries(
                        true: fn(Builder $query) => $query->where('is_archived', true),
                        false: fn(Builder $query) => $query->where('is_archived', false),
                        blank: fn(Builder $query) => $query,
                    ),

                SelectFilter::make('is_installment')
                    ->label('Ödeme Tipi')
                    ->options([
                        '1' => 'Vadeli',
                        '0' => 'Operasyonel',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            isset($data['value']) ? $data['value'] : null,
                            fn(Builder $query, $value): Builder => $query->where('is_installment', (bool)$value)
                        );
                    }),

            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions(
                [ExportBulkAction::make()]
            )
            ->defaultSort('id', 'desc');
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductLists::route('/'),
            'create' => Pages\CreateProductList::route('/create'),
            'edit' => Pages\EditProductList::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([])
            // ->where('is_archived', false)
            ->with(['brand', 'defaultUrl']);
    }
}
