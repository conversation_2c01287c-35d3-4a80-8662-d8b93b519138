<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ScoringRequestResource\Pages;
use App\Filament\Resources\ScoringRequestResource\RelationManagers;
use App\Filament\Resources\ScoringRequestResource\RelationManagers\NotesRelationManager;
use App\Models\ScoringRequest;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use App\States\ScoringRequest\PendingState;
use App\States\ScoringRequest\SentToRedisState;
use App\Models\ScoringSource;
use App\States\ScoringRequest\ScoringRequestState;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class ScoringRequestResource extends Resource
{
    protected static ?string $model = ScoringRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Skorlama Talepleri';
    protected static ?string $label = 'Skorlama Talebi';
    protected static ?string $pluralLabel = 'Skorlama Talepleri';
    protected static ?string $navigationGroup = 'Skorlama';
    protected static ?string $slug = 'scoring-requests';

    protected static function getNavigationBadge(): ?string
    {
        return static::getModel()::whereState('status', [PendingState::class, SentToRedisState::class])->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Skorlama Talebi Bilgileri')
                    ->schema([
                        // Temel Bilgiler
                        Forms\Components\Placeholder::make('ulid')
                            ->label('ULID')
                            ->content(fn($record) => $record?->ulid),

                        Forms\Components\Placeholder::make('status')
                            ->label('Durum')
                            ->content(fn($record) => $record?->status?->description()),

                        Forms\Components\TextInput::make('full_name')
                            ->label('Ad Soyad')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('tckn')
                            ->label('TCKN')
                            ->required()
                            ->maxLength(11),
                        Forms\Components\TextInput::make('email')
                            ->label('E-posta')
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\DatePicker::make('birth_date')
                            ->maxDate(now()->subYears(18))
                            ->default(now()->subYears(18))
                            ->label('Doğum Tarihi')
                            ->required(),

                        // Talep Detayları
                        Forms\Components\TextInput::make('requested_amount')
                            ->label('Talep Edilen Tutar')
                            ->required()
                            ->suffix('₺')
                            ->numeric(),
                        Forms\Components\TextInput::make('requested_duration_months')
                            ->label('Talep Edilen Süre (Ay)')
                            ->required()
                            ->numeric(),
                        // Forms\Components\TextInput::make('additional_data')
                        //     ->label('Ek Veriler'),

                        // Sistem Bilgileri
                        Forms\Components\Placeholder::make('scoring_source_id')
                            ->label('Skorlama Kaynağı')
                            ->content(fn($record) => $record?->scoring_source_with_details ?? '-'),
                        Forms\Components\Placeholder::make('redis_sent_at')
                            ->label('Redis Gönderim Zamanı')
                            ->content(fn($record) => $record?->redis_sent_at?->format('d.m.Y H:i')),

                        // Manuel İşlem Bilgileri
                        Forms\Components\Placeholder::make('manual_processed_by')
                            ->label('Manuel İşlem Yapan')
                            ->visible(fn($record) => $record?->manual_processed_by)
                            ->content(fn($record) => $record?->manual_processed_by),
                        Forms\Components\Placeholder::make('manual_processed_at')
                            ->label('Manuel İşlem Zamanı')
                            ->visible(fn($record) => $record?->manual_processed_at)
                            ->content(fn($record) => $record?->manual_processed_at?->format('d.m.Y H:i')),
                        Forms\Components\Placeholder::make('manual_approved_amount')
                            ->label('Manuel Onaylanan Tutar')
                            ->visible(fn($record) => $record?->manual_approved_amount)
                            ->content(fn($record) => $record?->manual_approved_amount ? number_format($record?->manual_approved_amount, 2, ',', '.') . ' ₺' : '-'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Skorlama Sonuçları')
                    ->schema([
                        Forms\Components\View::make('scoring-results')
                            ->view('filament.resources.scoring-request.scoring-results')
                    ])
                    ->visible(fn($record) => $record && $record->exists && $record->scoringResults()->exists())
                    ->collapsible()
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->fontFamily('mono')
                    ->size('sm')
                    ->searchable(isIndividual: true)
                    ->sortable()
                    ->visible(fn($record) => auth()->user()->hasRole('Super Admin'))
                    ->label('ID'),
                // Temel Bilgiler
                Tables\Columns\TextColumn::make('ulid')
                    ->fontFamily('mono')
                    ->size('sm')
                    ->searchable(isIndividual: true)
                    ->label('ULID'),
                Tables\Columns\TextColumn::make('full_name')
                    ->size('sm')
                    ->searchable(isIndividual: true)
                    ->label('Ad Soyad'),
                Tables\Columns\TextColumn::make('tckn')
                    ->fontFamily('mono')
                    ->size('sm')
                    ->searchable(isIndividual: true)
                    ->label('TCKN'),
                Tables\Columns\TextColumn::make('email')
                    ->size('sm')
                    ->searchable(isIndividual: true)
                    ->label('E-posta'),
                Tables\Columns\TextColumn::make('birth_date')
                    ->size('sm')
                    ->fontFamily('mono')
                    ->label('Doğum Tarihi')
                    ->dateTime('d.m.Y'),

                // Talep Detayları
                Tables\Columns\TextColumn::make('requested_amount')
                    ->formatStateUsing(fn($state) => number_format($state, 2, ',', '.') . ' ₺')
                    ->size('sm')
                    ->fontFamily('mono')
                    ->alignEnd()
                    ->label('Talep Edilen Tutar'),
                Tables\Columns\TextColumn::make('requested_duration_months')
                    ->size('sm')
                    ->alignEnd()
                    ->formatStateUsing(fn($state) => $state . ' Ay')
                    ->label('Talep Edilen Süre (Ay)'),

                // Sistem Bilgileri
                Tables\Columns\TextColumn::make('scoringSource.name')
                    ->size('sm')
                    ->label('Skorlama Kaynağı'),
                Tables\Columns\TextColumn::make('status')
                    ->formatStateUsing(fn($state) => $state->description())
                    ->size('sm')
                    ->label('Durum'),
                Tables\Columns\TextColumn::make('redis_sent_at')
                    ->size('sm')
                    ->label('Redis Gönderim Zamanı')
                    ->dateTime(),

                // Manuel İşlem Bilgileri
                Tables\Columns\TextColumn::make('manual_processed_by')
                    ->label('Manuel İşlem Yapan'),
                Tables\Columns\TextColumn::make('manual_processed_at')
                    ->label('Manuel İşlem Zamanı')
                    ->dateTime(),
                Tables\Columns\TextColumn::make('manual_approved_amount')
                    ->label('Manuel Onaylanan Tutar'),

                // Sistem Zaman Bilgileri
                Tables\Columns\TextColumn::make('created_at')
                    ->size('sm')
                    ->label('Oluşturulma Tarihi')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('scoring_source_id')
                    ->options(ScoringSource::all()->pluck('name', 'id'))
                    ->multiple()
                    ->searchable()
                    ->label('Skorlama Kaynağı'),
                Tables\Filters\SelectFilter::make('status')
                    ->options(
                        ScoringRequestState::all()
                            ->mapWithKeys(fn($state) => [
                                $state::getMorphClass() => (new $state(ScoringRequest::class))->description()
                            ])
                            ->toArray()
                    )
                    ->multiple()
                    ->searchable()
                    ->label('Durum'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                ExportBulkAction::make()
                    ->exports([
                        ExcelExport::make()
                            ->fromTable()
                            ->withFilename(date('Y-m-d') . '-selected-scoring-requests-export')
                    ])
                    ->label('Seçilenleri Excel\'e Aktar')
                    ->icon('heroicon-o-document-download'),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            NotesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListScoringRequests::route('/'),
            'create' => Pages\CreateScoringRequest::route('/create'),
            'edit' => Pages\EditScoringRequest::route('/{record}/edit'),
        ];
    }
}
