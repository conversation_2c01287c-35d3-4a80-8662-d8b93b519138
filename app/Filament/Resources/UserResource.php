<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Models\User;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\BelongsToManyMultiSelect;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Livewire\TemporaryUploadedFile;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use STS\FilamentImpersonate\Impersonate;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Kullanıcılar';
    protected static ?string $label = 'Kullanıcı';
    protected static ?string $pluralLabel = 'Kullanıcılar';
    protected static ?string $navigationGroup = 'Kullanıcı Yönetimi';

    protected static ?string $recordTitleAttribute = 'full_name';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                Forms\Components\Section::make('Kullanıcı Bilgileri')
                    ->schema([
                        Forms\Components\TextInput::make('id')
                            ->maxLength(36)
                            ->visible(fn(?User $record) => $record === null),
                        Forms\Components\TextInput::make('first_name')
                            ->label('Ad')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('last_name')
                            ->label('Soyad')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\DateTimePicker::make('email_verified_at')
                            ->label('E-posta Doğrulama Tarihi'),
                        //                        Forms\Components\TextInput::make('password')
                        //                            ->password()
                        //                            ->required()
                        //                            ->maxLength(255),
                        Forms\Components\TextInput::make('tckn')
                            ->label('T.C. Kimlik No')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('phone')
                            ->label('Telefon')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('findex_phone')
                            ->label('Findeks Kayıtlı Telefonu')
                            ->maxLength(255),
                        Forms\Components\DatePicker::make('date_of_birth')
                            ->label('Doğum Tarihi')
                            ->required(),
                        Forms\Components\Toggle::make('is_company')
                            ->label('Kurumsal Kullanıcı'),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn(?User $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make('Kredi Bilgileri')
                    ->schema([
                        Forms\Components\TextInput::make('findex_credit')
                            ->label('Findeks Kredisi Notu')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('total_limit')
                            ->label('Toplam Kredi Limiti')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('available_limit')
                            ->label('Kullanılabilir Kredi Limiti')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\FileUpload::make('findex_document')
                            ->label('Findeks PDF')
                            ->acceptedFileTypes(['application/pdf'])
                            ->getUploadedFileNameForStorageUsing(function (TemporaryUploadedFile $file): string {
                                return (string)str($file->getClientOriginalName())->prepend('findex-document-');
                            })
                            ->enableOpen()

                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn(?User $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make('Kullanıcı Notları')
                    ->schema([
                        Forms\Components\TextInput::make('user_score')
                            ->label('Kullanıcı Skoru')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(100)
                            ->required(),

                        Forms\Components\Textarea::make('notes')
                            ->label('Müşteri Temsilcisi Notları')
                            ->rows(10)
                            ->cols(20),

                        Forms\Components\Toggle::make('blocked_at')
                            ->inline()
                            ->label('Kullanıcıyı Engelle'),
                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => fn(?User $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make('Yetkiler')
                    ->schema([
                        BelongsToManyMultiSelect::make('roles')->label('Roller')->relationship('roles', 'name'),
                        BelongsToManyMultiSelect::make('permissions')->label('Yetkiler')->relationship('permissions', 'name'),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn(?User $record) => $record === null ? 3 : 2]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('first_name')
                    ->sortable()
                    ->label('Ad')
                    ->searchable(),
                Tables\Columns\TextColumn::make('last_name')
                    ->label('Soyad')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('phone')
                    ->searchable()
                    ->sortable(),

                IconColumn::make('is_company')
                    ->label('Kurumsal')
                    ->alignCenter()
                    ->boolean(),
                //                Tables\Columns\TextColumn::make('email_verified_at')
                //                    ->dateTime(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Oluşturulma Tarihi')
                    ->sortable()
                    ->dateTime(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Güncellenme Tarihi')
                    ->sortable()
                    ->dateTime(),
            ])
            ->prependActions([])
            ->filters([
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Üye Olma Başlangıç Tarihi')
                            ->placeholder(fn($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Üye Olma Tarihi')
                            ->placeholder(fn($state): string => now()->format('M d, Y')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['created_from'] ?? null) {
                            $indicators['created_from'] = 'Şu tarihten itibaren ' . Carbon::parse($data['created_from'])->toFormattedDateString();
                        }
                        if ($data['created_until'] ?? null) {
                            $indicators['created_until'] = 'Şu tarihe kadar ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                        }

                        return $indicators;
                    }),
            ])
            ->actions([
                Impersonate::make('impersonate'),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
            ])
            ->bulkActions([
                //                Tables\Actions\DeleteBulkAction::make(),
                Tables\Actions\RestoreBulkAction::make(),
                //                Tables\Actions\ForceDeleteBulkAction::make(),
                ExportBulkAction::make(),
            ])
            ->defaultSort('id', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\OrdersRelationManager::class,
            RelationManagers\CreditCardsRelationRelationManager::class,
            RelationManagers\MetaRelationManager::class,
            RelationManagers\AddressesRelationManager::class,
            RelationManagers\ScoringResultsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['first_name', 'last_name', 'email', 'phone', 'tckn'];
    }
}
