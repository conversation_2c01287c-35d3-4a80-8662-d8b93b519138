<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OrderResource\Pages;
use App\Filament\Resources\OrderResource\RelationManagers;
use App\Models\Order;
use App\Models\User;
use App\States\Order\Investigation;
use App\States\Order\OrderApproved;
use App\States\Order\OrderCancelled;
use App\States\Order\OrderDenied;
use App\States\Order\OrderDocumentWaiting;
use App\States\Order\OrderEvaluation;
use App\States\Order\OrderProductNotSuitable;
use App\States\Order\OrderReceived;
use App\States\Order\OrderUserCantBeReached;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Livewire\Component;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Siparişler Onboarding';
    protected static ?string $label = 'Sipariş';
    protected static ?string $pluralLabel = 'Siparişler';
    protected static ?string $navigationGroup = 'Sipariş Yönetimi';
    protected static ?int $navigationSort = 1;

    protected static function getNavigationBadge(): ?string
    {
        return static::getModel()::whereIn('status', [OrderReceived::class, OrderDocumentWaiting::class, OrderEvaluation::class])->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Sipariş Bilgileri')
                    ->schema(static::getOrderUserDetails())
                    ->columns(4)
                    ->columnSpan(['lg' => fn(?Order $record) => $record === null ? 3 : 2]),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->size('sm')
                    ->label('ID'),
                Tables\Columns\TextColumn::make('user.full_name')
                    ->formatStateUsing(fn(string $state): string => Str::title($state))
                    ->searchable(['first_name', 'last_name'], isIndividual: true)
                    ->label('Kullanıcı')
                    ->size('sm'),
                Tables\Columns\TextColumn::make('order_number')
                    ->searchable(isIndividual: true)
                    ->fontFamily('mono')
                    ->label('Sipariş Numarası')
                    ->size('sm'),
                //                Tables\Columns\TextColumn::make('payment_method')
                //                    ->label('Ödeme Metodu'),
                Tables\Columns\TextColumn::make('total')
                    ->label('Kira Bedeli')
                    ->formatStateUsing(fn(string $state): string => number_format($state, 2, ',', '.') . ' ₺')
                    ->alignEnd()
                    ->fontFamily('mono')
                    ->size('sm'),

                Tables\Columns\TextColumn::make('status_text')
                    ->label('Statü')
                    ->size('sm'),

                //                Tables\Columns\TextColumn::make('ip_address')
                //                    ->label('IP'),
                //                Tables\Columns\TextColumn::make('coupon_id')
                //                    ->label('Kupon'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Oluşturulma Tarihi')
                    ->dateTime()
                    ->alignEnd()
                    ->size('sm'),
                //                Tables\Columns\TextColumn::make('updated_at')
                //                    ->label('Güncellenme Tarihi')
                //                    ->dateTime(),
            ])
            ->filters([
                // Tables\Filters\TrashedFilter::make(),
                SelectFilter::make('status')
                    ->label('Sipariş Statüsü')
                    ->options([
                        OrderReceived::class => 'Yeni',
                        OrderEvaluation::class => 'Değerlendiriliyor',
                        OrderDocumentWaiting::class => 'Belge Bekleniyor',
                        OrderUserCantBeReached::class => 'Ulaşılamadı',
                        Investigation::class => 'Şüpheli İşlem',
                        OrderProductNotSuitable::class => 'Ürün Tedarik Edilemedi',
                    ]),


                Filter::make('created_at')
                    ->form([
                        DatePicker::make('created_from'),
                        DatePicker::make('created_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                //                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                //Tables\Actions\DeleteBulkAction::make(),
                Tables\Actions\RestoreBulkAction::make(),
                //Tables\Actions\ForceDeleteBulkAction::make(),
            ])
            ->defaultSort('id', 'desc');
    }

    public static function getOrderUserDetails(): array
    {
        return [
            Forms\Components\Select::make('user_id')
                ->label('Müşteri Adı')
                ->relationship('user', 'first_name')
                ->getOptionLabelFromRecordUsing(fn(User $record) => "{$record->first_name} {$record->last_name}")
                ->required()
                ->searchable()
                ->suffixAction(
                    fn(?string $state): Action => Action::make('visit')
                        ->icon('heroicon-s-external-link')
                        ->url(
                            "/admin/users/{$state}",
                            shouldOpenInNewTab: true,
                        )
                ),

            Forms\Components\TextInput::make('order_number')
                ->label('Sipariş Numarası')
                ->required()
                ->disabled()
                ->maxLength(255)
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateOrder)),

            Forms\Components\Placeholder::make('created_at')
                ->label('Sipaariş Tarihi')
                ->content(fn(?Order $record): ?string => $record?->created_at->timezone('Europe/Istanbul')->format('d.m.Y H:i:s'))
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateOrder)),

            Forms\Components\Select::make('status')
                ->label('Sipariş Durumu')
                // ->disabled()
                ->options([
                    OrderReceived::class => 'Yeni',
                    OrderEvaluation::class => 'Değerlendiriliyor',
                    OrderUserCantBeReached::class => 'Ulaşılamadı',
                    //                    OrderDocumentWaiting::class => 'Belge Bekleniyor',
                    //                    OrderStudentDocumentWaiting::class => 'Öğrenci Belgesi Bekleniyor',
                    Investigation::class => 'Şüpheli İşlem',
                    OrderApproved::class => 'Onaylandı',
                    //                    OrderShipped::class => 'Kargolandı',
                    OrderDenied::class => 'Reddedildi',
                    OrderCancelled::class => 'İptal',
                    //                    OrderRefunded::class => 'İade',
                    OrderProductNotSuitable::class => 'Ürün Tedarik Edilemedi',
                ])
                ->required()
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateOrder)),

            Forms\Components\Placeholder::make('email')
                ->content(fn(?Order $record): ?string => $record?->user->email)
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateOrder)),

            Forms\Components\Placeholder::make('phone')
                ->label('Telefon')
                ->content(fn(?Order $record): ?string => $record?->user->phone)
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateOrder)),

            Forms\Components\Placeholder::make('birt_date')
                ->label('Doğum Tarihi')
                ->content(fn(?Order $record): ?string => $record?->user->date_of_birth->format('d.m.Y'))
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateOrder)),

            Forms\Components\Placeholder::make('tckn')
                ->label('TC Kimlik No')
                ->content(fn(?Order $record): ?string => $record?->user->tckn)
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateOrder)),

            Forms\Components\Placeholder::make('address')
                ->label('Kargo Adresi')
                ->content(fn(?Order $record): ?string => json_decode($record?->shipping_address)->address . ' ' . $record?->user->address->county . ' / ' . $record?->user->address->city)
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateOrder)),

            Forms\Components\Placeholder::make('address')
                ->label('Fatura Adresi')
                ->content(fn(?Order $record): ?string => json_decode($record?->shipping_address)->address . ' ' . $record?->user->address->county . ' / ' . $record?->user->address->city)
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateOrder)),

            Forms\Components\TextInput::make('total')
                ->label('Kira Bedeli')
                ->required()
                ->minValue(1)
                ->maxValue(100000),
            //->visible(fn (Component $livewire): bool => ! ($livewire instanceof Pages\CreateOrder) )

            Forms\Components\Placeholder::make('cc')
                ->label('Kredi Kartı')
                ->content(fn(?Order $record): ?string => $record->paymentTransaction?->creditCard?->bin_number . '****' . $record->paymentTransaction?->creditCard?->number . ' / ' . $record->paymentTransaction?->creditCard?->holder),

            Forms\Components\Placeholder::make('coupon_id')
                ->label('Kupon Kodu')
                ->content(fn(?Order $record): ?string => $record?->coupon->code)
                ->visible(fn(?Order $record): ?bool => $record->coupon_id),

            Forms\Components\Placeholder::make('user_score')
                ->label('Kullanıcı Skorlama Bilgileri')
                ->content(function (?Order $record) {
                    if (!$record || !$record->user || !$record->user->scoringResults) {
                        return 'Skorlama bilgisi bulunamadı';
                    }

                    $html = '<div class="space-y-1">';
                    $score = $record->user->scoringResults->map(function ($item) use (&$html) {
                        if (isset($item->scoring_service_score)) {
                            $html .= '<div><strong>' . ucfirst($item->service_name) . '</strong>: ' . $item->scoring_service_score . '</div>';
                        }
                    });

                    $html .= '</div>';

                    return new \Illuminate\Support\HtmlString($html);
                })
                ->visible(function (?Order $record) {
                    return $record && $record->user && $record->user->scoringResults->count() > 0;
                }),
        ];
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\OrderItemsRelationManager::class,
            RelationManagers\OrderTransactionRelationManager::class,
            RelationManagers\UserRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            'create' => Pages\CreateOrder::route('/create'),
            'view' => Pages\ViewOrder::route('/{record}'),
            'edit' => Pages\EditOrder::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                //                SoftDeletingScope::class,
            ])
            ->whereIn('status', [OrderReceived::class, OrderDocumentWaiting::class, OrderEvaluation::class, OrderProductNotSuitable::class]);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['order_number'];
    }
}
