<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PaymentTransactionResource\Pages;
use App\Filament\Resources\PaymentTransactionResource\RelationManagers;
use App\Models\PaymentTransaction;
use App\Models\Transaction;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class PaymentTransactionResource extends Resource
{
    protected static ?string $model = Transaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'IYZICO Çekim <PERSON>leri';
    protected static ?string $label = 'IYZICO Çekim Hareketleri';
    protected static ?string $navigationGroup = 'Tahsilat Yönetimi';
    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //                Forms\Components\TextInput::make('billable_id')
                //                    ->required()
                //                    ->maxLength(36),
                //                Forms\Components\TextInput::make('credit_card_id')
                //                    ->required()
                //                    ->maxLength(36),
                //                Forms\Components\TextInput::make('subscription_id')
                //                    ->required()
                //                    ->maxLength(36),
                //                Forms\Components\TextInput::make('amount')
                //                    ->required()
                //                    ->maxLength(36),
                //                Forms\Components\TextInput::make('currency')
                //                    ->required()
                //                    ->maxLength(36),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->size('sm')
                    ->label('ID'),
                Tables\Columns\TextColumn::make('billable.full_name')
                    ->formatStateUsing(fn(string $state): string => Str::title($state))
                    ->fontFamily('mono')
                    ->size('sm')
                    ->label('Kullanıcı'),
                Tables\Columns\TextColumn::make('amount')
                    ->label('Ödeme Tutarı')
                    ->formatStateUsing(fn(string $state): string => number_format($state, 2, ',', '.') . ' ₺')
                    ->fontFamily('mono')
                    ->size('sm')
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('order_no')
                    ->label('Sipariş No')
                    ->size('sm')
                    ->getStateUsing(function (Transaction $record): null|string {
                        return $record->orderNo;
                    })
                    ->fontFamily('mono')
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('voided_at')
                    ->label('İade Tarihi')
                    ->dateTime()
                    ->size('sm'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->size('sm'),
            ])
            ->actions([])
            ->bulkActions([])
            ->defaultSort('id', 'desc');
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPaymentTransactions::route('/'),
            'create' => Pages\CreatePaymentTransaction::route('/create'),
            //            'view' => Pages\ViewPaymentTransaction::route('/{record}'),
            //            'edit' => Pages\EditPaymentTransaction::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
