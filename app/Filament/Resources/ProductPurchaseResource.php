<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductPurchaseResource\Pages;
use App\Filament\Resources\ProductPurchaseResource\RelationManagers;
use App\Models\Inventory;
use App\Models\OrderItems;
use App\Models\OrderTransaction;
use App\Models\ProductPurchase;
use App\Models\ProductStock;
use App\States\Order\OrderApproved;
use Closure;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class ProductPurchaseResource extends Resource
{
    protected static ?string $model = OrderItems::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Kargolama';
    protected static ?string $label = 'Sipariş';
    protected static ?string $pluralLabel = 'Kargolanacak Ürünler Listesi';
    protected static ?string $navigationGroup = 'Sipariş Yönetimi';
    protected static bool $shouldRegisterNavigation = false;


    protected static ?int $navigationSort = 3;

    protected static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('invoice_number')
                    ->label('İrsaliye Numarası')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //                Tables\Columns\TextColumn::make('id')->label('Sipariş No'),
                Tables\Columns\ImageColumn::make('product.product.getFirstPhoto')
                    ->height(120)
                    ->label('Ürün Resmi'),

                Tables\Columns\TextColumn::make('product.product.getName')->label('Ürün Adı')->weight('medium')
                    ->description(fn(OrderItems $record): string => $record->product->product->getExcerptText),

                Tables\Columns\TextColumn::make('product.getVaariant')->label('Varyant'),
                Tables\Columns\TextColumn::make('order.order_number')->label('Sipariş No'),
                Tables\Columns\TextColumn::make('order.created_at')->label('Sipariş Tarihi')->dateTime(),
                Tables\Columns\TextColumn::make('order.finance_approved_at')->label('Finans Onay Tarihi')->dateTime(),
                Tables\Columns\TextColumn::make('order.user.full_name')->label('Müşteri Adı'),
                Tables\Columns\TextColumn::make('plan')->label('Kiralama Süresi')
                    ->getStateUsing(function (OrderItems $record): null|string {
                        return $record->planObj->name;
                    })
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('order.total')
                    ->label('Kira Tutarı')
                    ->alignEnd()
                    ->formatStateUsing(fn(string $state): string => $state . ' ₺'),

                Tables\Columns\TextColumn::make('quantity')->label('Adet'),
                Tables\Columns\TextColumn::make('product_stock_id'),

                //                Tables\Columns\ToggleColumn::make('is_purchased')
                //                    ->label('Satın Alındı Mı?')
                //                    ->sortable()
                //                    ->toggleable(),

                IconColumn::make('is_exist_on_kb_inventory')
                    ->label('Ürün KB Depoda var mı?')
                    ->boolean(),

                IconColumn::make('is_purchased')
                    ->label('Satın Alındı Mı?')
                    ->boolean(),

                IconColumn::make('is_supplied')
                    ->label('KB Depoya Ulaştı Mı?')
                    ->boolean()

            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([

                //                Action::make('Satın alındı mı?')
                //                    ->color('danger')
                ////                    ->disabled(fn(OrderTransaction $record) => $record->payment_status_id == 1)
                //                    ->mountUsing(fn(Forms\ComponentContainer $form, OrderItems $record) => $form->fill([
                //                        'is_purchased' => (int)$record->is_purchased,
                //                    ]))
                //                    ->action(fn(OrderItems $record, array $data) => $record->saveIsPurchased($data['is_purchased'], $data['purchase_price'] ?? null))
                //                    ->form([
                //                            Forms\Components\Toggle::make('is_purchased')
                //                                ->label('Ürünün satın alındığı olarak işlenecek, emin misiniz?')
                //                                ->reactive()
                //                                ->inline(),
                //                        ]
                //                    ),
                //                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->label(fn(OrderItems $record): ?string => $record->cargo_at == null ? 'Sevk Bilgilerini Gir' : 'Sevk Bilgilerini Güncelle')
                    ->using(function (OrderItems $record, array $data): OrderItems {
                        $data['cargo_at'] = now();
                        $record->update($data);

                        // Depo güncelleme işlemi burada yapılacak
                        $user = $record->order->user;
                        $inventory = Inventory::firstOrCreate([
                            'user_id' => $user->id,
                            'is_user_inventory' => 1,
                        ], [
                            'name' => $user->full_name . ' ' . $user->tckn . ' Deposu',
                            'slug' => Str::of($user->full_name . ' ' . $user->tckn . ' Deposu')->slug(),
                        ]);

                        $productStock = ProductStock::find($record->product_stock_id)
                            ->update([
                                'inventory_id' => $inventory->id,
                                'entried_at' => now(),
                            ]);

                        return $record;
                    }),
            ])
            ->bulkActions([
                //                Tables\Actions\DeleteBulkAction::make(),
                //                Tables\Actions\ForceDeleteBulkAction::make(),
                //                Tables\Actions\RestoreBulkAction::make(),
            ])
            ->defaultSort('finance_approved_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductPurchases::route('/'),
            //            'create' => Pages\CreateProductPurchase::route('/create'),
            //            'view' => Pages\ViewProductPurchase::route('/{record}'),
            //            'edit' => Pages\EditProductPurchase::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ])
            ->leftJoin('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereIn('orders.status', [
                OrderApproved::class,
            ])
            ->where('order_items.is_user_suitable_control', 1)
            ->where('order_items.is_supplied', 1)
            ->select('order_items.*')
            ->orderBy('order_items.id', 'desc');
    }

    public static function getRecordRouteKeyName(): string
    {
        return 'order_items.id';
    }
}
