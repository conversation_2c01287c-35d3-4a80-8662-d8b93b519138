<?php

namespace App\Filament\Resources\AllOrdersTeknosaResource\RelationManagers;

use App\Models\Lunar\SubscriptionMonths;
use App\Models\Order\OrderItem;
use App\States\Order\OrderRenting;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;

class OrderItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'orderItems';
    protected static ?string $recordTitleAttribute = 'id';

    protected static ?string $navigationLabel = 'Sipariş Ürünleri';
    protected static ?string $label = 'Sipariş Ürünleri';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('product.id')
                    ->label('Ürün Varyant ID'),
// Bu spatie media kullanılır ise bu şekilde kullanılır.
//                Tables\Columns\ImageColumn::make('product.product.firstCoverImage')
//                    ->width(180)
//                    ->label('Ürün Resmi'),

                Tables\Columns\ImageColumn::make('product.product.getFirstPhoto')
                    ->height(180)
                    ->label('Ürün Resmi'),

                Tables\Columns\TextColumn::make('product.product.getName')->label('Ürün Adı'),
                Tables\Columns\TextColumn::make('quantity')->label('Adet'),
                // textcolumn content add ₺ to end of price
                Tables\Columns\TextColumn::make('total')->label('Tutar')
                    ->formatStateUsing(fn(string $state): string => $state . ' ₺'),
                //Tables\Columns\TextColumn::make('discount_amount')->label('İndirim Tutarı'),
                Tables\Columns\TextColumn::make('plan')->label('Kiralama Süresi')
                    ->formatStateUsing(fn(string $state): string => SubscriptionMonths::getSelectedMonth($state) . ' Ay'),

                IconColumn::make('is_user_suitable_control')
                    ->label('Tedarik Edebilir?')
                    ->boolean(),

                IconColumn::make('is_purchased')
                    ->label('Satın Alındı Mı?')
                    ->boolean(),

                Tables\Columns\TextColumn::make('cargo_at')->label('Kargolama Tarihi'),
                Tables\Columns\TextColumn::make('cargo_receiver_name')->label('Teslim Alan'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                //Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Action::make('back')
                    ->label('SÖZLEŞMEYİ KISMİ SONLANDIR')
                    ->color('danger')
                    ->action(fn(OrderItem $record, array $data) => $record->processOrderAsCancelled($data['purchase_price'], $data['missing_piece_fee']))
                    ->visible(function (OrderItem $record) {
                        return $record->order->status == OrderRenting::class &&
                            $record->payment_control == true &&
                            $record->findex_control == true &&
                            $record->is_user_suitable_control == true &&
                            $record->cancelled_at == null;
                    })->form([
//                            Forms\Components\TextInput::make('purchase_price')
//                                ->numeric()
//                                ->mask(fn(Forms\Components\TextInput\Mask $mask) => $mask
//                                    ->numeric()
//                                    ->decimalPlaces(2) // Set the number of digits after the decimal point.
//                                    ->decimalSeparator(',') // Add a separator for decimal numbers.
//                                    ->mapToDecimalSeparator([',']) // Map additional characters to the decimal separator.
//                                    ->minValue(0) // Set the minimum value that the number can be.
//                                    ->normalizeZeros() // Append or remove zeros at the end of the number.
//                                    ->padFractionalZeros() // Pad zeros at the end of the number to always maintain the maximum number of decimal places.
//                                    ->thousandsSeparator('.') // Add a separator for thousands.
//                                )
//                                ->required()
//                                ->label('İptal Ceza Bedeli'),
//
//                            Forms\Components\TextInput::make('missing_piece_fee')
//                                ->numeric()
//                                ->mask(fn(Forms\Components\TextInput\Mask $mask) => $mask
//                                    ->numeric()
//                                    ->decimalPlaces(2) // Set the number of digits after the decimal point.
//                                    ->decimalSeparator(',') // Add a separator for decimal numbers.
//                                    ->mapToDecimalSeparator([',']) // Map additional characters to the decimal separator.
//                                    ->minValue(0) // Set the minimum value that the number can be.
//                                    ->normalizeZeros() // Append or remove zeros at the end of the number.
//                                    ->padFractionalZeros() // Pad zeros at the end of the number to always maintain the maximum number of decimal places.
//                                    ->thousandsSeparator('.') // Add a separator for thousands.
//                                )
//                                ->label('Eksik Parça/Arıza Bedeli'),
                        ]
                    )
                    ->requiresConfirmation(),
//                Tables\Actions\EditAction::make(),
                //Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                //Tables\Actions\DeleteBulkAction::make(),
            ]);
    }
}
