<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BrandResource\Pages;
use App\Filament\Resources\BrandResource\RelationManagers;
use App\Models\Brand;
use Filament\Forms;
use Filament\Forms\Components\RichEditor;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;

class BrandResource extends Resource
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'Markalar';
    protected static ?string $label = 'Markalar';
    protected static ?string $navigationGroup = 'Ürün Yönetimi';

    protected static ?int $navigationSort = 1;

    protected static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    protected static ?string $model = Brand::class;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Marka Bilgileri')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Marka Adı')
                            ->hintIcon('heroicon-o-document-text')
                            ->helperText('Marka adını eksiksiz yazınız'),
    
                        Forms\Components\TextInput::make('title')
                            ->label('Title')
                            ->hintIcon('heroicon-o-document-text')
                            ->helperText('Title tag in header'),

                        Forms\Components\TextInput::make('meta_description')
                            ->label('Meta Description')
                            ->hintIcon('heroicon-o-document-text')
                            ->helperText('Meta description tag in header'),

                        Forms\Components\TextInput::make('meta_keywords')
                            ->label('Meta Keywords')
                            ->hintIcon('heroicon-o-document-text')
                            ->helperText('Meta keywords tag in header'),

                        RichEditor::make('sub_category_note')
                            ->label('Alt Marka Notu')
                            ->enableToolbarButtons($buttons = ['h1'])
                            ->columnSpan(2), // Enable toolbar buttons. See below for options.
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),

                Tables\Columns\TextColumn::make('name')
                    ->label('Marka Adı'),
            ])
            ->filters([
                //
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBrands::route('/'),
            'create' => Pages\CreateBrand::route('/create'),
            'edit' => Pages\EditBrand::route('/{record}/edit'),
        ];
    }
}
