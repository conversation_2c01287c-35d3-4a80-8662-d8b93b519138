<?php

namespace App\Filament\Resources\AllOrdersResource\Widgets;

use App\Models\Product\ProductStock;
use Filament\Tables;
use Filament\Widgets\TableWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class OrderInventory extends TableWidget
{
    protected static string $view = 'filament.resources.all-orders-resource.widgets.order-inventory';

    public ?Model $record = null;

    protected string|int|array $columnSpan = 2; // 2 tam ekran yapar

    protected static ?string $heading = null;

    protected function getTableQuery(): Builder
    {
        return ProductStock::whereIn('order_id', $this->record->orderItems->pluck('id'))->with('inventory')->orderBy('product_id', 'desc');
        //            ->where('user_id', $this->record->user_id)
        //            ->orderBy('created_at', 'desc');
        //        ->whereRelation('operationalNote', 'order_id', $this->record->id)
        //            ->orderBy('created_at', 'desc')
        //            ->orderBy('id', 'desc');
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('product_id')
                ->size('sm')
                ->label('Ürün Varyant ID'),
            // Tables\Columns\ImageColumn::make('product.product.getFirstPhoto')
            //     ->alignCenter()
            //     ->height(120)
            //     ->width(120)
            //     ->label('Ürün Resmi'),
            Tables\Columns\TextColumn::make('product.product.name')
                ->wrap()
                ->size('sm')
                ->label('Ürün Adı'),
            Tables\Columns\TextColumn::make('product.getVaariant')
                ->size('sm')
                ->label('Varyant'),
            Tables\Columns\TextColumn::make('sn')
                ->searchable(isIndividual: true)
                ->size('sm')
                ->label('SN'),
            Tables\Columns\TextColumn::make('inventory_id')
                ->size('sm')
                ->label('Depo ID'),
            Tables\Columns\TextColumn::make('inventory.name')
                ->size('sm')
                ->label('Depo'),
        ];
    }

    protected function getTableHeading(): ?string
    {
        $count = $this->getTableQuery()->count();
        return "Siparişte Olup Kullanıcı Deposunda Olan Ürünler ({$count} adet)";
    }
}
