<?php

namespace App\Filament\Resources\AllOrdersResource\RelationManagers;

use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Collection;
use App\Models\OrderTransaction;
use App\Services\Parasut\Parasut;

class OrderTransactionRelationManager extends RelationManager
{
    protected static string $relationship = 'orderTransaction';
    protected static ?string $label = 'Ödeme Planı';
    protected static ?string $recordTitleAttribute = 'id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //                Forms\Components\TextInput::make('id')
                //                    ->required()
                //                    ->maxLength(255),

                Forms\Components\DatePicker::make('due_date')
                    ->label('Vade Tarihi')
                    ->required(),

                Forms\Components\Select::make('payment_status_id')
                    ->label('Ödendi mi?')
                    ->options([
                        2 => 'Ödenmedi',
                        1 => 'Ödendi',
                        4 => 'İade Edildi',
                        6 => 'Sigorta / Ödenmedi',
                        7 => 'Sigorta / Ödendi',
                    ])
                    ->required(),

                Forms\Components\TextInput::make('amount')
                    ->numeric()
                    ->mask(
                        fn(Forms\Components\TextInput\Mask $mask) => $mask
                            ->numeric()
                            ->decimalPlaces(2) // Set the number of digits after the decimal point.
                            //->decimalSeparator(',') // Add a separator for decimal numbers.
                            ->mapToDecimalSeparator([',']) // Map additional characters to the decimal separator.
                            ->minValue(0) // Set the minimum value that the number can be.
                            ->normalizeZeros() // Append or remove zeros at the end of the number.
                            ->padFractionalZeros() // Pad zeros at the end of the number to always maintain the maximum number of decimal places.
                        //->thousandsSeparator('.') // Add a separator for thousands.
                    )
                    ->required()
                    ->label('Kiralama Bedeli'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //                Tables\Columns\TextColumn::make('uuid')
                //                    ->label('ID'),
                Tables\Columns\TextColumn::make('due_date')
                    ->label('Vade Tarihi')
                    ->dateTime(),
                Tables\Columns\TextColumn::make('paymentStatus.name')
                    ->label('Ödeme Durumu'),
                Tables\Columns\TextColumn::make('last_payment_check')
                    ->label('Son Ödeme Kontrol Zamanı')
                    ->dateTime(),
                Tables\Columns\TextColumn::make('amount')
                    ->alignRight()
                    ->formatStateUsing(fn(string $state): string => $state . ' ₺')
                    ->label('Tutar'),
                Tables\Columns\ViewColumn::make('invoice')
                    ->view('filament.tables.columns.invoice-icon')
                    ->label('Fatura')
                    ->alignCenter(),
                Tables\Columns\ViewColumn::make('payment')
                    ->view('filament.tables.columns.payment-icon')
                    ->label('Ödeme')
                    ->alignCenter(),
            ])
            ->filters([])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data, RelationManager $livewire): array {
                        $data['order_id'] = $livewire->ownerRecord->order_id_clean;
                        $data['card_id'] = $livewire->ownerRecord->card_id ?? 1;

                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->visible(in_array(auth()->id(), [14513, 22470])),
                // Send payment to parasut
                Tables\Actions\Action::make('sendPaymentToParasut')
                    ->label('Parasut\'a Ödeme Gönder')
                    ->icon('heroicon-o-currency-dollar')
                    ->visible(function (OrderTransaction $record) {
                        return in_array(auth()->id(), [1]) && $record->payment_status_id == 1 && !$record->meta()->where('key', 'parasut_payment_sent')->exists();
                    })
                    ->action(fn(OrderTransaction $record) => $record->sendPaymentToParasut())
                    ->requiresConfirmation(),
                // Create renting invoice
                Tables\Actions\Action::make('createRentingInvoice')
                    ->label('Fatura Oluştur')
                    ->icon('heroicon-o-currency-dollar') // TODO: change icon to invoice icon 
                    ->visible(function (OrderTransaction $record) {
                        return self::canCreateInvoice() && !$record->meta()->where('key', 'parasut_renting_invoice_id')->exists();
                    })
                    ->action(function (OrderTransaction $record) {
                        $parasut = new Parasut();
                        $parasut->createRentingInvoice($record);
                    })
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('Ödeme Planı Fiyat Güncelle')
                    ->color('danger')
                    ->icon('heroicon-o-currency-dollar')
                    ->action(fn(Collection $records, array $data) => $records->where('payment_status_id', 2)->each->update(['amount' => $data['price']]))
                    ->form(
                        [
                            Forms\Components\TextInput::make('price')
                                ->label('Ödeme Planı Yeni Tutarı')
                                ->numeric()
                                ->mask(
                                    fn(Forms\Components\TextInput\Mask $mask) => $mask
                                        ->numeric()
                                        ->decimalPlaces(2)
                                        ->decimalSeparator('.')
                                )
                                ->required()
                                ->minValue(1),
                        ]
                    )
                    ->requiresConfirmation()
                    ->modalSubheading('Sadece ödenmemiş ödeme planlarına fiyat güncellemesi yapılacaktır. Güncelleme işlemi geri alınamaz. Devam etmek istediğinize emin misiniz?')
                    ->deselectRecordsAfterCompletion(),
            ])->defaultSort('due_date', 'asc');
    }

    public static function canCreateInvoice(): bool
    {
        return auth()->user() && auth()->user()->hasRole('Muhasebe');
    }
}
