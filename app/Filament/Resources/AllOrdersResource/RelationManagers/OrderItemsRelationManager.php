<?php

namespace App\Filament\Resources\AllOrdersResource\RelationManagers;

use App\Models\B2BProduct;
use App\Models\Lunar\SubscriptionMonths;
use App\Models\Order\OrderItem;
use App\Models\SupportRequest;
use App\Services\Order\CreateInventoryService;
use App\Services\Order\ImportProductStockService;
use App\Services\ReturnProduct\ReturnProductService;
use App\States\Order\OrderAtLegalPursuit;
use App\States\Order\OrderRenting;
use App\States\SupportRequest\SupportProductWaiting;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\Position;
use Filament\Tables\Columns\IconColumn;
use Illuminate\Support\Str;

class OrderItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'orderItems';
    protected static ?string $recordTitleAttribute = 'id';
    protected static ?string $navigationLabel = 'Sipariş Ürünleri';
    protected static ?string $label = 'Sipariş Ürünleri';

    protected $listeners = [
        'createInventoryForCompany' => 'handleCreateInventoryForCompany',
        'createInventoryForUser' => 'handleCreateInventoryForUser'
    ];

    //    protected static function getNavigationBadge(): ?string
    //    {
    //        return 'sdfs';
    //    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')->label('Or.It.ID')->visible(fn() => auth()->user()->hasRole('Super Admin')),
                Tables\Columns\TextColumn::make('product.id')
                    ->label('Ürün Varyant ID'),
                //                Bu spatie media kullanılır ise bu şekilde kullanılır.
                //                Tables\Columns\ImageColumn::make('product.product.firstCoverImage')
                //                    ->width(180)
                //                    ->label('Ürün Resmi'),

                Tables\Columns\ImageColumn::make('product.product.getFirstPhoto')
                    ->height(fn(OrderItem $record) => $record->product_type == B2BProduct::class ? 44 : 120)
                    ->width(fn(OrderItem $record) => $record->product_type == B2BProduct::class ? 120 : 120)
                    ->alignCenter()
                    ->label('Ürün Resmi'),

                Tables\Columns\TextColumn::make('product.product.nameWithSource')->label('Ürün Adı'),
                Tables\Columns\TextColumn::make('product.getVaariant')->label('Varyant'),
                Tables\Columns\TextColumn::make('quantity')->label('Adet'),
                // textcolumn content add ₺ to end of price
                Tables\Columns\TextColumn::make('price')->label('Tutar')
                    ->formatStateUsing(fn(string $state): string => $state . ' ₺'),

                Tables\Columns\TextColumn::make('plan')->label('Kiralama Süresi')
                    ->formatStateUsing(fn(string $state): string => SubscriptionMonths::getSelectedMonth($state) . ' Ay'),

                IconColumn::make('has_insurance')
                    ->label('Sigorta')
                    ->boolean(),

                Tables\Columns\TextColumn::make('insurance_price')->label('Sigorta Tutarı')
                    ->formatStateUsing(fn(string $state): string => $state . ' ₺'),

                Tables\Columns\TextColumn::make('lastestLegalContractAmount')
                    ->label('Son Sözleşme Tutar')
                    ->getStateUsing(function (OrderItem $record): null|string {
                        return $record->lastestLegalContract()->amount ?? '-' . ' ₺';
                    })
                    ->alignRight(),

                Tables\Columns\TextColumn::make('legal_contract_expired_at')
                    ->label('Sözleşme Bitiş Tarihi')
                    ->date()
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('cancelled_at')
                    ->label('Sözleşme Sonlandırma Tarihi')
                    ->date()
                    ->alignEnd(),

                IconColumn::make('is_user_suitable_control')
                    ->label('Tedarik Edebilir?')
                    ->boolean(),

                IconColumn::make('is_purchased')
                    ->label('Satın Alındı Mı?')
                    ->boolean(),

                Tables\Columns\TextColumn::make('cargo_at')
                    ->label('Kargolama Tarihi')
                    ->dateTime(),
                //                    ->visible(function (OrderItem $record): bool {
                //                        return $record->cargo_at != null;
                //                    }),
                Tables\Columns\TextColumn::make('tracking_url')
                    ->url(fn(OrderItem $record): string => $record->tracking_url ?? '', true)
                    //->hidden(fn(OrderItem $record) => $record?->cargo_at == null)
                    ->label('Kargo Takip Linki'),

                Tables\Columns\TextColumn::make('delivered_at')
                    ->label('Teslim Tarihi')
                    ->dateTime(),
                //                    ->visible(fn(?OrderItem $record) => $record?->cargo_at != null),
                Tables\Columns\TextColumn::make('cargo_receiver_name')
                    ->label('Teslim Alan'),
                //                    ->visible(fn(?OrderItem $record) => $record?->cargo_at != null),
            ])
            ->filters([])
            ->headerActions([
                //Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                // GetTableActions ile devam edeceğinden dolayı bu actionu kaldırdım.
            ])
            ->bulkActions([
                //Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public function getTableActions(): array
    {

        // Sözleşme Uzat
        // Birim fiyat güncelle // Ödeme Planını etkileyebilir, bu konuda uyarı!!!
        // Bu ürün için gönderilen ürün SN değiştir
        // Kısmi Ödeme Sonlandırma talebi

        // Ürünü kopyala !! Bunu sadece onboarding için kullanacağız. Bu ekranda olmayacak !!

        if ($this->ownerRecord->user->is_company) {
            $addMonthsOptions = range(1, 24);
        } else {
            $addMonthsOptions = range(1, 18);
        }

        return [
            Tables\Actions\ActionGroup::make([

                Action::make('Ürün Güncelle')
                    ->label(fn(OrderItem $record): ?string => 'Ürün Güncelle')
                    ->color('danger')
                    ->icon('heroicon-o-cash')
                    ->mountUsing(fn(Forms\ComponentContainer $form, OrderItem $record) => $form->fill([
                        'price' => $record->price,
                        'quantity' => $record->quantity,
                    ]))
                    ->action(function (OrderItem $record, array $data): OrderItem {
                        return $record->updateAmountAndUnitPrice($data);
                    })
                    ->form(
                        [
                            Forms\Components\TextInput::make('price')
                                ->label('Ürün Birim Fiyatı')
                                ->numeric()
                                ->mask(
                                    fn(Forms\Components\TextInput\Mask $mask) => $mask
                                        ->numeric()
                                        ->decimalPlaces(2)
                                        ->decimalSeparator('.')
                                )
                                ->required()
                                ->minValue(1),

                            Forms\Components\TextInput::make('quantity')
                                ->label('Ürün Adedi')
                                ->numeric()
                                ->mask(
                                    fn(Forms\Components\TextInput\Mask $mask) => $mask
                                        ->numeric()
                                        ->decimalPlaces(0)
                                        ->decimalSeparator('.')
                                )
                                ->required()
                                ->minValue(1),
                        ]
                    )
                    ->requiresConfirmation(),

                Action::make('details')
                    //->label(fn(OrderItem $record): ?string => $record->product->product->name . ' Sözleşme Detayları')
                    ->label(fn(OrderItem $record): ?string => ' Sözleşme Detayları')
                    ->action(fn() => false)
                    ->visible(function (OrderItem $record) {
                        return $record->is_user_suitable_control == 1;
                    })
                    ->modalContent(fn(OrderItem $record) => view('filament.resources.event.actions.details')->with([
                        'record' => $record
                    ])),

                Action::make('Sözleşme Uzat')
                    ->label(fn(OrderItem $record): ?string => 'Sözleşme Uzat')
                    ->color('danger')
                    ->icon('heroicon-o-clock')
                    ->visible(function (OrderItem $record) {
                        return $record->is_user_suitable_control == 1 && $record->cancelled_at == null;
                    })
                    ->mountUsing(fn(Forms\ComponentContainer $form, OrderItem $record) => $form->fill([
                        'current_duration' => $record->planObj->value,
                        'amount' => $record->lastestLegalContract()->amount ?? $record->price,
                    ]))
                    ->action(function (OrderItem $record, array $data): OrderItem {
                        $record->addNewLegalContract($data);
                        return $record; // Return the record to be updated.
                    })
                    ->form(
                        [
                            Forms\Components\Select::make('duration')
                                //->options(fn(Closure $get) => range(1, (18 - $get('current_duration'))))
                                ->options($addMonthsOptions)
                                ->label('Kaç Ay Eklenecek')
                                ->required(),

                            Forms\Components\TextInput::make('amount')
                                ->label('Yeni Sözleşme Tutarı')
                                ->numeric()
                                ->mask(
                                    fn(Forms\Components\TextInput\Mask $mask) => $mask
                                        ->numeric()
                                        ->decimalPlaces(2)
                                        ->decimalSeparator('.')
                                )
                                ->required()
                                ->minValue(1),

                            Forms\Components\Textarea::make('moderator_comment')
                                ->label('Not'),
                        ]
                    )
                    ->requiresConfirmation(),

                Action::make('Sozlesme Sonlandir')
                    ->label('Kısmi Sözleşme Sonlandır')
                    ->visible(function (OrderItem $record) {
                        return $record->is_user_suitable_control == 1 && $record->cancelled_at == null;
                    })
                    ->color('danger')
                    ->action(fn(array $data, OrderItem $record) => $record->processOrderAsCancelled(cancellation_fee: data_get($data, 'purchase_price', 0), missing_piece_fee: data_get($data, 'missing_piece_fee', 0), amount_to_be_terminated: data_get($data, 'amount_to_be_terminated', 1)))
                    //                    ->disabled(function (OrderItem $record) {
                    //                        return $record->status != OrderRenting::class;
                    //                    })
                    ->form(
                        [

                            Forms\Components\TextInput::make('amount_to_be_terminated')
                                ->numeric()
                                ->visible(fn(OrderItem $record) => $record->quantity > 1)
                                ->mask(
                                    fn(Forms\Components\TextInput\Mask $mask) => $mask
                                        ->numeric()
                                        //                                    ->maxValue($this->ownerRecord->quantity)
                                        ->minValue(0) // Set the minimum value that the number can be.
                                )
                                ->required()
                                ->label('Sonlandırılan Adet'),

                            //                            Forms\Components\TextInput::make('purchase_price')
                            //                                ->numeric()
                            //                                ->mask(fn(Forms\Components\TextInput\Mask $mask) => $mask
                            //                                    ->numeric()
                            //                                    ->decimalPlaces(2) // Set the number of digits after the decimal point.
                            //                                    ->decimalSeparator(',') // Add a separator for decimal numbers.
                            //                                    ->mapToDecimalSeparator([',']) // Map additional characters to the decimal separator.
                            //                                    ->minValue(0) // Set the minimum value that the number can be.
                            //                                    ->normalizeZeros() // Append or remove zeros at the end of the number.
                            //                                    ->padFractionalZeros() // Pad zeros at the end of the number to always maintain the maximum number of decimal places.
                            //                                    ->thousandsSeparator('.') // Add a separator for thousands.
                            //                                )
                            //                                ->required()
                            //                                ->label('İptal Ceza Bedeli'),
                            //
                            //                            Forms\Components\TextInput::make('missing_piece_fee')
                            //                                ->numeric()
                            //                                ->mask(fn(Forms\Components\TextInput\Mask $mask) => $mask
                            //                                    ->numeric()
                            //                                    ->decimalPlaces(2) // Set the number of digits after the decimal point.
                            //                                    ->decimalSeparator(',') // Add a separator for decimal numbers.
                            //                                    ->mapToDecimalSeparator([',']) // Map additional characters to the decimal separator.
                            //                                    ->minValue(0) // Set the minimum value that the number can be.
                            //                                    ->normalizeZeros() // Append or remove zeros at the end of the number.
                            //                                    ->padFractionalZeros() // Pad zeros at the end of the number to always maintain the maximum number of decimal places.
                            //                                    ->thousandsSeparator('.') // Add a separator for thousands.
                            //                                )
                            //                                ->label('Eksik Parça/Arıza Bedeli'),
                        ]
                    )
                    ->requiresConfirmation()
                    ->modalSubheading(now()->format('d-m-Y') . ' tarihinden sonraki ilk ödeme planı itibari ile sözleşme sonlandırılacaktır.'),

                Action::make('Sozlesme Sonlandirma Tarihi Güncelle')
                    ->label('Sozlesme Sonlandirma Tarihi Güncelle')
                    ->visible(function (OrderItem $record) {
                        return auth()->id() == 1;
                    })
                    ->mountUsing(fn(Forms\ComponentContainer $form, OrderItem $record) => $form->fill([
                        'legal_contract_expired_at' => Carbon::parse($record->order->orderTransactions->max('due_date'))->addMonths(1),
                    ]))
                    ->color('danger')
                    ->action(fn(array $data, OrderItem $record) => $record->update(['legal_contract_expired_at' => $data['legal_contract_expired_at'], 'contract_expired_at' => $data['legal_contract_expired_at']]))
                    ->form(
                        [
                            Forms\Components\DatePicker::make('legal_contract_expired_at')
                                ->label('Sozlesme Sonlandirma Tarihi'),
                        ]
                    )
                    ->requiresConfirmation(),

                Action::make('Operasyon Ürün Fotoğraf / Not Ekle')
                    ->label('Operasyon Ürün Fotoğraf / Not Ekle')
                    ->color('primary')
                    ->icon('heroicon-o-camera')
                    //                    ->mountUsing(fn(Forms\ComponentContainer $form, OrderItem $record) => $form->fill([
                    //                        'priice' => $record->price,
                    //                    ]))
                    ->action(function (OrderItem $record, array $data) {
                        return $record->saveOperationalNotes($data);
                    })
                    ->form(
                        [
                            Forms\Components\Textarea::make('note')
                                ->label('Not'),
                            Forms\Components\FileUpload::make('photos')
                                ->label('Kontrol Fotoğrafları')
                                ->acceptedFileTypes(['image/*', 'video/*'])
                                ->disablePreview()
                                ->disk('s3')
                                ->directory(Str::of(config('app.app_short_name'))->append('-order-service-photos/', $this->ownerRecord->order_number)->__toString())
                                ->multiple(),
                        ]
                    )
                    ->requiresConfirmation(),

                Action::make('İadeye Gelen Ürün Ulaştı')
                    ->label('İadeye Gelen Ürün Ulaştı')
                    ->icon('heroicon-o-chat-alt-2')
                    ->visible(function (OrderItem $record) {
                        return in_array($record->order->status, [OrderRenting::class, OrderAtLegalPursuit::class]);
                    })
                    //                    ->mountUsing(fn(Forms\ComponentContainer $form, OrderItem $record) => $form->fill([
                    //                        'legal_contract_expired_at' => Carbon::parse($record->order->orderTransactions->max('due_date'))->addMonths(1),
                    //                    ]))
                    ->color('danger')
                    ->action(function (array $data, OrderItem $record) {

                        $prs = new ReturnProductService();
                        $prs->setDeliveredAt($data['delivered_at']);
                        $sr = SupportRequest::where('status', SupportProductWaiting::class)->where('product_id', $record->product_id)->first();
                        if ($sr) {
                            $prs->setSupportRequest($sr);
                        }
                        $prs->setOrder($record->order);
                        $prs->setProductVariant($record->product);
                        $prs->handle();

                        //                        // if support request exists, save the returned product infos
                        //                        $sr = SupportRequest::where('status', SupportProductWaiting::class)->where('product_id', $record->product_id)->first();
                        //                        $sr?->saveReturnedProductInfos($data['delivered_at']);
                        //
                        //                        // Send notification to the customer
                        //                        $record->sendProductReturnSmsAndEmail();
                        return $record;
                    })
                    ->form(
                        [
                            Forms\Components\DateTimePicker::make('delivered_at')
                                ->label('Teslim Alma Tarihi')
                                ->default(now())
                                ->required(),
                        ]
                    )
                    ->requiresConfirmation()
                    ->modalSubheading('Ürünlerin ulaştığına dair müşteriye SMS ve Email gönderilecek'),

                Action::make('Servise Gönderildi')
                    ->label('Servise Gönderildi')
                    ->icon('heroicon-o-chat-alt-2')
                    ->visible(function (OrderItem $record) {
                        return $record->order->status == OrderRenting::class;
                    })
                    ->color('danger')
                    ->action(fn(array $data, OrderItem $record) => $record->sendProductSentTechnicalServiceSmsAndEmail())
                    ->form([])
                    ->requiresConfirmation()
                    ->modalSubheading('Ürünlerin servise gönderildiğine dair müşteriye SMS ve Email gönderilecek'),

                Action::make('Seri No Yükle')
                    ->label('Seri No Yükle')
                    ->icon('heroicon-o-upload')
                    ->color('success')
                    ->visible(function (OrderItem $record) {
                        return $record->is_user_suitable_control == 1;
                    })
                    ->action(function (OrderItem $record, array $data) {
                        return static::handleProductStockImport($record, $data);
                    })
                    ->form([
                        Forms\Components\FileUpload::make('excel_file')
                            ->label('Excel Dosyası')
                            ->acceptedFileTypes([
                                'application/vnd.ms-excel',
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                            ])
                            ->maxSize(5 * 1024) // 5 MB
                            ->required()
                            ->storeFiles(false)
                            ->helperText('İlk kolon: SN/IMEI, İkinci kolon: Alım Maliyeti, Üçüncü kolon: Fatura No (opsiyonel)'),

                        Forms\Components\Toggle::make('has_header')
                            ->label('İlk satır başlık içeriyor')
                            ->default(true)
                            ->helperText('Excel dosyanızın ilk satırında başlık varsa işaretleyin'),
                    ])
                    ->modalHeading('Seri No Yükle')
                    ->modalSubheading(fn(OrderItem $record) => "Maksimum yüklenebilecek: " . ($record->quantity - \App\Models\ProductStock::where('order_id', $record->id)->count()) . " adet"),

                Action::make('SN Ata')
                    ->label('SN Ata')
                    ->icon('heroicon-o-tag')
                    ->color('warning')
                    ->visible(function (OrderItem $record) {
                        return $record->is_user_suitable_control == 1;
                    })
                    ->action(function (OrderItem $record, array $data) {
                        return static::handleAssignSerialNumber($record, $data);
                    })
                    ->form([
                        Forms\Components\TextInput::make('sn')
                            ->label('Seri Numarası')
                            ->required()
                            ->helperText('Atanacak seri numarasını girin'),
                    ])
                    ->modalHeading('Seri No Ata')
                    ->modalSubheading('Var olan bir seri numarasını bu order item\'a atayın'),

            ]),
        ];
    }

    protected function getTableActionsPosition(): ?string
    {
        return Position::BeforeCells;
    }

    protected static function sendInventoryNotFoundNotification(string $userName, string $eventName, array $eventData = []): void
    {
        \Filament\Notifications\Notification::make()
            ->title('Depo Bulunamadı')
            ->body("'{$userName}' için depo bulunamadı. Devam etmek için depo oluşturmanız gerekmektedir.")
            ->warning()
            ->persistent()
            ->actions([
                \Filament\Notifications\Actions\Action::make('create')
                    ->label('Depo Oluştur')
                    ->button()
                    ->emit($eventName, $eventData)
                    ->close(),
                \Filament\Notifications\Actions\Action::make('cancel')
                    ->label('İptal')
                    ->color('gray')
                    ->close(),
            ])
            ->send();
    }

    protected static function handleProductStockImport(OrderItem $record, array $data): void
    {
        try {
            $uploadedFile = $data['excel_file'];
            $hasHeader = $data['has_header'] ?? true;

            if (!$uploadedFile) {
                throw new \Exception('Excel dosyası bulunamadı.');
            }

            // Handle Livewire TemporaryUploadedFile
            if ($uploadedFile instanceof \Livewire\TemporaryUploadedFile) {
                $tempPath = $uploadedFile->getRealPath();
            } else {
                throw new \Exception('Beklenmeyen dosya tipi.');
            }

            if (!$tempPath || !file_exists($tempPath)) {
                throw new \Exception('Excel dosyası okunamadı.');
            }

            // Excel dosyasını oku
            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($tempPath);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            // Header varsa ilk satırı atla
            if ($hasHeader && count($rows) > 0) {
                array_shift($rows);
            }

            // Veriyi hazırla
            $importData = collect($rows)->map(function ($row) {
                return [
                    'sn' => $row[0] ?? null,
                    'purchase_price' => $row[1],
                    'invoice_number' => $row[2] ?? null, // 3. kolon: Fatura No (opsiyonel)
                ];
            })->filter(function ($row) {
                return !empty($row['sn']);
            });

            // Import servisini kullan
            $service = new ImportProductStockService($record);
            $result = $service->setData($importData)->import();

            if ($result['success']) {
                \Filament\Notifications\Notification::make()
                    ->title('Başarılı')
                    ->body("{$result['successCount']} adet seri no başarıyla yüklendi.")
                    ->success()
                    ->send();

                if (!empty($result['duplicates'])) {
                    \Filament\Notifications\Notification::make()
                        ->title('Duplicate SN Tespit Edildi')
                        ->body(count($result['duplicates']) . ' adet duplicate SN tespit edildi ve ilgili kişilere email gönderildi.')
                        ->warning()
                        ->persistent()
                        ->send();
                }
            } else {
                // Check for specific inventory errors
                $errorMessage = implode("\n", $result['errors']);

                if (str_contains($errorMessage, 'INVENTORY_NOT_FOUND_COMPANY')) {
                    $order = $record->order;
                    self::sendInventoryNotFoundNotification(
                        $order->user->full_name_db,
                        'createInventoryForCompany',
                        ['userId' => $order->user_id]
                    );
                } else if (str_contains($errorMessage, 'INVENTORY_NOT_FOUND_USER')) {
                    // Bireysel kullanıcı için de sipariş sahibinin ID'sini kullan
                    $order = $record->order;
                    self::sendInventoryNotFoundNotification(
                        $order->user->full_name_db,
                        'createInventoryForUser',
                        ['userId' => $order->user_id]
                    );
                } else {
                    \Filament\Notifications\Notification::make()
                        ->title('İşlem Başarısız')
                        ->body($errorMessage)
                        ->danger()
                        ->persistent()
                        ->send();
                }
            }
        } catch (\Exception $e) {
            \Filament\Notifications\Notification::make()
                ->title('Excel İşleme Hatası')
                ->body($e->getMessage() . ' ' . $e->getFile() . ' ' . $e->getLine())
                ->danger()
                ->persistent()
                ->send();
        }
    }

    protected static function handleAssignSerialNumber(OrderItem $record, array $data): void
    {
        try {
            $sn = $data['sn'];
            if (empty($sn)) {
                throw new \Exception('Seri numarası boş olamaz.');
            }

            // Find the ProductStock by SN
            $productStock = \App\Models\ProductStock::where('sn', $sn)->first();

            if (!$productStock) {
                \Filament\Notifications\Notification::make()
                    ->title('Seri No Bulunamadı')
                    ->body("'{$sn}' seri numarası product_stocks tablosunda bulunamadı.")
                    ->danger()
                    ->persistent()
                    ->send();
                return;
            }

            // Find the user's inventory
            $userInventory = \App\Models\Inventory::where('user_id', $record->order->user_id)
                // ->where('is_user_inventory', true)
                ->first();

            if (!$userInventory) {
                \Filament\Notifications\Notification::make()
                    ->title('Kullanıcı Deposu Bulunamadı')
                    ->body("Sipariş sahibi kullanıcının deposu bulunamadı.")
                    ->danger()
                    ->persistent()
                    ->send();
                return;
            }

            // Update the ProductStock with order_id and inventory_id
            $productStock->update([
                'order_id' => $record->id,
                'inventory_id' => $userInventory->id,
            ]);

            \Filament\Notifications\Notification::make()
                ->title('Başarılı')
                ->body("Seri numarası '{$sn}' başarıyla order item ID {$record->id} ve inventory ID {$userInventory->id} ile güncellendi.")
                ->success()
                ->send();
        } catch (\Exception $e) {
            \Filament\Notifications\Notification::make()
                ->title('Hata')
                ->body('Hata: ' . $e->getMessage())
                ->danger()
                ->persistent()
                ->send();
        }
    }

    public function handleCreateInventoryForCompany($data)
    {
        try {
            $userId = is_array($data) ? $data['userId'] : $data;
            $user = \App\Models\User::find($userId);
            if (!$user) {
                throw new \Exception('Kullanıcı bulunamadı.');
            }

            CreateInventoryService::createForUser($user);

            \Filament\Notifications\Notification::make()
                ->title('Depo Oluşturuldu')
                ->body('Kurumsal kullanıcı için depo başarıyla oluşturuldu. Lütfen Excel dosyasını tekrar yükleyin.')
                ->success()
                ->send();
        } catch (\Exception $e) {
            \Filament\Notifications\Notification::make()
                ->title('Depo Oluşturulamadı')
                ->body('Hata: ' . $e->getMessage() . ' ' . $e->getFile() . ' ' . $e->getLine())
                ->danger()
                ->send();
        }
    }

    public function handleCreateInventoryForUser($data)
    {
        try {
            $userId = is_array($data) ? $data['userId'] : $data;
            $user = \App\Models\User::find($userId);
            if (!$user) {
                throw new \Exception('Kullanıcı bulunamadı.');
            }

            CreateInventoryService::createForUser($user);

            \Filament\Notifications\Notification::make()
                ->title('Depo Oluşturuldu')
                ->body('Kullanıcı deposu başarıyla oluşturuldu. Lütfen Excel dosyasını tekrar yükleyin.')
                ->success()
                ->send();
        } catch (\Exception $e) {
            \Filament\Notifications\Notification::make()
                ->title('Depo Oluşturulamadı')
                ->body('Hata: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
