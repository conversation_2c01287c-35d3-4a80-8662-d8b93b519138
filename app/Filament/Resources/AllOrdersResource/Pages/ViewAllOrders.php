<?php

namespace App\Filament\Resources\AllOrdersResource\Pages;

use App\Filament\Resources\AllOrdersResource;
use App\Services\ReturnProduct\ReturnProductService;
use App\States\Order\OrderAtLegalPursuit;
use App\States\Order\OrderRenting;
use App\States\SupportRequest\SupportProductWaiting;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Pages\Actions;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ViewRecord;
use App\Filament\Resources\AllOrdersResource\Pages\Actions\UpdateOrderDateAction;

class ViewAllOrders extends ViewRecord
{
    protected static string $resource = AllOrdersResource::class;

    protected function getActions(): array
    {
        return [

            UpdateOrderDateAction::make("Sipariş Tarihi Düzenle")->record($this->record),

            Action::make('iadeKargo')
                ->label('İade Kargo')
                ->color('danger')
                ->action(function (array $data) {

                    $productVariant = $this->record->orderItems()->where('id', $data['order_item_id'])->first()->product;
                    $rps = new ReturnProductService;
                    $rps->setOrder(\App\Models\Order\Order::find($this->record->id));
                    $rps->setProductVariant($productVariant);
                    $cargo_return_code = $this->record->sendReturnCargoCode($productVariant);
                    $rps->openSupportRequest(
                        supportRequestType: 7,
                        note: 'MHZ İade Kargo Talebi',
                        status: SupportProductWaiting::class,
                        cargo_return_code: $cargo_return_code,
                    );

                    Notification::make()
                        ->title('İade Kargo Kodu Müşteriye Gönderildi.')
                        ->success()
                        ->send();
                })
                ->visible(function () {
                    return ($this->record->status == OrderRenting::class || $this->record->status == OrderAtLegalPursuit::class);
                })
                ->form([
                    Forms\Components\Select::make('order_item_id')
                        ->label('İade hangi ürün için yapılacak?')
                        ->options($this->record->orderItems->mapWithKeys(fn($item) => [$item->id => $item->product?->name]))
                        ->required(),
                ])
                ->requiresConfirmation(),

            Action::make('Satın Alma')
                ->label('Satın Alma')
                ->color('secondary')
                ->action(fn(array $data) => false)
                ->form(
                    [
                        Forms\Components\TextInput::make('psf')
                            ->numeric()
                            ->default(0)
                            ->mask(
                                fn(Forms\Components\TextInput\Mask $mask) => $mask
                                    ->numeric()
                                    ->decimalPlaces(2) // Set the number of digits after the decimal point.
                                    ->decimalSeparator(',') // Add a separator for decimal numbers.
                                    ->mapToDecimalSeparator([',']) // Map additional characters to the decimal separator.
                                    ->minValue(0) // Set the minimum value that the number can be.
                                    ->normalizeZeros() // Append or remove zeros at the end of the number.
                                    ->padFractionalZeros() // Pad zeros at the end of the number to always maintain the maximum number of decimal places.
                                    ->thousandsSeparator('.') // Add a separator for thousands.
                            )
                            ->reactive()
                            ->required()
                            ->label('PSF'),

                        Forms\Components\Placeholder::make('total_rent_pay')
                            ->label('Toplam Kiralama Ödemesi')
                            ->content(fn() => $this->record->total_rent_pay),

                        Forms\Components\Placeholder::make('total_purchase_pay')
                            ->label('Kalan Satın Alma Ödemesi')
                            ->content(fn(callable $get) => (float)$get('psf') - $this->record->total_rent_pay * .25),

                    ]
                )
                ->requiresConfirmation()
                ->modalSubheading('Satış Fiyat Hesaplama')
                ->modalButton('Hesapla')
                ->visible(fn() => $this->record->status == OrderRenting::class),

            Action::make('EOR')
                ->label('Kiralama Sonu Maili')
                ->color('secondary')
                ->visible(fn() => !$this->record->user->is_company)
                ->action(fn(array $data) => $this->record->sendLegalAgrementComesToEndEmail())
                ->requiresConfirmation()
                ->modalSubheading('Kiralama Sonu Maili Gönderilecek. Onaylıyor musunuz?')
                ->modalButton('Evet, Gönder')
                ->visible(fn() => $this->record->status == OrderRenting::class),

            Action::make('ÖBF')
                ->label('ÖBF')
                ->color('primary')
                ->visible(fn() => !$this->record->user->is_company)
                ->url(route('order.get-obf', $this->record->id), true),

            Action::make('MKS')
                ->label('MKS')
                ->color('primary')
                ->visible(fn() => !$this->record->user->is_company)
                ->url(route('order.get-mks', $this->record->id), true),

            Action::make('sozlesmeSonlandir')
                ->label('SÖZLEŞME SONLANDIR')
                ->color('danger')
                ->action(fn(array $data) => $this->record->processOrderAsCancelled($data['purchase_price'], $data['missing_piece_fee']))
                ->after(fn() => redirect()->route('filament.resources.all-orders.view', ['record' => $this->record->id]))
                ->disabled(function () {
                    return $this->record->status != OrderRenting::class;
                })->form(
                    [
                        Forms\Components\TextInput::make('purchase_price')
                            ->numeric()
                            ->mask(
                                fn(Forms\Components\TextInput\Mask $mask) => $mask
                                    ->numeric()
                                    ->decimalPlaces(2) // Set the number of digits after the decimal point.
                                    ->decimalSeparator(',') // Add a separator for decimal numbers.
                                    ->mapToDecimalSeparator([',']) // Map additional characters to the decimal separator.
                                    ->minValue(0) // Set the minimum value that the number can be.
                                    ->normalizeZeros() // Append or remove zeros at the end of the number.
                                    ->padFractionalZeros() // Pad zeros at the end of the number to always maintain the maximum number of decimal places.
                                    ->thousandsSeparator('.') // Add a separator for thousands.
                            )
                            ->required()
                            ->label('İptal Ceza Bedeli'),

                        Forms\Components\TextInput::make('missing_piece_fee')
                            ->numeric()
                            ->mask(
                                fn(Forms\Components\TextInput\Mask $mask) => $mask
                                    ->numeric()
                                    ->decimalPlaces(2) // Set the number of digits after the decimal point.
                                    ->decimalSeparator(',') // Add a separator for decimal numbers.
                                    ->mapToDecimalSeparator([',']) // Map additional characters to the decimal separator.
                                    ->minValue(0) // Set the minimum value that the number can be.
                                    ->normalizeZeros() // Append or remove zeros at the end of the number.
                                    ->padFractionalZeros() // Pad zeros at the end of the number to always maintain the maximum number of decimal places.
                                    ->thousandsSeparator('.') // Add a separator for thousands.
                            )
                            ->label('Eksik Parça/Arıza Bedeli'),
                    ]
                )
                ->requiresConfirmation(),

            //            Action::make('Tedarik Edebilir Kontrolü')
            //                ->color('danger')
            ////                    ->disabled(fn(OrderTransaction $record) => $record->payment_status_id == 1)
            //                ->mountUsing(fn(Forms\ComponentContainer $form, OrderItem $record) => $form->fill([
            //                    'is_user_suitable_control' => (int)$record->is_user_suitable_control,
            //                ]))
            //                ->action(fn(OrderItem $record, array $data) => $record->saveIsUserSuitableControl($data['is_user_suitable_control']))
            //                ->form([
            ////                        Forms\Components\Toggle::make('is_user_suitable_control')
            ////                            ->label('Sipariş onayı verilmesine emin misiniz?')
            ////                            ->inline(),
            //                    ]
            //                ),
            Actions\EditAction::make(),
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            AllOrdersResource\Widgets\OperationalNotes::class,
            AllOrdersResource\Widgets\Notes::class,
            AllOrdersResource\Widgets\OrderInventory::class,
        ];
    }
}
