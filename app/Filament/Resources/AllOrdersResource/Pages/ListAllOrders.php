<?php

namespace App\Filament\Resources\AllOrdersResource\Pages;

use App\Filament\Resources\AllOrdersResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;
use pxlrbt\FilamentExcel\Actions\Pages\ExportAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;

class ListAllOrders extends ListRecords
{
    protected static string $resource = AllOrdersResource::class;

    protected function getActions(): array
    {
        return [
            ExportAction::make()
                ->visible(fn(): bool => auth()->id() == 1)
                ->exports([
                    ExcelExport::make()
                        ->fromTable()
                        ->queue()
                        ->withFilename(date('Y-m-d') . '-all-orders-export')
                ]),
        ];
    }
}
