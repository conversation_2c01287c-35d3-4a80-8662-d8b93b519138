<?php

namespace App\Filament\Resources\AllOrdersResource\Pages;

use App\Filament\Resources\AllOrdersResource;
use App\Models\UserAddress;
use App\States\Order\OrderApproved;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Filament\Pages\Actions\Action;

class EditAllOrders extends EditRecord
{
    protected static string $resource = AllOrdersResource::class;

    protected function getActions(): array
    {
        return [
            Action::make('change-status-cancelled')
                ->label('İptal Et')
                ->visible(fn() => $this->record->status == OrderApproved::class && auth()->user()->hasAnyPermission(['Cancel Order From All Orders']))
                ->action(fn() => $this->record->updateStatusToCancelled($this->record))
                ->after(fn() => $this->redirect(route('filament.resources.all-orders.view', $this->record->id)))
                ->color('warning')
                ->requiresConfirmation()
                ->modalSubheading('Bu siparişi iptal etmek istediğinizden emin misiniz?'),

            Actions\ViewAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        //        if ($record->status != $data['status']) {
        //            try {
        //                $record->status->transitionTo($data['status']);
        //            } catch (TransitionNotFound $e) {
        //                Notification::make()
        //                    ->title('Sipariş durumu bu şekilde değiştirilemez.')
        //                    ->danger()
        //                    ->send();
        //
        //                $this->halt();
        //
        //                return $record->fresh();
        //            }
        //        }

        $data['billing_address'] = json_encode(UserAddress::find($data['billing_address_id'])->toArray());
        $data['shipping_address'] = json_encode(UserAddress::find($data['shipping_address_id'])->toArray());

        $record->update($data);

        return $record;
    }
}
