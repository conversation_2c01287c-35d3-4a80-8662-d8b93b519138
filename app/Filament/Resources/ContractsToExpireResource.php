<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContractsToExpireResource\Pages;
use App\Filament\Resources\ContractsToExpireResource\RelationManagers;
use App\Models\Order\OrderItem;
use App\States\Order\OrderApproved;
use App\States\Order\OrderRenting;
use App\States\Order\OrderShipped;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;

class ContractsToExpireResource extends Resource
{
    protected static ?string $model = OrderItem::class;
    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Sözleşmesi Uzatılacaklar';
    protected static ?string $label = 'Sözleşmesi Uzatılacaklar';
    protected static ?string $pluralLabel = 'Sözleşmesi Uzatılacaklar';
    protected static ?string $navigationGroup = 'Tahsilat Yönetimi';
    protected static ?string $slug = 'contracts-to-expire-1-3-6-12-months';
    protected static ?int $navigationSort = 7;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
//                Forms\Components\Section::make('Sözleşmesi Uzatılacaklar')
//                    ->schema([
//                        Forms\Components\TextInput::make('name')
//                            ->maxValue(50)
//                            ->required(),
//
//                        Forms\Components\TextInput::make('email')
//                            ->label('Email address')
//                            ->required()
//                            ->email()
//                            ->unique(ignoreRecord: true),
//
//                        Forms\Components\TextInput::make('phone')
//                            ->maxValue(50),
//
//                        Forms\Components\DatePicker::make('birthday')
//                            ->maxDate('today'),
//                    ])
//                    ->columns(2)
//                    ->columnSpan(['lg' => fn(?OrderItem $record) => $record === null ? 1 : 1]),
//
                Forms\Components\Section::make('Sipariş Bilgileri')
                    ->schema([
                        Forms\Components\Placeholder::make('Onaylanmış ve Sözleşmesi Sonlanmamış Sipariş Ürünleri')
                            ->label('Onaylanmış ve Sözleşmesi Sonlanmamış Sipariş Ürünleri')
                            ->content(fn(OrderItem $record): ?string => $record->order->orderItems->where('is_user_suitable_control', true)->whereNull('cancelled_at')->map(fn($item) => $item->product?->product?->name . ' (' . $item->total . ' ₺)' . ' ' . $item->planObj->name)->implode(',')),

                        Forms\Components\Placeholder::make('Ödeme Planı Güncelenecek Ürün')
                            ->label('Ödeme Planı Güncelenecek Ürün')
                            ->content(fn(OrderItem $record): ?string => $record->product->product->name . ' ' . $record->total . ' ₺' . ' - ' . $record->planObj->name),

                        Forms\Components\Placeholder::make('Ödeme Planı Güncelenecek Ürün Kiralama Süresi')
                            ->label('Ödeme Planı Güncelenecek Ürün Kiralama Süresi')
                            ->content(fn(OrderItem $record): ?string => $record->planObj->name),

                        Forms\Components\Placeholder::make('orders.order_number')
                            ->label('Sipariş NO')
                            ->content(fn(OrderItem $record): ?string => $record->order->order_number),

                        Forms\Components\Placeholder::make('legal_contract_expired_at')
                            ->label('Sözleşme Bitiş Tarihi')
                            ->content(fn(OrderItem $record): ?string => $record->legal_contract_expired_at),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn(?OrderItem $record) => $record === null),
            ])
            ->columns(2);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order.id')
                    ->searchable(isIndividual: true)
                    ->sortable()
                    ->label('ID'),

                Tables\Columns\TextColumn::make('order.last_due_date')
                    ->label('Son Ödeme Planı Tarihi')
                    ->dateTime()
                    ->sortable()
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('contract_expired_at')
                    ->label('Sözleşme Bitiş Tarihi')
                    ->dateTime()
                    ->sortable()
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('legal_contract_expired_at')
                    ->label('Yasal Sözleşme Bitiş Tarihi')
                    ->dateTime()
                    ->sortable()
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('order.created_at')
                    ->label('Sipariş Tarihi')
                    ->sortable()
                    ->dateTime()
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('order.user.full_name')
                    ->searchable(['first_name', 'last_name'], isIndividual: true)
                    ->label('Kullanıcı'),
                Tables\Columns\TextColumn::make('order.order_number')
                    ->searchable(isIndividual: true)
                    ->label('Sipariş Numarası'),
//                Tables\Columns\TextColumn::make('payment_method')
//                    ->label('Ödeme Metodu'),
                Tables\Columns\TextColumn::make('order.total')
                    ->label('Kira Bedeli')
                    ->formatStateUsing(fn(string $state): string => $state . ' ₺')
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('product.product.getName')
//                    ->searchable(query: function (Builder $query, string $search): Builder {
//                        return $query
//                            ->with('product.product')
//                            ->where('last_name', 'like', "%{$search}%");
//                    })
                    ->label('Ürün Adı')
                    ->html(),

                Tables\Columns\TextColumn::make('planObj.name')
                    ->alignEnd()
                    ->label('Kiralama Süresi')
                    ->html(),

                Tables\Columns\TextColumn::make('order2.statusText')
                    ->label('Statü'),


            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    // All Relations for this Resource must be defined here. Even if they are not used on some pages.
    public static function getRelations(): array
    {
        return [
            RelationManagers\OrderTransactionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContractsToExpires::route('/'),
            'create' => Pages\CreateContractsToExpire::route('/create'),
            'edit' => Pages\EditContractsToExpire::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([])
            ->whereBetween('order_items.contract_expired_at', [now()->subYears(10), now()->addMonth()])
//            ->where('order_items.is_extended_to_full_contract', false)
            ->where('order_items.is_user_suitable_control', true)
            ->whereNull('cancelled_at')
            //->where('order_items.plan', '<>', 5)
            //->whereHas('orders.status', [OrderRenting::class, OrderShipped::class, OrderApproved::class]);
            ->whereHas('order', function ($query) {
                return $query->whereIn('status', [OrderRenting::class, OrderShipped::class, OrderApproved::class]);
            })
            ->whereHas('order.user', function ($query) {
                return $query->where('is_company', false);
            });
    }
}
