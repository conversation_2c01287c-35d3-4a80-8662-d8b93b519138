<?php

namespace App\Filament\Resources\ProductListResource\Pages;

use App\Filament\Resources\ProductListResource;
use App\Helpers\ProductHelper;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditProductList extends EditRecord
{
    protected static string $resource = ProductListResource::class;

    protected function getActions(): array
    {
        return [];
    }

    protected function afterSave(): void
    {
        try {
            // Field type değerlerini düzelt
            ProductHelper::fixAttributeDataFieldType();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Field type düzeltme hatası')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
