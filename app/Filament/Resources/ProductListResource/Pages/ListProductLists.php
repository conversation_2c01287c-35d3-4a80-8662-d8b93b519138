<?php

namespace App\Filament\Resources\ProductListResource\Pages;

use App\Filament\Resources\ProductListResource;
use App\Models\Lunar\ProductVariant;
use App\Models\ProductNonCast;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;
use Konnco\FilamentImport\Actions\ImportAction;
use Konnco\FilamentImport\Actions\ImportField;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use pxlrbt\FilamentExcel\Actions\Pages\ExportAction;

class ListProductLists extends ListRecords
{
    protected static string $resource = ProductListResource::class;

    protected function getActions(): array
    {
        return [
            ExportAction::make()
                ->exports([
                    ExcelExport::make()
                        ->fromTable()
                        ->queue()
                        ->withFilename(date('Y-m-d') . '-products-export')
                ]),

            ImportAction::make()
                ->fields([
                    ImportField::make('id')
                        ->label('ID')
                        ->helperText('Kiralabunu V2 Ürün <PERSON>üncelleme'),

                    ImportField::make('meta_description')
                        ->label('SEO Meta Description')
                        ->helperText('Google Meta Description'),

                    ImportField::make('title')
                        ->label('SEO Meta Title'),

                    //                    ImportField::make('excerpt')
                    //                        ->label('Ürün Kısa Açıklama'),

                    //                    ImportField::make('teknosa_id')
                    //                        ->label('Teknosa Oracle ID'),


                ])
                //                ->mutateBeforeCreate(function ($row) {
                //                    //$row['price'] = (int)Str::of($row['price'])->remove(' ₺')->__toString() * 100;
                //
                //                    return $row;
                //                })
                ->handleRecordCreation(function ($data) {

                    // excel üzrinden veri id si varyant ama burada ürün id si üzerinden iş yapıyoruz
                    $p = ProductNonCast::find($data['id']);

                    //                    ray('teknosa_id null değil', $data['id']);
                    //                    $p = ProductVariant::find($data['id']);
                    //                    $p->update([
                    //                        'teknosa_id' => $data['teknosa_id'],
                    //                    ]);

                    $p->update([
                        'meta_description' => $data['meta_description'],
                        'title' => $data['title'],
                    ]);

                    return $p;
                })
        ];
    }
}
