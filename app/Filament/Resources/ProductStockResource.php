<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductStockResource\Pages;
use App\Filament\Resources\ProductStockResource\RelationManagers;
use App\Models\Order\OrderItem;
use App\Models\ProductStock;
use Closure;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Livewire\Component;

class ProductStockResource extends Resource
{
    protected static ?string $model = ProductStock::class;
    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Ürün Giriş';
    protected static ?string $pluralLabel = 'Ürün Girişleri';
    protected static ?string $navigationGroup = 'Stok Yönetimi';
    protected static ?string $label = 'Ürün Giriş';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Satın Alınan Ürün Bilgileri')
                    ->schema([

                        Forms\Components\Select::make('product_id')
                            ->label('Ürün')
//                            ->options(fn() => \App\Models\Lunar\ProductVariant::with(['product', 'options'])->get()->mapWithKeys(function ($x) {
//                                return [$x->id => $x->product->getName . ' ' . $x->options->map(fn($y) => json_decode($y->productOptionValue->name)->tr ?? json_decode($y->productOptionValue->name)->en)->implode(', ')];
//                            }))
                            ->options(fn() => \App\Models\Lunar\ProductVariant::query()->pluck('name', 'id'))
                            ->default(fn() => request()->query('product_id'))
                            ->disabled(fn(Component $livewire): bool => ($livewire instanceof Pages\CreateProductStock && request()->query('order_item_id')))
//                            ->extraInputAttributes(['readonly' => (fn(Component $livewire): bool => ($livewire instanceof Pages\CreateProductStock && request()->query('order_item_id'))) ? true : false])
                            ->searchable()
                            ->required(),

                        Forms\Components\TextInput::make('order_no')
                            ->label('Sipariş Numarası')
                            ->default(fn() => OrderItem::find(request()->query('order_item_id'))->order->order_number ?? null)
                            ->visible(fn(Component $livewire): bool => ($livewire instanceof Pages\CreateProductStock && request()->query('order_item_id')))
//                            ->visibleOn('create')
                            ->required()
                            ->disabled(true)
                            ->maxLength(255),

                        Forms\Components\Hidden::make('product_variant_id')
                            ->default(fn() => request()->query('product_id')),

                        Forms\Components\Hidden::make('order_item_id')
                            ->default(fn(Component $livewire): int => ($livewire instanceof Pages\CreateProductStock && request()->query('order_item_id')) ? request()->query('order_item_id') : 0),

                        Forms\Components\TextInput::make('sn')
                            ->label('Seri Numarası')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('purchase_price')
                            ->label('Satın Alma Fiyatı (KDV Dahil)')
                            ->numeric()
                            ->required(),
                        Forms\Components\TextInput::make('insurance_firm')
                            ->label('Sigorta Şirketi')
                            ->maxLength(255),
                        Forms\Components\DateTimePicker::make('insurance_started_at')
                            ->label('Sigorta Başlangıç Tarihi'),
                        Forms\Components\DateTimePicker::make('insurance_finished_at')
                            ->label('Sigorta Bitiş Tarihi'),
//                        Forms\Components\TextInput::make('moderator_id')
//                            ->required(),
                        Forms\Components\TextInput::make('supplier_vkn')
                            ->numeric()
                            ->label('Tedarikçi Vergi Kimlik Numarası'),
                        Forms\Components\TextInput::make('invoice_number')
                            ->label('Fatura Numarası')
                            ->maxLength(255),
                        Forms\Components\Toggle::make('is_kb_owner')
                            ->label('Ürün Sahibi KB Mi')
                            ->helperText('Ürünün sahibi KB ise bu alanı işaretleyin.')
                            ->default(true)
                            ->reactive()
                            ->required(),
                        Forms\Components\TextInput::make('owner_name')
                            ->label('Ürün Sahibi Adı')
                            ->hidden(fn(Closure $get) => $get('is_kb_owner') === true)
                            ->maxLength(255),
                    ])
                    ->columns(4)
                    ->columnSpan(['lg' => fn(?ProductStock $record) => $record === null ? 3 : 2]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('product.product.getName')->label('Ürün Adı'),
                Tables\Columns\TextColumn::make('product.getVaariant')->label('Varyant'),

                Tables\Columns\TextColumn::make('sn')->label('Seri Numarası'),
                Tables\Columns\TextColumn::make('purchase_price')->label('Satın Alma Fiyatı (KDV Dahil)')
                    ->formatStateUsing(fn(string $state): string => $state . ' ₺')->alignRight(),
                Tables\Columns\TextColumn::make('insurance_firm')->label('Sigorta Şirketi'),
                Tables\Columns\TextColumn::make('insurance_started_at')->label('Sigorta Başlangıç Tarihi')
                    ->date(),
                Tables\Columns\TextColumn::make('insurance_finished_at')->label('Sigorta Bitiş Tarihi')
                    ->date(),
                Tables\Columns\TextColumn::make('moderator.full_name')->label('Moderatör'),
                Tables\Columns\TextColumn::make('supplier_vkn')->label('Tedarikçi Vergi Kimlik Numarası'),
                Tables\Columns\TextColumn::make('invoice_number')->label('Fatura Numarası'),
                Tables\Columns\IconColumn::make('is_kb_owner')->label('Ürün Sahibi KB Mi')
                    ->boolean(),
//                Tables\Columns\TextColumn::make('created_at')
//                    ->dateTime(),
//                Tables\Columns\TextColumn::make('updated_at')
//                    ->dateTime(),
//                Tables\Columns\TextColumn::make('deleted_at')
//                    ->dateTime(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
                Tables\Actions\ForceDeleteBulkAction::make(),
                Tables\Actions\RestoreBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductStocks::route('/'),
            'create' => Pages\CreateProductStock::route('/create'),
            'view' => Pages\ViewProductStock::route('/{record}'),
            'edit' => Pages\EditProductStock::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
