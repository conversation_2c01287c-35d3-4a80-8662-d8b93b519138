<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ManualOrderResource\Pages;
use App\Filament\Resources\ManualOrderResource\RelationManagers;
use App\Models\Lunar\SubscriptionMonths;
use App\Models\Order;
use App\Models\User;
use App\Models\UserAddress;
use Closure;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Support\Str;
use App\Models\B2BProduct;

class ManualOrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Manuel <PERSON>';
    protected static ?string $label = 'Manuel Sipar<PERSON>ş';
    protected static ?string $pluralLabel = 'Siparişler';
    protected static ?string $navigationGroup = 'Sipariş Yönetimi';
    protected static ?string $slug = 'manual-orders';
    protected static ?int $navigationSort = 4;


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        // Forms\Components\Card::make()
                        //     ->schema(static::getFormSchema())
                        //     ->columns(2),

                        // Forms\Components\Section::make('Order items')
                        //     ->schema(static::getFormSchema('items')),
                    ])
                    ->columnSpan(['lg' => fn(?Order $record) => $record === null ? 3 : 2]),

                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn(Order $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn(Order $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn(?Order $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getFormSchema(?string $section = null): array
    {
        if ($section === 'items') {
            return [
                Forms\Components\Repeater::make('orderItems')
                    ->label('Sipariş Ürünleri')
                    ->relationship()
                    ->registerListeners([
                        'repeater::createItem' => [
                            function (Forms\Components\Component $component, string $statePath): void {
                                if ($component->isDisabled()) {
                                    return;
                                }

                                if ($statePath !== $component->getStatePath()) {
                                    return;
                                }

                                \Illuminate\Support\Facades\Log::info('Yeni sipariş ürünü eklendi', [
                                    'component_state' => $component->getState(),
                                    'state_path' => $statePath
                                ]);
                            },
                        ],
                    ])
                    ->schema([

                        Forms\Components\Toggle::make('is_b2b_product')
                            ->label('B2B Ürünü mü?')
                            ->reactive()
                            ->default(false)
                            ->afterStateUpdated(fn(Closure $get, $state, callable $set) => $set('product_type', $state ? B2BProduct::class : \App\Models\Lunar\ProductVariant::class)),

                        Forms\Components\Select::make('product_id')
                            ->label('Ürün')
                            ->visible(fn(Closure $get) => $get('is_b2b_product') == false)
                            ->options(fn() => \App\Models\Lunar\ProductVariant::where(fn($query) => $query->where('purchasable', 'always')->orWhere(fn($query) => $query->where('purchasable', 'in_stock')->where('stock', '>', 0)))->pluck('name', 'id'))
                            ->required()
                            ->searchable()
                            ->reactive()
                            //->afterStateUpdated(fn($state, callable $set) => $set('unit_price', \App\Models\Lunar\ProductVariant::find($state)?->price ?? 0))
                            ->columnSpan([
                                'md' => 4,
                            ]),

                        Forms\Components\Select::make('b2b_product_id')
                            ->label('B2B Ürünü')
                            ->options(B2BProduct::pluck('name', 'id'))
                            ->searchable()
                            ->visible(fn(Closure $get) => $get('is_b2b_product') == true)
                            ->reactive()
                            ->afterStateUpdated(fn(Closure $get, $state, callable $set) => $set('tracking_url', $state))
                            ->columnSpan([
                                'md' => 4,
                            ]),

                        Forms\Components\TextInput::make('quantity')
                            ->label('Adet')
                            ->default(1)
                            ->minValue(1)
                            ->reactive()
                            ->maxValue(99999)
                            ->numeric()
                            ->required(),

                        Forms\Components\Hidden::make('product_type')
                            ->default('App\Models\Lunar\ProductVariant'),

                        //                        Forms\Components\Hidden::make('quantity')
                        //                            ->default(1),

                        Forms\Components\Select::make('plan')
                            ->label('Kiralama Süresi')
                            ->disabled(fn(Closure $get) => $get('product_id') == null && $get('is_b2b_product') == false)
                            ->options(SubscriptionMonths::orderBy('value')->pluck('name', 'id'))
                            ->reactive()
                            //->afterStateUpdated(fn(Closure $get, $state, callable $set) => $set('unit_price', \App\Models\Lunar\ProductVariant::where('priceable_id', $get('product'))?->prices->where('subscription_months_id', $state)->price->value ?? 0))
                            ->afterStateUpdated(function (Closure $get, $state, callable $set) {

                                $price = 0;
                                if ($get('is_b2b_product') == false) {
                                    //                                if ($state != 24)
                                    $price = \App\Models\Lunar\Price::where('priceable_id', $get('product_id'))->where('subscription_months_id', $state)->first()?->price->value / 100 ?? 0;
                                } else {
                                    $price = B2BProduct::find($get('b2b_product_id'))?->price ?? 0;
                                }

                                $set('price', $price);
                                $set('total', $price * $get('quantity'));
                                $set('sub_total', $price * $get('quantity') / 120 * 100);
                                $set('tax_amount', $price * $get('quantity') / 120 * 20);
                            })
                            //->afterStateUpdated(fn($state, callable $set, Closure $get) => $set('unit_price', $get('product')))
                            ->required(),

                        Forms\Components\TextInput::make('price')
                            ->label('Kiralama Ücreti')
                            ->numeric()
                            ->required()
                            ->columnSpan([
                                'md' => 3,
                            ])
                            ->afterStateUpdated(function (Closure $get, $state, callable $set) {
                                $set('total', $state * $get('quantity'));
                                $set('sub_total', $state * $get('quantity') / 120 * 100);
                                $set('tax_amount', $state * $get('quantity') / 120 * 20);
                            }),

                        Forms\Components\Hidden::make('sub_total')
                            ->default(1),

                        Forms\Components\Hidden::make('tax_amount')
                            ->default(1),

                        Forms\Components\Hidden::make('total')
                            ->default(1),

                        Forms\Components\Hidden::make('tax_rate')
                            ->default(20),

                        Forms\Components\Hidden::make('tracking_url')
                            ->default(null),
                    ])
                    //                    ->orderable('id')
                    ->defaultItems(1)
                    ->disableLabel()
                    ->columns([
                        'md' => 10,
                    ])
                    ->required(),
            ];
        }

        return [

            Forms\Components\Select::make('user_id')
                ->relationship('user', 'full_name_db')
                ->getOptionLabelFromRecordUsing(fn(User $record) => "{$record->first_name} {$record->last_name} {$record->phone}")
                ->label('Müşteri')
                ->searchable()
                ->reactive()
                ->required()
                ->getSearchResultsUsing(
                    fn(string $search) => User::query()
                        ->where('first_name', 'like', '%' . $search . '%')
                        ->orWhere('last_name', 'like', '%' . $search . '%')
                        ->orWhere('phone', 'like', '%' . $search . '%')
                        ->orWhere('email', 'like', '%' . $search . '%')
                        ->orWhere('tckn', 'like', '%' . $search . '%')
                        ->orWhere('full_name_db', 'like', '%' . $search . '%')
                        ->pluck('full_name_db', 'id')
                )
                ->afterStateUpdated(function ($state, callable $set) {
                    $user = User::find($state);
                    $set('billing_address', json_encode(UserAddress::find($user->address->id)->toArray()));
                    $set('shipping_address', json_encode(UserAddress::find($user->address->id)->toArray()));
                    $set('billing_address_id', $user->address->id);
                    $set('shipping_address_id', $user->address->id);
                })
                //                ->getOptionLabelFromRecordUsing(fn(User $record) => "{$record->first_name} {$record->last_name} {$record->phone}")
                ->required(),

            Forms\Components\DateTimePicker::make('created_at')
                ->label('Sipariş Tarihi')
                ->default(now()),

            Forms\Components\Toggle::make('will_ownership_transfer')
                ->label('Sözleşme Sonunda Mülkiyet Devri Yapılacak mı?')
                ->default(false),

            Forms\Components\Hidden::make('billing_address')
                ->default(null),

            Forms\Components\Hidden::make('shipping_address')
                ->default(null),

            Forms\Components\Hidden::make('billing_address_id')
                ->default(null),

            Forms\Components\Hidden::make('shipping_address_id')
                ->default(null),

            Forms\Components\Hidden::make('order_number')
                ->default(env('APP_SHORT') . Str::of(Str::random(10))->upper()),

            //            $data['billing_address'] = json_encode(UserAddress::find($data['billing_address_id'])->toArray());
            //        $data['shipping_address'] = json_encode(UserAddress::find($data['shipping_address_id'])->toArray());


            //                ->createOptionForm([
            //                    Forms\Components\TextInput::make('name')
            //                        ->required(),
            //
            //                    Forms\Components\TextInput::make('email')
            //                        ->required()
            //                        ->email()
            //                        ->unique(),
            //
            //                    Forms\Components\TextInput::make('phone'),
            //                ])
            //                ->createOptionAction(function (Forms\Components\Actions\Action $action) {
            //                    return $action
            //                        ->modalHeading('Create customer')
            //                        ->modalButton('Create customer')
            //                        ->modalWidth('lg');
            //                }),

            //            Forms\Components\TextInput::make('number')
            //                ->default('OR-' . random_int(100000, 999999))
            //                ->disabled()
            //                ->required(),

            //            Forms\Components\Select::make('status')
            //                ->options([
            //                    'new' => 'New',
            //                    'processing' => 'Processing',
            //                    'shipped' => 'Shipped',
            //                    'delivered' => 'Delivered',
            //                    'cancelled' => 'Cancelled',
            //                ])
            //                ->required(),

            //            Forms\Components\Select::make('currency')
            //                ->searchable()
            //                ->getSearchResultsUsing(fn(string $query) => Currency::where('name', 'like', "%{$query}%")->pluck('name', 'id'))
            //                ->getOptionLabelUsing(fn($value): ?string => Currency::find($value)?->getAttribute('name'))
            //                ->required(),

            //            AddressForm::make('address')
            //                ->columnSpan('full'),

            //            Forms\Components\MarkdownEditor::make('notes')
            //                ->columnSpan('full'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListManualOrders::route('/'),
            'create' => Pages\CreateManualOrder::route('/create'),
            'edit' => Pages\EditManualOrder::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getEloquentQuery()->where('id', '>', 100000000);
    }
}
