<?php

namespace App\Filament\Resources\ManualOrderResource\Pages;

use App\Filament\Resources\AllOrdersResource;
use App\Filament\Resources\ManualOrderResource;
use App\PaymentPlan;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Wizard\Step;
use Filament\Resources\Pages\CreateRecord;
use Filament\Resources\Pages\CreateRecord\Concerns\HasWizard;
use App\Models\B2BProduct;
use App\Models\B2BOrder;

class CreateManualOrder extends CreateRecord
{

    use HasWizard;

    protected static string $resource = ManualOrderResource::class;

    protected function getRedirectUrl(): string
    {
        return AllOrdersResource::getUrl();
    }

    protected function getSteps(): array
    {
        return [
            Step::make('Sipariş Detayları')
                ->schema([
                    Card::make(ManualOrderResource::getFormSchema())->columns(),
                ]),

            Step::make('Sipar<PERSON>ş Ürünleri')
                ->schema([
                    Card::make(ManualOrderResource::getFormSchema('items')),
                ]),
        ];
    }

    // protected function mutateFormDataBeforeCreate(array $data): array
    // {
    //     dd($this->form->getState());
    //     // B2B ürünü seçildiğinde product_id'yi b2b_product_id'ye ata
    //     if (data_get($data, 'is_b2b_product', false)) {
    //         $data['product_id'] = data_get($data, 'b2b_product_id');
    //     }

    //     return $data;
    // }

    //    protected function handleRecordCreation(array $data): Model
    //    {
    //        return static::getModel()::create($data);
    //    }

    protected function afterCreate(): void
    {
        $order = $this->record;

        // Sipariş toplam bilgisini yaz ve ödeme planını ekle
        $this->createPaymentPlan($order);

        $order->update([
            'total' => $order->orderItems->sum('total'),
            'sub_total' => $order->orderItems->sum('sub_total'),
            'tax_amount' => $order->orderItems->sum('tax_amount'),
        ]);

        $order->orderItems->map(function ($item) {
            $item->update([
                'contract_expired_at' => $item->order->created_at->addMonth($item->planObj->value),
                'legal_contract_expired_at' => $item->order->created_at->addMonth($item->planObj->value),
                'product_id' => $item->product_type == B2BProduct::class ? $item->tracking_url : $item->product_id,
                'tracking_url' => $item->product_type == B2BProduct::class ? null : $item->tracking_url,
            ]);
            // if ($item->plan == 24) {
            //     $item->update([
            //         'plan' => 5,
            //         'contract_expired_at' => $item->order->created_at->addMonth(24),
            //         'legal_contract_expired_at' => $item->order->created_at->addMonth(24),
            //     ]);
            // } else {

            // }
        });

        // Kurumsal sipariş ise B2BOrder oluştur
        if ($order->user->is_company) {
            B2BOrder::createB2BOrderFromOrder($order);
        }
    }

    private function createPaymentPlan($order)
    {
        $pp = new PaymentPlan($order->created_at, $order->user->creditCardsRelation()->orderBy('id', 'desc')->first()?->id ?? 1, $order->id);
        $pp->add(0, 1);
        $order->orderItems->map(function ($item) use ($pp) {
            //            // 24 aylık plan seçilmişse, 24 aylık planı ekliyoruz değilse seçilen aylık planı ekliyoruz
            //            if ($item->plan == 24) {
            //                $pp->add($item->total, 24);
            //            } else {
            //                $pp->add($item->total, $item->planObj->value);
            //            }

            $pp->add($item->total, $item->planObj->value);
        });
        $pp->save();

        // Normal ödeme planı oluştururken hep ilk plan ödendi işaretlendiği için burada da ilk plan ödenMEdi işaretliyoruz
        $order->orderTransactions->first()->update([
            'payment_status_id' => 2, // Ödenmedi, çünkü bu kurumsal sipariş
        ]);

        //dd( SubscriptionMonths::getSelectedMonth($cart->items->first()->month), $cart->items->first()->month );
        //        for ($i = 1; $i <= SubscriptionMonths::getSelectedMonth($order->orderItems->max('plan')); $i++) {
        //        for ($i = 1; $i <= SubscriptionMonths::find($order->orderItems->max('plan'))->value; $i++) {
        //            $ot = OrderTransaction::create([
        //                'order_id' => $order->id,
        //                'due_date' => now()->addMonth($i - 1),
        //                'payment_status_id' => 2,
        //                'amount' => $order->orderItems->sum('total'),
        ////                'card_id' => 1,
        //            ]);
        //        }
    }
}
