<?php

namespace App\Filament\Resources\CouponResource\Pages;

use App\Filament\Resources\CouponResource;
use App\Models\Coupon;
use Filament\Forms;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCoupons extends ListRecords
{
    protected static string $resource = CouponResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('bulkCreateCoupons')
                ->label('Toplu Kupon Oluştur')
                ->form([
                    Forms\Components\TextInput::make('coupon_code')
                        ->required()
                        ->label('Kopyalanacak Kupon Kodu')
                        ->required(),
                    Forms\Components\TextInput::make('quantity')
                        ->label('Kaç Adet Kupon')
                        ->required()
                        ->minValue(1)
                        ->numeric(),
                    Forms\Components\TextInput::make('description')
                        ->label('Kod Açıklaması')
                        ->required(),
                ])
                ->action(function (array $data) {
                    Coupon::createMassCoupon($data['coupon_code'], $data['quantity'], $data['description']);
                }),
        ];
    }
}
