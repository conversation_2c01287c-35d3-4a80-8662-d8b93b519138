<?php

namespace App\Filament\Resources\OverdueOrderTransactionsResource\Pages;

use App\Filament\Resources\OverdueOrderTransactionsResource;
use Filament\Pages\Actions;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Mail;

class ViewOverdueOrderTransactions extends ViewRecord
{
    protected static string $resource = OverdueOrderTransactionsResource::class;

    protected function getActions(): array
    {
        return [
            Action::make('order_offical_delay')
                ->label('Gecikme İhtarı Gönder')
                ->color('danger')
                ->action(fn(array $data) => $this->sendOfficalDelayEmail())
                ->disabled(function () {
                    return $this->record->offical_delay_emailed_at != null;
                })
                ->requiresConfirmation(),

            Action::make('notice_of_termination_emailed_at')
                ->label('<PERSON><PERSON><PERSON>')
                ->color('danger')
                ->action(fn(array $data) => $this->sendNoticeOfTerminationEmail())
                ->disabled(function () {
                    return $this->record->offical_delay_emailed_at == null || $this->record->notice_of_termination_emailed_at != null;
                })
                ->requiresConfirmation(),

            Actions\EditAction::make(),
        ];
    }

    private function sendOfficalDelayEmail()
    {
        $this->record->sendOfficalDelayEmail();
    }

    private function sendNoticeOfTerminationEmail()
    {
        Mail::driver('hukuk')->queue(new \App\Mail\NoticeOfterminationEmail($this->record));
        $this->record->update(['notice_of_termination_emailed_at' => now()]);
    }
}
