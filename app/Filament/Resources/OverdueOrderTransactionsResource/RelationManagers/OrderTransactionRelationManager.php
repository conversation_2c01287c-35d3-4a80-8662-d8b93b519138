<?php

namespace App\Filament\Resources\OverdueOrderTransactionsResource\RelationManagers;

use App\Models\CreditCard;
use App\Models\OrderTransaction;
use Closure;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Actions\Action;

class OrderTransactionRelationManager extends RelationManager
{
    protected static string $relationship = 'sameOrderTransactions';

    protected static ?string $label = 'Bu Siparişe Ait Ödeme Planı';
    protected static ?string $recordTitleAttribute = 'uuid';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([]);
    }

    public static function table(Table $table): Table
    {
        $rowNumber = 0;
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('no')
                    ->label('No')
                    ->size('sm')
                    ->formatStateUsing(function () use (&$rowNumber) {
                        $rowNumber++;
                        return $rowNumber;
                    }),
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->size('sm')
                    ->visible(fn(): bool => auth()->user()->hasRole('Super Admin'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('due_date')
                    ->label('Vade Tarihi')
                    ->alignEnd()
                    ->size('sm')
                    ->date(),
                Tables\Columns\TextColumn::make('next_try_at')
                    ->label('Sonraki Deneme Tarihi')
                    ->size('sm')
                    ->dateTime(),
                Tables\Columns\TextColumn::make('paymentStatus.name')
                    ->label('Ödeme Durumu')
                    ->size('sm'),
                Tables\Columns\TextColumn::make('last_payment_check')
                    ->label('Son Ödeme Kontrol Zamanı')
                    ->size('sm')
                    ->dateTime(),
                Tables\Columns\TextColumn::make('bank_last_message')
                    ->label('Banka Son Mesajı')
                    ->size('sm')
                    ->limit(30),
                Tables\Columns\TextColumn::make('note')
                    ->label('Not')
                    ->size('sm')
                    ->limit(30),
                Tables\Columns\TextColumn::make('amount')
                    ->formatStateUsing(fn(string $state): string => number_format($state, 2, ',', '.') . '₺')
                    ->label('Tutar')
                    ->alignEnd()
                    ->size('sm'),
                Tables\Columns\ViewColumn::make('filename')
                    ->view('filament.tables.columns.file-icon')
                    ->label('Dekont')
                    ->alignCenter(),
            ])
            ->filters([])
            ->headerActions([])
            ->actions([
                Action::make('Ödeme Almayı Dene')
                    ->color('danger')
                    ->visible(fn(OrderTransaction $record) => !in_array($record->payment_status_id, [1, 4]))
                    ->mountUsing(fn(Forms\ComponentContainer $form, OrderTransaction $record) => $form->fill([
                        'amountToPay' => (float)$record->amount,
                        'orderUserCards' => $record->order->user->creditCardsRelation->first()->id,
                        'user_id' => $record->order->user->id,
                    ]))
                    ->action(fn(OrderTransaction $record, array $data) => $record->payWithAmount($data['amountToPay'], $data['orderUserCards']))
                    ->form(
                        [

                            Forms\Components\Hidden::make('user_id')->reactive(),

                            Forms\Components\Placeholder::make('due_date')
                                ->label('Çekimi Denenecek Olan Borç Vadesi')
                                ->content(fn(?OrderTransaction $record): ?string => $record->due_date->format('d.m.Y')),

                            Forms\Components\Select::make('orderUserCards')
                                ->label('Kullanıcı Kartları')
                                ->options(fn(Closure $get) => CreditCard::where('billable_id', $get('user_id'))->pluck('number', 'id'))
                                ->required(),

                            Forms\Components\TextInput::make('amountToPay')
                                ->label('Tutar')
                                ->numeric()
                                ->required(),
                        ]
                    )
                    //->url(fn(OrderTransaction $record): string => '/')
                    ->requiresConfirmation(),

                Action::make('Havale')
                    ->color('danger')
                    ->visible(fn(OrderTransaction $record) => !in_array($record->payment_status_id, [1, 4]))
                    ->mountUsing(fn(Forms\ComponentContainer $form, OrderTransaction $record) => $form->fill([
                        'amountToPay' => (float)$record->amount,
                    ]))
                    ->action(fn(OrderTransaction $record, array $data) => $record->registerBankPayment($data['amountToPay'], $data['filename']))
                    ->form(
                        [
                            Forms\Components\FileUpload::make('filename')
                                ->label('Banka Ödeme Ekran Görüntüsü')
                                ->disk('s3')
                                ->directory('payments/' . date('Y/m/d'))
                                ->visibility('private')
                                ->acceptedFileTypes(['image/*', 'application/pdf'])
                                ->maxSize(1024 * 5)
                                ->required(),
                            Forms\Components\TextInput::make('amountToPay')
                                ->label('Tutar')
                                ->numeric()
                                ->required(),
                        ]
                    )
                    //->url(fn(OrderTransaction $record): string => '/')
                    ->requiresConfirmation(),
            ])
            ->bulkActions([])
            ->defaultSort('due_date', 'asc');
    }
}
