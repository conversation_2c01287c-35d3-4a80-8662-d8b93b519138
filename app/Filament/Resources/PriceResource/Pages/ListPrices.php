<?php

namespace App\Filament\Resources\PriceResource\Pages;

use App\Filament\Resources\PriceResource;
use App\Models\Lunar\Price;
use Exception;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Str;
use Konnco\FilamentImport\Actions\ImportAction;
use Konnco\FilamentImport\Actions\ImportField;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use pxlrbt\FilamentExcel\Actions\Pages\ExportAction;

class ListPrices extends ListRecords
{
    protected static string $resource = PriceResource::class;

    protected function getActions(): array
    {
        return [

            ExportAction::make()
                ->exports([
                    ExcelExport::make()
                        ->fromTable()
                        ->queue()
                        ->withFilename(date('Y-m-d') . '-prices-export')
                ]),

            ImportAction::make()
                ->fields([
                    ImportField::make('id')
                        ->label('Fiyat ID')
                        ->helperText('Kiralabunu V2 Fiyat Güncelleme Export ID'),
                    //                    ImportField::make('urun_id')
                    //                        ->label('Ürün ID')
                    //                        ->helperText('Kiralabunu V2 Ürün ID'),
                    ImportField::make('price')
                        ->label('Ürün Ay Kiralama Bedeli'),

                    ImportField::make('compare_price')
                        ->label('Ürün Ay İndirimli Kiralama Bedeli'),
                ])
                ->mutateBeforeCreate(function ($row) {

                    try {
                        $price = (int)Str::of($row['price'])->remove(' ₺')->__toString();

                        //                    if ($row['id'] == '17439') {
                        //                        //$compare_price = (int)Str::of($row['compare_price'])->remove(' ₺')->__toString();
                        //
                        //
                        //                        $row['price'] = (int)Str::of($row['compare_price'])->remove(' ₺')->__toString() * 100;
                        //                        $row['compare_price'] = (int)Str::of($row['price'])->remove(' ₺')->__toString();
                        //                        dd($row);
                        //                    }
                        if (array_key_exists('compare_price', $row)) {
                            $compare_price = (int)Str::of($row['compare_price'])->remove(' ₺')->__toString();
                            if ($compare_price > 0) {
                                $row['price'] = (int)Str::of($row['compare_price'])->remove(' ₺')->__toString() * 100;
                                $row['compare_price'] = $price * 100;
                            } else {
                                $row['price'] = $price * 100;
                                $row['compare_price'] = $compare_price;
                            }
                        } else {
                            $row['price'] = $price * 100;
                        }
                        return $row;
                    } catch (Exception $exception) {
                        logger()->error('mutateBeforeCreate ' . $exception->getFile() . ' ' . $exception->getMessage());
                    }
                })
                ->handleRecordCreation(function ($data) {
                    // Yeni satır açmadan mevcutta güncelleme yapması için bu alan gerekiyor
                    try {
                        logger('handleRecordCreation ' . $data['id']);
                        return Price::updateOrCreate([
                            'id' => $data['id'],
                        ], [
                            'price' => $data['price'],
                            'compare_price' => $data['compare_price'] ?? 0,
                        ]);
                    } catch (Exception $exception) {
                        logger()->error('handleRecordCreation' . $exception->getFile() . ' ' . $exception->getMessage());
                    }
                })
        ];
    }

    //    public function getTableRecordKey(Model $record): string
    //    {
    //        return 'prices.id';
    //    }

}
