<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TodayDueOrderTransactionsResource\Pages;
use App\Filament\Resources\TodayDueOrderTransactionsResource\RelationManagers;
use App\Models\OrderTransaction;
use App\Models\PaymentStatus;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use Illuminate\Support\Str;

class TodayDueOrderTransactionsResource extends Resource
{
    protected static ?string $model = OrderTransaction::class;
    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Günlük Ödeme Planları  ';
    protected static ?string $label = 'Bugün Vadeli Ödeme Planları';
    protected static ?string $navigationGroup = 'Tahsilat Yönetimi';
    protected static ?string $slug = 'today-due-order-transactions';

    protected static function getNavigationBadge(): ?string
    {
        return static::getModel()::whereBetween('due_date', [today()->startOfDay(), today()->endOfDay()])
            ->whereNotBetween('created_at', [today()->startOfDay(), today()->endOfDay()])
            ->where('amount', '>', 0)
            ->whereHas('order', function ($query) {
                $query->whereNull('deleted_at');
            })
            ->where('payment_status_id', 2)
            ->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Ödeme Planı Bilgileri')
                    ->schema([

                        Forms\Components\Placeholder::make('order_number')
                            ->label('Sipariş Numarası')
                            ->content(fn(?OrderTransaction $record): ?string => $record->order->order_number),

                        Forms\Components\Placeholder::make('customer_name')
                            ->label('Müşteri Adı')
                            ->content(fn(?OrderTransaction $record): ?string => $record->order->user->full_name_db),

                        Forms\Components\Placeholder::make('due_date')
                            ->label('Kira Vade Tarihi')
                            ->content(fn(?OrderTransaction $record): ?string => $record->due_date->format('d.m.Y')),

                        Forms\Components\Placeholder::make('last_payment_check')
                            ->label('Son Ödeme Kontrol Tarihi')
                            ->content(fn(?OrderTransaction $record): ?string => $record->last_payment_check?->format('d.m.Y')),

                        Forms\Components\Select::make('payment_status_id')
                            ->label('Ödeme Durumu')
                            ->options(PaymentStatus::all()->pluck('name', 'id'))
                            ->required(),

                        Forms\Components\TextInput::make('amount')
                            ->label('Kiralama Tutarı')
                            ->numeric()
                            ->mask(
                                fn(Forms\Components\TextInput\Mask $mask) => $mask
                                    ->numeric()
                                    ->decimalPlaces(2)
                                    ->decimalSeparator('.')
                            )
                            ->required(),

                    ])
                    ->columns(3),
                Forms\Components\Section::make('Ödeme Planı Notu')
                    ->schema([
                        Forms\Components\RichEditor::make('note')
                            ->label('Not')
                            ->disableToolbarButtons([
                                'attachFiles',
                                'codeBlock',
                            ])
                            ->maxLength(65535),
                        //                        Forms\Components\TextInput::make('card_id')
                        //                            ->required(),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->visible(fn(): bool => auth()->user()->hasRole('Super Admin'))
                    ->size('sm')
                    ->label('ID')
                    ->alignEnd()
                    ->fontFamily('mono')
                    ->sortable(),
                Tables\Columns\ViewColumn::make('invoice')
                    ->view('filament.tables.columns.invoice-icon')
                    ->label('Fatura')
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('order.order_number')
                    ->size('sm')
                    ->label('Sipariş Numarası')
                    ->fontFamily('mono')
                    ->sortable(),
                Tables\Columns\TextColumn::make('order.user.full_name')
                    ->size('sm')
                    ->label('Müşteri Adı')
                    ->formatStateUsing(fn($state) => Str::title($state))
                    ->wrap()
                    ->sortable(),
                Tables\Columns\TextColumn::make('order.user.clean_phone_number')
                    ->size('sm')
                    ->label('Müşteri Tel')
                    ->alignEnd()
                    ->fontFamily('mono')
                    ->sortable(),
                Tables\Columns\TextColumn::make('paymentStatus.name')
                    ->sortable()
                    ->wrap()
                    ->size('sm')
                    ->label('Ödeme Durumu'),
                Tables\Columns\TextColumn::make('order.status_text')
                    ->label('Statü')
                    ->size('sm')
                    ->wrap(),
                Tables\Columns\TextColumn::make('order.orderProductList')
                    ->label('Ürün Adı')
                    ->size('sm')
                    ->html(),
                Tables\Columns\TextColumn::make('plan')
                    ->getStateUsing(function (OrderTransaction $record): null|string {
                        return $record->order?->orderItems->map(fn($item) => $item->planObj->name)->implode('<br>');
                    })
                    ->alignEnd()
                    ->html()
                    ->size('sm')
                    ->label('Kiralama Süresi'),
                Tables\Columns\TextColumn::make('plan2')
                    ->getStateUsing(function (OrderTransaction $record): null|string {
                        return $record->order->created_at->diffInMonths($record->due_date) + 1 . '. Ay';
                    })
                    ->alignEnd()
                    ->size('sm')
                    ->html()
                    ->label('Kaçıncı Ay'),
                Tables\Columns\TextColumn::make('amount')
                    ->label('Aylık Tutar')
                    ->alignEnd()
                    ->fontFamily('mono')
                    ->formatStateUsing(fn($state) => number_format($state, 2, ',', '.') . ' ₺')
                    ->size('sm'),
                Tables\Columns\TextColumn::make('order.created_at')
                    ->label('Sipariş Tarihi')
                    ->date()
                    ->size('sm'),
                Tables\Columns\TextColumn::make('last_payment_check')
                    ->label('Son Çekim Denemesi Tarihi')
                    ->date()
                    ->size('sm'),

            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([])
            ->bulkActions([
                ExportBulkAction::make()
            ])
            ->defaultSort('payment_status_id', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\OrderTransactionRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTodayDueOrderTransactions::route('/'),
            'create' => Pages\CreateTodayDueOrderTransactions::route('/create'),
            'view' => Pages\ViewTodayDueOrderTransactions::route('/{record}'),
            'edit' => Pages\EditTodayDueOrderTransactions::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([])
            ->whereBetween('due_date', [today()->startOfDay(), today()->endOfDay()])
            ->whereNotBetween('created_at', [today()->startOfDay(), today()->endOfDay()])
            ->where('amount', '>', 0)
            ->whereHas('order', function ($query) {
                $query->whereNull('deleted_at');
            })
            ->where('payment_status_id', 2);
    }
}
