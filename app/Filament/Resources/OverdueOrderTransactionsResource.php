<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OverdueOrderTransactionsResource\Pages;
use App\Filament\Resources\OverdueOrderTransactionsResource\RelationManagers;
use App\Models\OrderTransaction;
use App\Models\PaymentStatus;
use App\Models\UserAddress;
use App\States\Order\Investigation;
use App\States\Order\OrderApproved;
use App\States\Order\OrderAtLegalPursuit;
use App\States\Order\OrderCancelled;
use App\States\Order\OrderCompleted;
use App\States\Order\OrderDenied;
use App\States\Order\OrderDocumentWaiting;
use App\States\Order\OrderEvaluation;
use App\States\Order\OrderReceived;
use App\States\Order\OrderRefunded;
use App\States\Order\OrderRenting;
use App\States\Order\OrderShipped;
use App\States\OrderTransactionCustomerContact\CantReached;
use App\States\OrderTransactionCustomerContact\LineOff;
use App\States\OrderTransactionCustomerContact\NotContacted;
use App\States\OrderTransactionCustomerContact\Talked;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class OverdueOrderTransactionsResource extends Resource
{
    protected static ?string $model = OrderTransaction::class;
    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Verimor';
    protected static ?string $label = 'Gecikmiş Ödeme Planları';
    protected static ?string $navigationGroup = 'Tahsilat Yönetimi';
    protected static ?int $navigationSort = 3;
    protected static ?string $slug = 'overdue-order-transactions-eski';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Ödeme Planı Bilgileri')
                    ->schema([

                        Forms\Components\Placeholder::make('order_number')
                            ->label('Sipariş Numarası')
                            ->content(fn(?OrderTransaction $record): ?string => $record->order->order_number),

                        Forms\Components\Placeholder::make('due_date')
                            ->label('Kira Vade Tarihi')
                            ->content(fn(?OrderTransaction $record): ?string => $record->due_date->format('d.m.Y')),

                        Forms\Components\Placeholder::make('last_payment_check')
                            ->label('Son Ödeme Kontrol Tarihi')
                            ->content(fn(?OrderTransaction $record): ?string => $record->last_payment_check?->format('d.m.Y')),

                        Forms\Components\Placeholder::make('order.user.phone')
                            ->label('Telefon Numarası')
                            ->content(fn(?OrderTransaction $record): ?string => $record->order->user->phone),

                        Forms\Components\Placeholder::make('order.user.email')
                            ->label('E-Posta Adresi')
                            ->content(fn(?OrderTransaction $record): ?string => $record->order->user->email),

                        Forms\Components\Select::make('payment_status_id')
                            ->label('Ödeme Durumu')
                            ->disabled()
                            ->options(PaymentStatus::all()->pluck('name', 'id'))
                            ->required(),

                        Forms\Components\TextInput::make('amount')
                            ->label('Kiralama Tutarı')
                            ->numeric()
                            ->disabled()
                            ->mask(
                                fn(Forms\Components\TextInput\Mask $mask) => $mask
                                    ->numeric()
                                    ->decimalPlaces(2)
                                    ->decimalSeparator('.')
                            )
                            ->required(),

                        Forms\Components\Select::make('customer_contact_status')
                            ->label('Müşteri İletişim Durumu')
                            ->options([
                                CantReached::class => 'Ulaşılamadı',
                                LineOff::class => 'Hat Kapalı',
                                NotContacted::class => 'İletişime Geçilmedi',
                                Talked::class => 'Ulaşıldı',
                            ])
                            ->required(),
                    ])
                    ->columns(3),
                Forms\Components\Section::make('Ödeme Planı Notu')
                    ->schema([
                        Forms\Components\RichEditor::make('note')
                            ->label('Not')
                            ->disableToolbarButtons([
                                'attachFiles',
                                'codeBlock',
                            ])
                            ->maxLength(65535),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->size('sm')
                    ->visible(fn(): bool => auth()->user()->hasRole('Super Admin'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('order.order_number')
                    ->searchable(isIndividual: true),
                Tables\Columns\TextColumn::make('order.user.full_name_db')
                    ->searchable(isIndividual: true)
                    ->label('Müşteri Adı'),
                Tables\Columns\TextColumn::make('order.user.phone')
                    ->getStateUsing(function (OrderTransaction $record): null|string {
                        $no = Str::of($record->order->user->phone)->remove('(')->remove(')')->remove(' ')->remove('-');
                        if ($no->startsWith(5))
                            $no = $no->prepend('+90');
                        if ($no->startsWith(0))
                            $no = $no->prepend('+9');
                        return $no;
                    })
                    ->html()
                    ->searchable(isIndividual: true)
                    ->label('Müşteri Tel'),

                Tables\Columns\TextColumn::make('order.status_text')
                    ->label('Statü')
                    ->wrap(),
                Tables\Columns\TextColumn::make('due_date')
                    ->label('Vade Tarihi')
                    ->date(),

                Tables\Columns\TextColumn::make('order.orderProductList')
                    ->label('Ürün Adı')
                    ->wrap()
                    ->html(),
                Tables\Columns\TextColumn::make('plan')
                    ->getStateUsing(function (OrderTransaction $record): null|string {
                        return $record->order?->orderItems->map(fn($item) => $item->planObj->name)->implode('<br />');
                    })
                    ->alignEnd()
                    ->html()
                    ->label('Kiralama Süresi'),

                Tables\Columns\TextColumn::make('order.shippingAddress')
                    ->getStateUsing(function (OrderTransaction $record): null|string {
                        return $record->order->shippingAddress?->city . ' / ' . $record->order->shippingAddress?->county;
                    })
                    ->label('İl İlçe')
                    ->wrap(),

                Tables\Columns\TextColumn::make('amount')
                    ->label('Aylık Tutar')
                    ->formatStateUsing(fn(string $state): string => $state . ' ₺')
                    ->alignRight(),

                Tables\Columns\TextColumn::make('order.created_at')
                    ->label('Sipariş Tarihi')
                    ->date(),

                Tables\Columns\TextColumn::make('last_payment_check')
                    ->label('Son Çekim Denemesi Tarihi')
                    ->date(),

                //                Tables\Columns\TextColumn::make('delay_emailed_at')
                //                    ->label('Gecikme EPostası')
                //                    ->date(),
                Tables\Columns\TextColumn::make('offical_delay_emailed_at')
                    ->label('Gecikme Bildirimi')
                    ->date(),
                Tables\Columns\TextColumn::make('notice_of_termination_emailed_at')
                    ->label('Fesih Bildirimi')
                    ->date(),
                //                Tables\Columns\TextColumn::make('customer_contact_status')
                //                    ->label('Müşteri İletişim Durumu'),

                //                Tables\Columns\TextColumn::make('last_payment_check')
                //                    ->dateTime(),
                //                Tables\Columns\TextColumn::make('note'),
                //
                //                Tables\Columns\TextColumn::make('card_id'),
                //                Tables\Columns\TextColumn::make('created_at')
                //                    ->dateTime(),
                //                Tables\Columns\TextColumn::make('updated_at')
                //                    ->dateTime(),
            ])
            ->filters([
                //Tables\Filters\TrashedFilter::make(),
                Tables\Filters\Filter::make('status')
                    ->form([
                        Forms\Components\Select::make('status')
                            ->label('Sipariş Statüsü')
                            ->options([
                                OrderReceived::class => 'Yeni',
                                OrderEvaluation::class => 'Değerlendiriliyor',
                                OrderDocumentWaiting::class => 'Belge Bekleniyor',
                                Investigation::class => 'Şüpheli İşlem',
                                OrderApproved::class => 'Onaylandı',
                                OrderShipped::class => 'Kargolandı',
                                OrderDenied::class => 'Reddedildi',
                                OrderCancelled::class => 'İptal',
                                OrderCompleted::class => 'Tamamlandı',
                                OrderRefunded::class => 'İade',
                                OrderRenting::class => 'Kiralama Devam Ediyor',
                                OrderAtLegalPursuit::class => 'Yasal Takip Siparişi',
                            ]),
                        //->placeholder(fn($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['status'],
                                fn(Builder $query, $statu): Builder => $query->whereHas('order', function (Builder $query) use ($statu) {
                                    $query->where('status', $statu);
                                }),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['status'] ?? null) {
                            $indicators['status'] = 'Sipariş Durumu ' . get_order_status_text($data['status']) . ' olarak filtrelenmiştir.';
                        }

                        return $indicators;
                    }),
            ])
            ->actions([])
            ->bulkActions([
                ExportBulkAction::make()
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\OrderTransactionRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOverdueOrderTransactions::route('/'),
            'create' => Pages\CreateOverdueOrderTransactions::route('/create'),
            'view' => Pages\ViewOverdueOrderTransactions::route('/{record}'),
            'edit' => Pages\EditOverdueOrderTransactions::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            //            ->withoutGlobalScopes([
            //                SoftDeletingScope::class,
            //            ])
            ->where('payment_status_id', 2)
            ->where('due_date', '<', now())
            ->whereHas('order.user', function ($query) {
                $query->where('is_company', false);
            });
    }
}
