<?php

namespace App\Filament\Resources;

use App\Exports\B2BOrderExport;
use App\Filament\Resources\B2BOrderResource\Pages;
use App\Filament\Resources\B2BOrderResource\RelationManagers\NotesRelationManager;
use App\Filament\Resources\B2BOrderResource\RelationManagers\OrderItemsRelationManager;
use App\Filament\Resources\B2BOrderResource\RelationManagers\OrderTransactionRelationManager;
use App\Models\B2BOrder;
use App\States\Order\OrderApproved;
use App\States\Order\OrderRenting;
use App\States\Order\OrderShipped;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class B2BOrderResource extends Resource
{
    protected static ?string $model = B2BOrder::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Kurumsal Siparişler';
    protected static ?string $label = 'Kurumsal Sipariş';
    protected static ?string $pluralLabel = 'Kurumsal Siparişler';
    protected static ?string $navigationGroup = 'Kurumsal Siparişler';
    protected static ?int $navigationSort = 5;
    protected static ?string $slug = 'b2b-orders';

    protected static function getNavigationBadge(): ?string
    {
        return static::getModel()::whereHas('order', function ($query) {
            $query->whereIn('status', [OrderRenting::class, OrderShipped::class, OrderApproved::class]);
        })->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Sipariş Bilgileri')
                    ->schema([
                        Forms\Components\TextInput::make('order_number')
                            ->label('Sipariş No')
                            ->formatStateUsing(fn($record) => $record->order->order_number)
                            ->extraInputAttributes(['readonly' => true])
                            ->required(),
                        Forms\Components\TextInput::make('external_responsible_person')
                            ->label('Firma Yetkilisi')
                            ->required(),
                        Forms\Components\TextInput::make('external_responsible_person_phone')
                            ->label('Firma Yetkilisi Telefon')
                            ->numeric()
                            ->mask(fn(Forms\Components\TextInput\Mask $mask) => $mask->pattern('(500) 000 00 00'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('internal_responsible_person')
                            ->label('Hesap Sorumlusu')
                            ->required(),
                    ])
                    ->columns(5),
                Forms\Components\Section::make('Firma Bilgileri')
                    ->schema([
                        Forms\Components\TextInput::make('company_full_name')
                            ->label('Unvan')
                            ->extraInputAttributes(['readonly' => true])
                            ->required(),
                        Forms\Components\TextInput::make('tax_office')
                            ->label('Vergi Dairesi')
                            ->extraInputAttributes(['readonly' => true])
                            ->required(),
                        Forms\Components\TextInput::make('tax_number')
                            ->label('Vergi No')
                            ->extraInputAttributes(['readonly' => true])
                            ->required(),
                        Forms\Components\TextInput::make('overdue_rent_total')
                            ->label('Toplanacak Kira')
                            ->formatStateUsing(fn($state) => number_format($state, 2, ',', '.') . ' ₺')
                            ->extraInputAttributes(['readonly' => true]),
                    ])->columns(4),
                Forms\Components\Section::make('Sözleşme Bilgileri')
                    ->schema([
                        Forms\Components\TextInput::make('contract_number')
                            ->label('Sözleşme Kodu'),
                        Forms\Components\DatePicker::make('contract_start_date')
                            ->label('Sözleşme Başlangıç Tarihi'),
                        Forms\Components\TextInput::make('term')
                            ->label('Süre (Ay)')
                            ->extraInputAttributes(['readonly' => true])
                            ->numeric(),
                        Forms\Components\TextInput::make('payment_type')
                            ->label('Bakiye Ay')
                            ->extraInputAttributes(['readonly' => true])
                            ->numeric(),
                        Forms\Components\Toggle::make('has_payment_due')
                            ->label('Ödeme Vadesi Var mı?')
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, $state) {
                                if (!$state) {
                                    $set('payment_due_days', 0);
                                }
                            }),
                        Forms\Components\TextInput::make('payment_due_days')
                            ->label('Ödeme Vadesi (Gün)')
                            ->numeric()
                            ->minValue(1)
                            ->visible(fn(callable $get) => $get('has_payment_due')),
                        Forms\Components\FileUpload::make('scanned_agreement_path')
                            ->label('Taranmış Sözleşme')
                            ->acceptedFileTypes(['application/pdf'])
                            ->maxSize(20480) // 20MB
                            ->directory('encrypted-b2b-files/agreements')
                            ->disk('s3')
                            ->visibility('private')
                            ->getUploadedFileNameForStorageUsing(function ($file, $record) {
                                // Record varsa ID'sini kullan, yoksa timestamp kullan
                                $filename = $record && $record->id ? $record->id : time() . '_' . uniqid();
                                return $filename . '.pdf';
                            })
                            ->helperText('Maksimum 20MB PDF dosyası yükleyebilirsiniz. Dosyalar şifrelenerek saklanır.'),
                    ])->columns(4),

                Forms\Components\Section::make('Teminat Bilgileri')
                    ->schema([
                        Forms\Components\CheckboxList::make('surety_and_payment_guarantee')
                            ->options([
                                'kefalet' => 'Kefalet',
                                'senet' => 'Senet',
                                'teminat_mektubu' => 'Teminat Mek.',
                            ])
                            ->label('Kefalet/Garanti'),
                        // Forms\Components\TextInput::make('promissory_note')
                        //     ->label('Senet'),
                        Forms\Components\TextInput::make('letter_of_guarantee_is_exist')
                            ->label('Teminat Mek. Kurumu'),
                        Forms\Components\DatePicker::make('letter_of_guarantee_expiry_date')
                            ->label('Teminat Mek. Bitiş Tarihi'),
                    ])->columns(4),

                Forms\Components\Section::make('Temlik Bilgileri')
                    ->schema([
                        Forms\Components\TextInput::make('assignment_status')
                            ->label('Temliğin Kime Verildiği'),
                        Forms\Components\TextInput::make('inventory_value')
                            ->label('Ürünlerin Envanter Değeri')
                            ->numeric()
                            ->suffix('₺')
                            ->mask(
                                fn(Forms\Components\TextInput\Mask $mask) => $mask
                                    ->numeric()
                                    ->decimalPlaces(2)
                                    ->thousandsSeparator(',')
                            ),
                        // Forms\Components\TextInput::make('assignment_term')
                        //     ->label('Temlik Süresi')
                        //     ->numeric(),
                        // Forms\Components\TextInput::make('remaining_assignment_term')
                        //     ->label('Kalan Temlik Süresi')
                        //     ->numeric(),
                    ])->columns(3),
                Forms\Components\Section::make('Sigorta Bilgileri')
                    ->schema([
                        Forms\Components\Toggle::make('has_insurance')
                            ->label('Sigorta'),
                        Forms\Components\TextInput::make('insurance_price')
                            ->label('Sigorta Tutarı')
                            ->numeric()
                            ->mask(
                                fn(Forms\Components\TextInput\Mask $mask) => $mask
                                    ->numeric()
                                    ->decimalPlaces(2)
                                    ->thousandsSeparator(',')
                            ),
                        Forms\Components\TextInput::make('insurance_company')
                            ->label('Sigorta Firma'),
                        Forms\Components\DatePicker::make('insurance_expiry_date')
                            ->label('Sigorta Bitiş Tarihi'),
                    ])->columns(4),

                Forms\Components\Section::make('Diğer Bilgiler')
                    ->schema([
                        Forms\Components\TextInput::make('credit_type')
                            ->label('Kredi Türü'),
                        // Forms\Components\TextInput::make('financing_institution')
                        //     ->label('Finansman Kuruluşu'),
                        Forms\Components\Radio::make('subscription_status')
                            ->options([
                                'Vadeli' => 'Vadeli',
                                'OPERASYONEL' => 'Operasyonel',
                            ])
                            ->label('Abonelik Durumu'),
                        Forms\Components\TextInput::make('authorized_signatory_list')
                            ->label('İmza Sirkuleri Durumu'),
                        Forms\Components\TextInput::make('dealer_name')
                            ->label('Bayi Adı'),
                        Forms\Components\TextInput::make('referral_name')
                            ->label('Serbest Referans Adı'),
                    ])->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order.user.full_name_db')
                    ->label('Unvan')
                    ->searchable(isIndividual: true)
                    ->sortable(query: fn($query, $direction) => $query->orderBy('company_full_name', $direction))
                    ->size('sm')
                    ->wrap()
                    ->limit(50),
                Tables\Columns\TextColumn::make('order.order_number')
                    ->label('Sipariş No')
                    ->fontFamily('mono')
                    ->alignEnd()
                    ->searchable(isIndividual: true)
                    ->size('sm'),
                // Tables\Columns\TextColumn::make('b2bOrder.tax_office')
                //     ->label('Vergi Dairesi')
                //     ->searchable()
                //     ->sortable(),
                // Tables\Columns\TextColumn::make('b2bOrder.tax_number')
                //     ->label('Vergi No')
                //     ->searchable()
                //     ->sortable(),
                Tables\Columns\TextColumn::make('contract_number')
                    ->label('Sözleşme Kodu')
                    ->searchable(isIndividual: true)
                    ->size('sm')
                    ->sortable(),
                Tables\Columns\TextColumn::make('contract_start_date')
                    ->date()
                    ->label('Sözleşme Başlangıç Tarihi')
                    ->searchable(isIndividual: true)
                    ->sortable()
                    ->size('sm'),
                // Tables\Columns\TextColumn::make('b2bOrder.monthly_payment')
                //     ->label('Aylık Ödeme')
                //     ->money('try')
                //     ->sortable(),
                // Tables\Columns\TextColumn::make('b2bOrder.monthly_payment_with_tax')
                //     ->label('Aylık Ödeme (KDV Dahil)')
                //     ->money('try')
                //     ->sortable(),
                Tables\Columns\TextColumn::make('order.will_ownership_transfer')
                    ->formatStateUsing(fn($state) => $state ? 'Vadeli' : 'Operasyonel')
                    ->label('Vadeli/Operasyonel')
                    ->sortable()
                    ->size('sm'),
                Tables\Columns\TextColumn::make('term')
                    ->label('Süre')
                    ->formatStateUsing(fn($state) => $state . ' Ay')
                    ->sortable()
                    ->alignEnd()
                    ->size('sm'),
                Tables\Columns\TextColumn::make('payment_type')
                    ->label('Bakiye Ay')
                    ->formatStateUsing(fn($state) => $state . ' Ay')
                    ->alignEnd()
                    ->sortable()
                    ->size('sm'),
                Tables\Columns\TextColumn::make('total_purchase_price')
                    ->label('Toplam Alış Fiyatı')
                    ->getStateUsing(function ($record) {
                        $orderItemIds = $record->order->orderItems()->pluck('id')->toArray();
                        return \App\Models\ProductStock::whereIn('order_id', $orderItemIds)
                            ->sum('purchase_price');
                    })
                    ->formatStateUsing(fn($state) => number_format($state, 2, ',', '.'))
                    ->suffix('₺')
                    ->alignEnd()
                    ->size('sm'),
                Tables\Columns\TextColumn::make('overdue_rent_total')
                    ->label('Toplanacak Kira')
                    ->formatStateUsing(fn($state) => number_format($state, 2, ',', '.'))
                    ->suffix('₺')
                    ->sortable()
                    ->alignEnd()
                    ->size('sm'),
                // Tables\Columns\TextColumn::make('b2bOrder.surety_and_payment_guarantee')
                //     ->label('Kefalet/Garanti'),
                // Tables\Columns\TextColumn::make('b2bOrder.promissory_note')
                //     ->label('Senet'),
                // Tables\Columns\TextColumn::make('b2bOrder.letter_of_guarantee_is_exist')
                //     ->label('Teminat Mek. Durumu'),
                // Tables\Columns\TextColumn::make('b2bOrder.letter_of_guarantee_expiry_date')
                //     ->label('Teminat Mek. Bitiş Tarihi')
                //     ->date(),
                // Tables\Columns\TextColumn::make('b2bOrder.assignment_status')
                //     ->label('Temlik Durumu'),
                // Tables\Columns\TextColumn::make('b2bOrder.assignment_term')
                //     ->label('Temlik Süresi')
                //     ->sortable(),
                // Tables\Columns\TextColumn::make('b2bOrder.remaining_assignment_term')
                //     ->label('Kalan Temlik Süresi')
                //     ->sortable(),
                // Tables\Columns\TextColumn::make('b2bOrder.credit_type')
                //     ->label('Kredi Türü'),
                // Tables\Columns\TextColumn::make('b2bOrder.financing_institution')
                //     ->label('Finansman Kuruluşu'),
                // Tables\Columns\TextColumn::make('b2bOrder.subscription_status')
                //     ->label('Abonelik Durumu'),
                // Tables\Columns\TextColumn::make('b2bOrder.authorized_signatory_list')
                //     ->label('İmza Sirkuleri Durumu'),
                // Tables\Columns\TextColumn::make('created_at')
                //     ->label('Oluşturulma Tarihi')
                //     ->dateTime()
                //     ->sortable(),
            ])
            ->filters([
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Sipariş Başlangıç Tarihi')
                            ->placeholder(fn($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Sipariş Bitiş Tarihi')
                            ->placeholder(fn($state): string => now()->format('M d, Y')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereHas('order', function ($query) use ($date) {
                                    $query->whereDate('created_at', '>=', $date);
                                }),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereHas('order', function ($query) use ($date) {
                                    $query->whereDate('created_at', '<=', $date);
                                }),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['created_from'] ?? null) {
                            $indicators['created_from'] = 'Şu tarihten itibaren ' . Carbon::parse($data['created_from'])->toFormattedDateString();
                        }
                        if ($data['created_until'] ?? null) {
                            $indicators['created_until'] = 'Şu tarihe kadar ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                        }

                        return $indicators;
                    }),
            ])
            ->bulkActions([
                ExportBulkAction::make()
                    ->exports([
                        B2BOrderExport::make()
                            ->withFilename(date('Y-m-d') . '-b2b-siparisler-export')
                    ])
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            OrderItemsRelationManager::class,
            OrderTransactionRelationManager::class,
            NotesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListB2BOrders::route('/'),
            'create' => Pages\CreateB2BOrder::route('/create'),
            'edit' => Pages\EditB2BOrder::route('/{record}/edit'),
            'view' => Pages\ViewB2BOrder::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with('order', 'order.user')
            ->whereHas('order', function ($query) {
                $query->whereIn('status', [OrderRenting::class, OrderShipped::class, OrderApproved::class]);
            });
    }

    public static function canViewAny(): bool
    {
        return auth()->user() && auth()->user()->hasRole('Kurumsal Temsilci');
    }

    public static function canCreate(): bool
    {
        return auth()->user() && auth()->user()->hasRole('Kurumsal Temsilci');
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user() && auth()->user()->hasRole('Kurumsal Temsilci');
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user() && auth()->user()->hasRole('Kurumsal Temsilci');
    }
}
