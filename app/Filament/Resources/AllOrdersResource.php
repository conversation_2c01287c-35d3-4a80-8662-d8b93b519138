<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AllOrdersResource\Pages;
use App\Filament\Resources\AllOrdersResource\RelationManagers;
use App\Models\AllOrders;
use App\Models\HopiCampaign;
use App\Models\Order;
use App\Models\User;
use App\States\Order\Investigation;
use App\States\Order\OrderApproved;
use App\States\Order\OrderAtLegalPursuit;
use App\States\Order\OrderCancelled;
use App\States\Order\OrderCompleted;
use App\States\Order\OrderDenied;
use App\States\Order\OrderDocumentWaiting;
use App\States\Order\OrderEvaluation;
use App\States\Order\OrderPaused;
use App\States\Order\OrderProductNotSuitable;
use App\States\Order\OrderReceived;
use App\States\Order\OrderRefunded;
use App\States\Order\OrderRenting;
use App\States\Order\OrderShipped;
use App\States\Order\OrderUserCantBeReached;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Livewire\Component;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class AllOrdersResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static ?string $navigationLabel = 'Tüm Siparişler';
    protected static ?string $label = 'Sipariş';
    protected static ?string $pluralLabel = 'Siparişler';
    protected static ?string $navigationGroup = 'Sipariş Yönetimi';
    protected static ?string $slug = 'all-orders';
    protected static ?string $recordTitleAttribute = 'order_number';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Sipariş Bilgileri')
                    ->schema(static::getOrderUserDetails())
                    ->columns(4)
                    ->columnSpan(['lg' => fn(?Order $record) => $record === null ? 3 : 2]),
            ]);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['wp_order_id', 'order_number', 'contract_code'];
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->size('sm')
                    ->label('ID'),
                Tables\Columns\TextColumn::make('user.full_name')
                    ->searchable(['first_name', 'last_name'], isIndividual: true)
                    ->formatStateUsing(fn(string $state): string => Str::title($state))
                    ->wrap()
                    ->size('sm')
                    ->label('Kullanıcı'),
                Tables\Columns\TextColumn::make('order_number')
                    ->searchable(isIndividual: true)
                    ->size('sm')
                    ->fontFamily('mono')
                    ->alignEnd()
                    ->label('Sipariş Numarası'),
                Tables\Columns\TextColumn::make('total')
                    ->label('Kira Bedeli')
                    ->formatStateUsing(fn(string $state): string => number_format($state, 2, ',', '.') . ' ₺')
                    ->fontFamily('mono')
                    ->alignEnd()
                    ->size('sm'),

                Tables\Columns\TextColumn::make('orderProductList')
                    //                    ->searchable(query: function (Builder $query, string $search): Builder {
                    //                        return $query
                    //                            ->with('product.product')
                    //                            ->where('last_name', 'like', "%{$search}%");
                    //                    })
                    ->label('Ürün Adı')
                    ->size('sm')
                    ->html(),

                Tables\Columns\TextColumn::make('plan')
                    ->getStateUsing(function (Order $record): null|string {
                        return $record->orderItems->map(fn($item) => $item->planObj->name)->implode('<br>');
                    })
                    ->alignEnd()
                    ->label('Kiralama Süresi')
                    ->html()
                    ->size('sm'),

                Tables\Columns\TextColumn::make('kiralama')
                    ->getStateUsing(function (Order $record): null|string {
                        return $record->orderItems->map(fn($item) => $item->shipmentStatus)->implode('<br>');
                    })
                    ->alignEnd()
                    ->label('Kargolama')
                    ->html()
                    ->size('sm'),

                Tables\Columns\TextColumn::make('status_text')
                    ->label('Statü')
                    ->wrap()
                    ->size('sm'),

                //                Tables\Columns\TextColumn::make('ip_address')
                //                    ->label('IP'),
                //                Tables\Columns\TextColumn::make('coupon_id')
                //                    ->label('Kupon'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Oluşturulma Tarihi')
                    ->dateTime()
                    ->alignEnd()
                    ->size('sm'),
            ])
            ->filters([
                //                Tables\Filters\TrashedFilter::make(),
                Tables\Filters\Filter::make('status')
                    ->form([
                        Forms\Components\Select::make('status')
                            ->label('Sipariş Statüsü')
                            ->options([
                                OrderReceived::class => 'Yeni',
                                OrderEvaluation::class => 'Değerlendiriliyor',
                                OrderDocumentWaiting::class => 'Belge Bekleniyor',
                                OrderUserCantBeReached::class => 'Ulaşılamadı',
                                Investigation::class => 'Şüpheli İşlem',
                                OrderApproved::class => 'Onaylandı',
                                OrderShipped::class => 'Kargolandı',
                                OrderDenied::class => 'Reddedildi',
                                OrderCancelled::class => 'İptal',
                                OrderCompleted::class => 'Tamamlandı',
                                OrderRefunded::class => 'İade',
                                OrderRenting::class => 'Kiralama Devam Ediyor',
                                OrderAtLegalPursuit::class => 'Yasal Takip Siparişi',
                                OrderPaused::class => 'Beklemede',
                                OrderProductNotSuitable::class => 'Ürün Tedarik Edilemedi',
                            ]),
                        //->placeholder(fn($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['status'],
                                fn(Builder $query, $statu): Builder => $query->where('status', $statu),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['status'] ?? null) {
                            $indicators['status'] = 'Sipariş Durumu ' . get_order_status_text($data['status']) . ' olarak filtrelenmiştir.';
                        }

                        return $indicators;
                    }),
                Tables\Filters\Filter::make('corpetate')
                    ->form([
                        Forms\Components\Checkbox::make('is_corporate')
                            ->label('Sadece Kurumsal Sipariş'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['is_corporate'],
                                fn(Builder $query, $state): Builder => $query->whereHas('user', function (Builder $query) use ($state) {
                                    $query->where('is_company', $state);
                                }),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['is_corporate']) {
                            $indicators['corpetate'] = 'Sadace kurumsal siparişler gösterilmektedir.';
                        }

                        return $indicators;
                    }),
                Tables\Filters\Filter::make('kiralamotor')
                    ->form([
                        Forms\Components\Checkbox::make('is_kiralamotor')
                            ->label('Sadece Kiralamotor Siparişleri'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['is_kiralamotor'],
                                function (Builder $query) {
                                    // Belirtilen product_id'lere sahip varyantların ID'lerini al
                                    $productIds = [2182, 2178, 2063, 2061, 2060, 2181, 2180, 2179, 3473];
                                    $variantIds = \App\Models\Lunar\ProductVariant::whereIn('product_id', $productIds)
                                        ->pluck('id')
                                        ->toArray();

                                    return $query->whereHas('orderItems', function (Builder $query) use ($variantIds) {
                                        $query->whereIn('product_id', $variantIds);
                                    });
                                }
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['is_kiralamotor']) {
                            $indicators['kiralamotor'] = 'Sadece kiralamotor siparişleri gösterilmektedir.';
                        }

                        return $indicators;
                    }),
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Sipariş Başlangıç Tarihi')
                            ->placeholder(fn($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Sipariş Bitiş Tarihi')
                            ->placeholder(fn($state): string => now()->format('M d, Y')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['created_from'] ?? null) {
                            $indicators['created_from'] = 'Şu tarihten itibaren ' . Carbon::parse($data['created_from'])->toFormattedDateString();
                        }
                        if ($data['created_until'] ?? null) {
                            $indicators['created_until'] = 'Şu tarihe kadar ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                        }

                        return $indicators;
                    }),

                Tables\Filters\Filter::make('affiliate')
                    ->form([
                        Forms\Components\Select::make('affiliate')
                            ->label('Affiliate')
                            ->multiple()
                            ->options([
                                'teknosa' => 'Teknosa',
                                'abonesepeti' => 'Abone Sepeti',
                                'aktifbank' => 'N Kolay',
                                'migros' => 'Migros',
                            ]),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['affiliate'],
                                fn(Builder $query, $state): Builder => $query->whereIn('affiliate', $state),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['affiliate'] ?? null) {
                            $indicators['affiliate'] = 'Affiliate ' . implode(', ', $data['affiliate']) . ' olarak filtrelenmiştir.';
                        }
                        return $indicators;
                    }),

            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                ExportBulkAction::make(),
            ])
            ->defaultSort('id', 'desc');
    }

    public static function getOrderUserDetails(): array
    {
        return [
            Forms\Components\Select::make('user_id')
                ->label('Müşteri Adı')
                ->relationship('user', 'first_name')
                ->getOptionLabelFromRecordUsing(fn(User $record) => "{$record->first_name} {$record->last_name}")
                ->required()
                ->suffixAction(
                    fn(?string $state): Action => Action::make('visit')
                        ->icon('heroicon-s-external-link')
                        ->url(
                            "/admin/users/{$state}",
                            shouldOpenInNewTab: true,
                        )
                )->searchable(),

            Forms\Components\TextInput::make('order_number')
                ->label('Sipariş Numarası')
                ->required()
                ->disabled()
                ->maxLength(255)
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateAllOrders)),

            Forms\Components\Placeholder::make('created_at')
                ->label('Sipaariş Tarihi')
                ->content(fn(?Order $record): ?string => $record?->created_at->timezone('Europe/Istanbul')->format('d.m.Y H:i:s'))
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateAllOrders)),

            Forms\Components\Select::make('status')
                ->label('Sipariş Durumu')
                // ->disabled()
                ->options([
                    OrderReceived::class => 'Yeni',
                    OrderEvaluation::class => 'Değerlendiriliyor',
                    OrderDocumentWaiting::class => 'Belge Bekleniyor',
                    //                    OrderStudentDocumentWaiting::class => 'Öğrenci Belgesi Bekleniyor',
                    OrderUserCantBeReached::class => 'Ulaşılamadı',
                    OrderApproved::class => 'Onaylandı',
                    Investigation::class => 'Şüpheli İşlem',
                    //                    OrderShipped::class => 'Kargolandı',
                    OrderDenied::class => 'Reddedildi',
                    OrderCancelled::class => 'İptal',
                    OrderCompleted::class => 'Tamamlandı',
                    //                    OrderRefunded::class => 'İade',
                    OrderRenting::class => 'Kiralama Devam Ediyor',
                    OrderAtLegalPursuit::class => 'Yasal Takip Siparişi',
                    OrderPaused::class => 'Beklemede',
                    OrderProductNotSuitable::class => 'Ürün Tedarik Edilemedi',
                ])
                ->required()
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateAllOrders)),

            Forms\Components\Placeholder::make('email')
                ->content(fn(?Order $record): ?string => $record?->user->email)
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateAllOrders)),

            Forms\Components\Placeholder::make('phone')
                ->label('Telefon')
                ->content(fn(?Order $record): ?HtmlString => new HtmlString('<a href="https://wa.me/' . $record?->userPhone . '" target="_blank">' . $record?->userPhone . '</a>'))
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateAllOrders)),

            Forms\Components\Placeholder::make('birt_date')
                ->label('Doğum Tarihi')
                ->content(fn(?Order $record): ?string => $record?->user->date_of_birth->format('d.m.Y'))
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateAllOrders)),

            Forms\Components\Placeholder::make('tckn')
                ->label('TC Kimlik No')
                ->content(fn(?Order $record): ?string => $record?->user->tckn)
                ->visible(fn(Component $livewire): bool => !($livewire instanceof Pages\CreateAllOrders)),

            Forms\Components\Placeholder::make('address')
                ->label('Adres')
                ->content(fn(?Order $record, Component $livewire): ?string => optional(json_decode($record?->shipping_address))->address)
                ->hidden(fn(Component $livewire): bool => ($livewire instanceof Pages\CreateAllOrders || $livewire instanceof Pages\EditAllOrders)),

            Forms\Components\Select::make('billing_address_id')
                ->label('Fatura Adresi')
                ->options(function (?Order $record) {
                    return $record->user->addresses->pluck('addressWithBillType', 'id');
                })
                ->required()
                ->visible(fn(Component $livewire): bool => ($livewire instanceof Pages\EditAllOrders)),

            Forms\Components\Select::make('shipping_address_id')
                ->label('Sevk Adresi')
                ->options(function (?Order $record) {
                    return $record->user->addresses->pluck('addressWithBillType', 'id');
                })
                ->required()
                ->visible(fn(Component $livewire): bool => ($livewire instanceof Pages\EditAllOrders)),

            Forms\Components\Placeholder::make('total')
                ->label('Kira Bedeli'),
            //->visible(fn (Component $livewire): bool => ! ($livewire instanceof Pages\CreateAllOrders) )

            Forms\Components\Placeholder::make('cc')
                ->label('Kredi Kartı')
                ->content(fn(?Order $record): ?string => $record->paymentTransaction?->creditCard()->withTrashed()->first()->bin_number . '****' . $record->paymentTransaction?->creditCard()->withTrashed()->first()->number . ' / ' . $record->paymentTransaction?->creditCard()->withTrashed()->first()->holder),

            Forms\Components\Placeholder::make('wp_order_id')
                ->label('WP Sipariş No')
                //    ->content(fn(?Order $record): ?HtmlString => new HtmlString('<a href="https://wp.kiralabunu.com/wp-admin/post.php?post=' . $record?->wp_order_id . '&action=edit" target="_blank">' . $record?->wp_order_id . '</a>')),
                ->content(fn(?Order $record): ?string => $record?->wp_order_id),

            Forms\Components\Placeholder::make('user')
                ->label('Müşteri Adı')
                ->content(fn(?Order $record): ?string => $record->user->full_name_db),

            Forms\Components\Placeholder::make('coupon_id')
                ->label('Kupon Kodu')
                ->content(fn(?Order $record): ?string => $record?->coupon->code)
                ->visible(fn(?Order $record): ?bool => $record->coupon_id),

            Forms\Components\Placeholder::make('hopi')
                ->label('Hopi Siparişi')
                ->lazy()
                ->visible(fn(?Order $record): ?bool => Order\Order::find($record->id)->meta()->where('key', 'hopi_selected_campaign')->exists())
                ->content(fn(?Order $record): ?string => HopiCampaign::where('code', Order\Order::find($record->id)->meta()->where('key', 'hopi_selected_campaign')->first()->value)->first()?->name),

            Forms\Components\TextInput::make('contract_code')
                ->label('Sözleşme Kodu')
                ->default(fn(?Order $record): ?string => $record?->contract_code)
                ->visible(fn(?Order $record, Component $livewire): ?bool => ($livewire instanceof Pages\CreateAllOrders || $livewire instanceof Pages\EditAllOrders) || ($record->contract_code)),

            Forms\Components\DatePicker::make('contract_date')
                ->label('Sözleşme Tarihi')
                ->default(fn(?Order $record): ?string => $record?->contract_date)
                ->visible(fn(?Order $record, Component $livewire): ?bool => ($livewire instanceof Pages\CreateAllOrders || $livewire instanceof Pages\EditAllOrders) || ($record->contract_date)),

            Forms\Components\TextInput::make('affiliate')
                ->label('Affiliate')
                ->default(fn(?Order $record): ?string => $record?->affiliate)
                ->visible(fn(?Order $record, Component $livewire): ?bool => ($livewire instanceof Pages\CreateAllOrders || $livewire instanceof Pages\EditAllOrders) || ($record->affiliate)),

            Forms\Components\Placeholder::make('will_ownership_transfer')
                ->label('Vadeli/Operasyonel')
                ->content(fn(?Order $record): ?string => $record?->will_ownership_transfer ? 'Vadeli' : 'Operasyonel'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\OrderItemsRelationManager::class,
            RelationManagers\OrderTransactionRelationManager::class,
            RelationManagers\PaymentTransactionRelationManager::class,
            //            RelationManagers\NotesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAllOrders::route('/'),
            'create' => Pages\CreateAllOrders::route('/create'),
            'view' => Pages\ViewAllOrders::route('/{record}'),
            'edit' => Pages\EditAllOrders::route('/{record}/edit'),
        ];
    }

    //    public static function getWidgets(): array
    //    {
    //        return [
    ////            OperationalNotes::class,
    ////            Notes::class,
    ////            OrderInventory::class,
    //        ];
    //    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([])
            ->with('orderItems', 'orderItems.planObj', 'orderItems.product', 'orderItems.product.product');
    }
}
