<?php

namespace App\Filament\Resources\OrderResource\RelationManagers;

use App\Models\Lunar\SubscriptionMonths;
use App\Models\Order\OrderItem;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\Position;
use Filament\Tables\Columns\IconColumn;
use App\Models\B2BProduct;

class OrderItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'orderItems';
    protected static ?string $recordTitleAttribute = 'id';
    protected static ?string $navigationLabel = 'Sipariş Ürünleri';
    protected static ?string $label = 'Sipariş Ürünleri';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('product_id')
                    ->label('Ürün')
                    ->options(fn() => \App\Models\Lunar\ProductVariant::whereHas('product', function ($query) {
                        return $query->where('status', 'published')->where('is_archived', false);
                    })
                        // varyant purchasable always yada in_stock ise de stock > 0 ise 
                        ->where(fn($query) => $query->where('purchasable', 'always')->orWhere(fn($query) => $query->where('purchasable', 'in_stock')->where('stock', '>', 0)))
                        ->pluck('name', 'id'))
                    // ->with(['product', 'options'])->get()->mapWithKeys(function ($x) {
                    //     return [$x->id => $x->product?->getName . ' ' . $x->options->map(fn($y) => json_decode($y->productOptionValue->name)->tr ?? json_decode($y->productOptionValue->name)->en)->implode(', ')];
                    // }))
                    ->searchable()
                    ->required(),

                Forms\Components\Hidden::make('product_type')
                    ->default('App\Models\Lunar\ProductVariant'),

                Forms\Components\TextInput::make('quantity')
                    ->label('Adet')
                    ->integer()
                    ->default(1)
                    ->required(),

                Forms\Components\TextInput::make('total')
                    ->label('Fiyat')
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        $set('sub_total', $state / 118 * 100);
                        $set('tax_amount', $state / 118 * 18);
                        $set('price', $state);
                    }),

                Forms\Components\Hidden::make('sub_total')
                    ->default(1),

                Forms\Components\Hidden::make('tax_amount')
                    ->default(1),

                Forms\Components\Hidden::make('price')
                    ->default(1),

                Forms\Components\Select::make('plan')
                    ->label('Kiralama Süresi')
                    ->options(SubscriptionMonths::get()->sortBy('value')->pluck('name', 'id'))
                    ->required(),

                Forms\Components\Toggle::make('has_insurance')
                    ->label('Sigorta var mı?')
                    ->default(false),

                Forms\Components\TextInput::make('insurance_price')
                    ->label('Sigorta Fiyatı'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')->visible(fn() => auth()->user()->hasRole('Super Admin')),
                Tables\Columns\ImageColumn::make('product.product.getFirstPhoto')
                    ->alignCenter()
                    ->height(fn(OrderItem $record) => $record->product_type == B2BProduct::class ? 44 : 120)
                    ->width(fn(OrderItem $record) => $record->product_type == B2BProduct::class ? 120 : 120)
                    ->label('Ürün Resmi'),
                Tables\Columns\TextColumn::make('product.purchasable')->label('Stok Satılabilirlik')->visible(fn() => auth()->user()->hasRole('Super Admin')),
                Tables\Columns\TextColumn::make('product.id')->label('VID')->visible(fn() => auth()->user()->hasRole('Super Admin')),
                Tables\Columns\TextColumn::make('product.product.nameWithSource')->label('Ürün Adı'),
                Tables\Columns\TextColumn::make('product.getVaariant')->label('Varyant'),
                Tables\Columns\TextColumn::make('quantity')->label('Adet')->alignEnd(),
                // textcolumn content add ₺ to end of price
                Tables\Columns\TextColumn::make('total')->label('Tutar')
                    ->alignEnd()
                    ->formatStateUsing(fn(string $state): string => $state . ' ₺'),
                //Tables\Columns\TextColumn::make('discount_amount')->label('İndirim Tutarı'),
                Tables\Columns\TextColumn::make('plan')->label('Kiralama Süresi')
                    ->alignEnd()
                    ->formatStateUsing(fn(string $state): string => SubscriptionMonths::getSelectedMonth($state) . ' Ay'),
                IconColumn::make('has_insurance')
                    ->label('Sigorta')
                    ->alignCenter()
                    ->boolean(),
                Tables\Columns\TextColumn::make('insurance_price')->label('Sigorta Tutarı')
                    ->alignEnd()
                    ->formatStateUsing(fn(string $state): string => $state . ' ₺'),
                Tables\Columns\ToggleColumn::make('payment_control')
                    ->label('Ödeme Kontrolü')
                    ->alignCenter()
                    ->toggleable(),
                Tables\Columns\ToggleColumn::make('findex_control')
                    ->label('Findeks Kontrolü')
                    ->alignCenter()
                    ->toggleable(),
                //                Tables\Columns\ToggleColumn::make('is_user_suitable_control')
                //                    ->label('Tedarik Edebilir')
                //                    ->sortable()
                //                    ->toggleable(),

                IconColumn::make('is_user_suitable_control')
                    ->label('Tedarik Edebilir?')
                    ->alignCenter()
                    ->boolean(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                //Tables\Actions\EditAction::make(),
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                //Tables\Actions\EditAction::make(),
                //                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public function getTableActions(): array
    {
        return [

            Tables\Actions\ActionGroup::make([

                Action::make('Tedarik Edebilir Kontrolü')
                    ->color('danger')
                    //                    ->disabled(fn(OrderTransaction $record) => $record->payment_status_id == 1)
                    ->mountUsing(fn(Forms\ComponentContainer $form, OrderItem $record) => $form->fill([
                        'is_user_suitable_control' => (int)$record->is_user_suitable_control,
                    ]))
                    ->action(fn(OrderItem $record, array $data) => $record->saveIsUserSuitableControl($data['is_user_suitable_control']))
                    ->form(
                        [
                            Forms\Components\Toggle::make('is_user_suitable_control')
                                ->label('Sipariş onayı verilmesine emin misiniz?')
                                ->inline(),
                        ]
                    )->requiresConfirmation(),

                Action::make('Ürün Fiyat/Adet Güncelle')
                    ->label(fn(OrderItem $record): ?string => 'Fiyat/Adet Güncelle')
                    ->color('danger')
                    ->icon('heroicon-o-cash')
                    ->mountUsing(fn(Forms\ComponentContainer $form, OrderItem $record) => $form->fill([
                        'price' => $record->price,
                        'quantity' => $record->quantity,
                    ]))
                    ->action(function (OrderItem $record, array $data): OrderItem {
                        return $record->updateAmountAndUnitPrice($data);
                    })
                    ->form(
                        [
                            Forms\Components\TextInput::make('price')
                                ->label('Ürün Birim Fiyatı')
                                ->numeric()
                                ->mask(
                                    fn(Forms\Components\TextInput\Mask $mask) => $mask
                                        ->numeric()
                                        ->decimalPlaces(2)
                                        ->decimalSeparator('.')
                                )
                                ->required()
                                ->minValue(1),

                            Forms\Components\TextInput::make('quantity')
                                ->label('Ürün Adedi')
                                ->numeric()
                                ->mask(
                                    fn(Forms\Components\TextInput\Mask $mask) => $mask
                                        ->numeric()
                                        ->decimalPlaces(0)
                                        ->decimalSeparator('.')
                                )
                                ->required()
                                ->minValue(1),
                        ]
                    )
                    ->requiresConfirmation(),

                Action::make('Ürün Varyant Değiştirme')
                    ->label(fn(OrderItem $record): ?string => 'Varyant Değiştirme')
                    ->color('danger')
                    ->icon('heroicon-o-switch-horizontal')
                    ->mountUsing(fn(Forms\ComponentContainer $form, OrderItem $record) => $form->fill([
                        'main_product_id' => $record->product->product->id,
                    ]))
                    ->action(function (OrderItem $record, array $data): OrderItem {
                        return $record->changeProductWithVariant($data);
                    })
                    ->form(
                        [
                            Forms\Components\Select::make('product_id')
                                ->label('Varyant')
                                ->options(fn(callable $get) => \App\Models\Lunar\ProductVariant::where('product_id', $get('main_product_id'))->get()->pluck('name', 'id'))
                                ->searchable()
                                ->required(),
                        ]
                    )
                    ->requiresConfirmation(),
            ]),
        ];
    }

    protected function getTableActionsPosition(): ?string
    {
        return Position::BeforeCells;
    }
}
