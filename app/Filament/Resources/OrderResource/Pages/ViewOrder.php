<?php

namespace App\Filament\Resources\OrderResource\Pages;

use App\Filament\Resources\OrderResource;
use App\Models\Document;
use App\Models\Meta;
use App\States\Order\OrderEvaluation;
use App\States\Order\OrderReceived;
use App\States\Order\OrderUserCantBeReached;
use App\States\Order\OrderDenied;
use App\States\Order\OrderApproved;
use App\States\Order\Investigation;
use App\States\Order\OrderCancelled;
use App\States\Order\OrderProductNotSuitable;
use Filament\Forms;
use Filament\Pages\Actions;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ViewRecord;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use App\States\Order\OrderDocumentWaiting;

class ViewOrder extends ViewRecord
{
    protected static string $resource = OrderResource::class;

    protected function getActions(): array
    {
        return [

            Action::make('back')
                ->label('GERİ DÖN')
                ->url(route('filament.resources.orders.index')),

            Action::make('Belge Talebi')
                ->label('Belge Talebi')
                ->visible(fn() => $this->record->status == OrderEvaluation::class)
                ->color('danger')
                ->action(fn(array $data) => $this->record->setStatusAsDocumentWaitingAndSendEmail($data['document']))
                ->form(
                    [
                        Forms\Components\CheckboxList::make('document')
                            ->label('Belge Listesi')
                            ->options(Document::pluck('name', 'id'))
                            ->required()
                    ]
                )
                ->requiresConfirmation()
                ->modalSubheading('Müşteriye bu listede seçilen belgelerin beklendiğine dair email gönderilecektir'),

            // change status Evaluation
            Action::make('change-status-evaluation')
                ->label('Değerlendiriliyor')
                ->visible(fn() => $this->record->status == OrderReceived::class)
                ->action(fn() => $this->record->updateStatusToEvaluation($this->record))
                ->after(fn() => $this->redirect(route('filament.resources.orders.view', $this->record->id)))
                ->color('success')
                ->requiresConfirmation()
                ->modalSubheading('Bu siparişi değerlendirmeye almak istediğinizden emin misiniz?'),

            Action::make('change-status-user-cant-be-reached')
                ->label('Ulaşılamadı')
                ->visible(fn() => in_array($this->record->status, [OrderReceived::class]))
                ->action(fn() => $this->record->updateStatusToUserCantBeReached($this->record))
                ->after(fn() => $this->redirect(route('filament.resources.all-orders.view', $this->record->id)))
                ->color('danger')
                ->requiresConfirmation()
                ->modalSubheading('Bu siparişi ulaşılamadı olarak işaretlemek istediğinizden emin misiniz?'),

            Action::make('change-status-denied')
                ->label('Reddedildi')
                ->visible(fn() => $this->record->status == OrderEvaluation::class)
                ->action(fn() => $this->record->updateStatusToDenied($this->record))
                ->after(fn() => $this->redirect(route('filament.resources.all-orders.view', $this->record->id)))
                ->color('danger')
                ->requiresConfirmation()
                ->modalSubheading('Bu siparişi reddetmek istediğinizden emin misiniz?'),

            Action::make('change-status-approved')
                ->label('Onaylandı')
                ->visible(fn() => in_array($this->record->status, [OrderEvaluation::class, OrderProductNotSuitable::class]))
                ->action(fn() => $this->record->updateStatusToApproved($this->record))
                ->after(fn() => $this->redirect(route('filament.resources.orders.view', $this->record->id)))
                ->color('success')
                ->requiresConfirmation()
                ->modalSubheading('Bu siparişi onaylamak istediğinizden emin misiniz?'),

            Action::make('change-status-investigation')
                ->label('Şüpheli İşlem')
                ->visible(fn() => $this->record->status == OrderEvaluation::class)
                ->action(fn() => $this->record->updateStatusToInvestigation($this->record))
                ->after(fn() => $this->redirect(route('filament.resources.orders.view', $this->record->id)))
                ->color('warning')
                ->requiresConfirmation()
                ->modalSubheading('Bu siparişi şüpheli işlem olarak işaretlemek istediğinizden emin misiniz?'),

            Action::make('change-status-cancelled')
                ->label('İptal Et')
                ->visible(fn() => in_array($this->record->status, [
                    OrderReceived::class,
                    OrderEvaluation::class,
                    OrderDocumentWaiting::class,
                    OrderProductNotSuitable::class
                ]))
                ->action(fn() => $this->record->updateStatusToCancelled($this->record))
                ->after(fn() => $this->redirect(route('filament.resources.all-orders.view', $this->record->id)))
                ->requiresConfirmation()
                ->modalSubheading('Bu siparişi iptal etmek istediğinizden emin misiniz?')
                ->modalButton('Evet, İptal Et')
                ->color('danger'),

            Actions\EditAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        if ($this->isToslaPayedOrder($this->record))
            Notification::make('test')
                ->title('DİKKAT: Bu Sipariş TOSLA ile ödenmiştir')
                ->warning()
                ->body("Siparişiniz TOSLA üzerinden ödenmiştir. Bu nedenle onay için sistemde kayıtlı kredi kartı varlığını kontrol etmelisiniz.")
                ->persistent()
                ->send();

        // if order_back_to_onboard_for_item meta exists, then we need to show the note
        if ($this->isOrderBackToOnboardForItem($this->record)) {
            // each for all order_back_to_onboard_for_item
            $orderItemsBackToOnboard = $this->record->meta()->where('key', 'order_back_to_onboard_for_item')->whereBetween('created_at', [now()->subDays(14), now()])->get();
            $orderItemsBackToOnboard->each(function ($item) {
                Notification::make('order_back_to_onboard_for_item_' . $item->value)
                    ->title('DİKKAT: Bu Sipariş Tedarik Edilemedi')
                    ->danger()
                    ->body("Siparişiniz Tedarik Edilemedi. Gönderilen not: " . Meta::where('metaable_id', $item->value)->where('metaable_type', \App\Models\OrderItems::class)->where('key', 'order_item_back_to_onboard_note')->first()->value)
                    ->persistent()
                    ->send();
            });
        }

        return $data;
    }

    private function isToslaPayedOrder(Model $record): bool
    {
        return Meta::where('metaable_id', $record->id)->where('metaable_type', \App\Models\Order\Order::class)->where('key', 'tosla_transaction_id')->count() > 0;
    }

    private function isOrderBackToOnboardForItem(Model $record): bool
    {
        return Meta::where('metaable_id', $record->id)->where('metaable_type', \App\Models\Order::class)->where('key', 'order_back_to_onboard_for_item')->count() > 0;
    }
}
