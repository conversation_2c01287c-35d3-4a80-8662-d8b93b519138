<?php

namespace App\Filament\Resources\OrderResource\Pages;

use App\Exceptions\Transaction\TransactionVoidException;
use App\Facades\IyzipayFacade;
use App\Filament\Resources\OrderResource;
use App\Jobs\HopiNotifyCheckoutRequest;
use App\Jobs\klavioApprovedOrder;
use App\Jobs\klavioRejectedOrder;
use App\Models\Cart\Cart;
use App\Models\Cart\CartItem;
use App\Models\Coupon;
use App\Models\Lunar\ProductVariant;
use App\Models\Lunar\SubscriptionMonths;
use App\Models\Meta;
use App\Models\Transaction;
use App\PaymentPlan;
use App\States\Order\Investigation;
use App\States\Order\OrderApproved;
use App\States\Order\OrderCancelled;
use App\States\Order\OrderDenied;
use App\States\Order\OrderEvaluation;
use App\States\Order\OrderUserCantBeReached;
use Exception;
use Filament\Notifications\Notification;
use Filament\Pages\Actions;
use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Spatie\ModelStates\Exceptions\TransitionNotAllowed;
use Spatie\ModelStates\Exceptions\TransitionNotFound;

class EditOrder extends EditRecord
{
    protected static string $resource = OrderResource::class;

    protected function getActions(): array
    {
        return [
            Action::make('back')
                ->label('GERİ DÖN')
                ->url(route('filament.resources.orders.view', $this->record->getKey())),
            Actions\ViewAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        if ($record->status != $data['status']) {
            $data = $this->transitionChangeCheck($record, $data);
        }

        if ($data['status'] == OrderApproved::class) {
            $data = $this->orderApprovedActions($record, $data);
            $this->restoreNonApprovedStockAction($record);
            Mail::queue(new \App\Mail\SuccessOrder($record->user, $record));
            klavioApprovedOrder::dispatch($record->user, $record);

            $hopiOrder = \App\Models\Order\Order::find($record->id);
            $birdId = $hopiOrder->meta()
                ->where("key", "hopi_bird_id")
                ->first();

            if ($birdId) {
                $requestedCoin = $hopiOrder->meta()
                    ->where("key", "hopi_requested_coin")
                    ->first();

                $selected_campaign = $hopiOrder->meta()
                    ->where("key", "hopi_selected_campaign")
                    ->first();

                HopiNotifyCheckoutRequest::dispatch((int)$birdId->value, $hopiOrder, $requestedCoin?->value ?? 0, $selected_campaign?->value ?? null);
                $hopiOrder->meta()->firstOrCreate(['key' => 'hopi_notify_checkout'], ['value' => true]);
            }
        }

        if ($data['status'] == OrderCancelled::class || $data['status'] == OrderDenied::class) {
            $this->restoreStockAction($record);
            $data = $this->orderCancelledActions($record, $data);
            klavioRejectedOrder::dispatch($record->user, $record);
            $this->hopiReturnTransaction($record);
        }

        if ($data['status'] == OrderDenied::class) {
            Mail::queue(new \App\Mail\SuccessRefund($record->user, $record));
        }

        if ($data['status'] == OrderCancelled::class) {
            Mail::queue(new \App\Mail\RentalCancellation($record->user, $record));
        }

        if ($data['status'] == OrderEvaluation::class) {
            Mail::queue(new \App\Mail\OnEvaluation($record->user, $record));
        }

        //        if ($data['status'] == OrderDocumentWaiting::class) {
        //            if ($record->coupon_id == 80383) {
        //                Mail::queue(new \App\Mail\StudentDocumentWaiting($record->user, $record));
        //            } else {
        //                Mail::queue(new \App\Mail\PendingDocument($record->user, $record));
        //            }
        //        }

        if ($data['status'] == OrderUserCantBeReached::class) {
            $data = $this->orderCancelledActions($record, $data);
            $this->hopiReturnTransaction($record);
            Mail::queue(new \App\Mail\OrderStatus\UserCantBeReached($record->user, $record));
        }

        //        if ($data['total'] != $record->total) {
        //            $record->orderTransaction()->where('payment_status_id', 2)->update(['amount' => $data['total']]);
        //        }

        $record->update($data);

        return $record;
    }

    protected function getRedirectUrl(): string
    {
        // Sipariş onayladığında sipariş listesine yönlendir.
        if ($this->record->status == OrderApproved::class || $this->record->status == OrderDenied::class || $this->record->status == OrderCancelled::class || $this->record->status == Investigation::class || $this->record->status == OrderUserCantBeReached::class) {
            return $this->getResource()::getUrl('index');
        }

        return $this->getResource()::getUrl('view', $this->record->getKey());
    }

    private function orderApprovedActions(Model $record, array $data)
    {
        $data['finance_approved_at'] = $data['finance_approved_at'] ?? now();

        $this->setContractExpiredAt($record);

        // Siparişin tüm ürünleri için ödeme kontrolü yapılmışsa siparişin ödeme kontrolü tamamlanmış sayılır.
        $it = $record->orderItems->filter(function ($item) {
            return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1;
        })->count();

        if ($it != $record->orderItems->count()) {

            $amountBeforeTheReevaluation = $record->orderTransactions->first()->amount;
            $total = $record->orderItems->filter(function ($item) {
                return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1;
            })->sum('total');

            $insuranceTotal = $record->orderItems->filter(function ($item) {
                return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1;
            })->sum('insurance_price');

            $discount = 0;
            if ($record->coupon_id)
                $discount = $this->calculateCouponDiscount($record);

            try {
                DB::transaction(function () use ($record, $total, &$data, $discount) {
                    $enFazlaTaksideSahipUrununAyi = SubscriptionMonths::find($record->orderItems->where('payment_control', true)->where('findex_control', true)->where('is_user_suitable_control', true)->max('plan'))->value;
                    $planBaslangicTarihi = $record->created_at;
                    $cardId = $record->orderTransactions->first()->card_id;

                    // Calculate new prices
                    $data['sub_total'] = $total / 120 * 100;
                    $data['tax_amount'] = $total / 120 * 20;
                    $data['total'] = $total;

                    $record->orderTransactions()->delete();

                    $pp = new PaymentPlan($record->created_at, $cardId, $record->id);
                    $pp->add($total, $enFazlaTaksideSahipUrununAyi);
                    $pp->save();

                    // if coupon is used, update first payment plan for the coupon
                    if ($record->coupon_id) {
                        $ot = $record->orderTransactions()->whereNull('deleted_at')->first();
                        for ($i = 1; $i <= SubscriptionMonths::getSelectedMonth($record->coupon->effected_months); $i++) {
                            // Check if there is not transaction record for the month
                            if ($ot) {
                                $ot->amount = $total + $discount;
                                $ot->note = 'Kupon indirimi ' . $ot->id . ' numaralı ödeme planına eklendi. Toplam indirim: ' . $discount . ' TL';
                                $ot->save();
                                $ot = $ot->nextTransection();
                            }
                        }
                    }
                });
            } catch (TransitionNotAllowed $e) {
                logger($e);
                Notification::make()
                    ->title($e->getMessage())
                    ->danger()
                    ->send();

                $this->halt();

                return $record->fresh();
            } catch (TransitionNotFound $e) {
                logger($e);
                Notification::make()
                    ->title($e->getMessage())
                    ->danger()
                    ->send();

                $this->halt();

                return $record->fresh();
            }

            // Aradaki farkın IYZICO ya aktarımı, üst db transection içeren bloktan çıkarıldı çünkü eğer iyzico dan iade onaylanmaz ise süreç sorun yaşamamalı dendi
            try {
                $t = Transaction::where('order_id', $record->id)->whereNull('refunds')->first();
                $refund = IyzipayFacade::partitialRefund($t, $amountBeforeTheReevaluation - ($total + $discount + $insuranceTotal));
            } catch (TransactionVoidException $e) {
                logger($e);
                Notification::make()
                    ->title('Iyzico iade hatası => ' . $e->getMessage())
                    ->danger()
                    ->send();

                Notification::make()
                    ->title('Yeni ödeme planı oluşturulmuştur.')
                    ->success()
                    ->duration(10000)
                    ->send();

                return $data;
            }

            Notification::make()
                ->title('Kısmi ödeme yapılmıştır. Yeni ödeme planı oluşturulmuş ve iade gerçekleştirilmiştir')
                ->success()
                ->duration(10000)
                ->send();
        }

        return $data;
    }

    private function orderCancelledActions(Model $record, array $data)
    {
        // early return if order is payed with tosla
        if ($this->isToslaPayedOrder($record)) {
            try {
                $this->cancelToslaPayedOrder($record);
                $record->orderTransactions()->delete(); // İptal edilen siparişin tüm ödemeleri silinir.
            } catch (Exception $e) {
                logger()->error($e);
                Notification::make()
                    ->title('Tosla iade hatası => ' . $e->getMessage())
                    ->danger()
                    ->send();
            }

            return $data; // this is important for early return
        }

        // standard payment with iyzico
        $record->orderTransactions()->delete(); // İptal edilen siparişin tüm ödemeleri silinir.
        try {
            $t = Transaction::where('order_id', $record->id)->whereNull('refunds')->first();
            if ($t)
                $refund = IyzipayFacade::partitialRefund($t, $t->amount); // siparişin ilk kira tahsilatı iade edilecek
        } catch (TransactionVoidException $e) {
            logger($e);
            Notification::make()
                ->title('Iyzico iade hatası => ' . $e->getMessage())
                ->danger()
                ->send();
        }

        return $data;
    }

    private function transitionChangeCheck(Model $record, array $data)
    {
        try {
            $record->status->transitionTo($data['status']);
        } catch (TransitionNotFound $e) {
            Notification::make()
                ->title('Sipariş durumu bu şekilde değiştirilemez.')
                ->danger()
                ->send();

            $this->halt();

            return $record->fresh();
        }

        return $data;
    }

    private function setContractExpiredAt(Model $record)
    {
        $record->orderItems->filter(function ($item) {
            return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1;
        })->map(function ($item) use ($record) {
            $item->contract_expired_at = $record->created_at->addMonths($item->planObj->value);
            $item->legal_contract_expired_at = $item->contract_expired_at;
            $item->save();
        });
    }

    private function restoreStockAction(Model $record)
    {
        // On board üzerinden İptal ve iade edilen siparişin ürünleri onaylanıp onaylanmadığına bakılmaksızın stoklarına geri eklenir, çünkü tüm ürünlerin stokları sipariş anında düşürülür.
        $record->orderItems->filter(function ($item) {
            //return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1 && $item->product->purchasable == 'in_stock';
            return $item->product->purchasable == 'in_stock';
        })->each(function ($item) use ($record) {
            $item->product->increment('stock', $item->quantity);
        });
    }

    private function restoreNonApprovedStockAction(Model $record)
    {
        // On board üzerinden onaylanan edilen siparişin ürünlerinden eğer stok tabiki yapıldığı halde onaylanmayan oldu ise onun stoğunu da geri döndürüyoruz.
        $record->orderItems->filter(function ($item) {
            return $item->is_user_suitable_control == 0 && $item->product->purchasable == 'in_stock';
        })->each(function ($item) use ($record) {
            $item->product->increment('stock', $item->quantity);
        });
    }

    private function hopiReturnTransaction($record)
    {
        $hopiOrder = \App\Models\Order\Order::find($record->id);
        $hopi_notify_checkout = $hopiOrder->meta()
            ->where("key", "hopi_notify_checkout")
            ->first();

        if ($hopi_notify_checkout?->value == 1) {
            \App\Jobs\HopiStartReturnTransactionRequest::dispatchSync(
                \App\Models\Order\Order::find($record->id)
            );

            \App\Jobs\HopiCompleteReturnTransactionRequest::dispatchSync(
                \App\Models\Order\Order::find($record->id)
            );
        }

        $hopi_requested_coin = $hopiOrder->meta()
            ->where("key", "hopi_requested_coin")
            ->first();

        if ($hopi_requested_coin?->value > 0)
            \App\Jobs\HopiRefundCoinRequest::dispatchSync(
                \App\Models\Order\Order::find($record->id)
            );
    }

    private function calculateCouponDiscount(Model $order)
    {
        $cart = new Cart();
        $orderItemsToCalculate = $order->orderItems->filter(function ($item) {
            return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1;
        });

        $orderItemsToCalculate->map(function ($item) use ($cart) {
            $cart->items->push(new CartItem([
                'product_type' => ProductVariant::class,
                'product_id' => $item->product_id,
                'quantity' => $item->quantity,
                'month' => $item->plan,
            ]));
        });

        $ccd = new \App\Rules\CalculateCouponDiscount($cart, Coupon::find($order->coupon_id));
        $ccd->calculateDiscount();
        $totalDiscount = $ccd->getDiscount();

        // if coupon is rated amount, then we need to grab eligible items' prices from order items
        if ($order->coupon->type == 'rate') {
            $getEligableCartItemsTotalPrice = 0;
            $orderItemsToCalculate->pluck('product_id')->intersect($ccd->getEligableCartItems()->pluck('product_id'))->map(function ($item) use (&$getEligableCartItemsTotalPrice, $orderItemsToCalculate) {
                $getEligableCartItemsTotalPrice += $orderItemsToCalculate->where('product_id', $item)->first()->total;
            });
            $totalDiscount = $getEligableCartItemsTotalPrice * ($order->coupon->value / 100);
        }

        return round($totalDiscount > 0 ? -1 * $totalDiscount : 0, 2);
    }

    private function isToslaPayedOrder(Model $record): bool
    {
        return Meta::where('metaable_id', $record->id)->where('metaable_type', \App\Models\Order\Order::class)->where('key', 'tosla_transaction_id')->count() > 0;
    }

    private function cancelToslaPayedOrder(Model $record)
    {
        $orderTransactionToRefund = $record->orderTransactions()
            ->where('payment_type', 'tosla')
            ->where('payment_status_id', 1) // 1 is paid
            ->where('card_id', -9999) // -9999 is tosla card id
            ->first();

        if ($orderTransactionToRefund) {
            $tosla = new \App\Services\Tosla\Tosla();
            $tosla->getAuth();
            $processId = Meta::where('metaable_id', $record->id)->where('metaable_type', \App\Models\Order\Order::class)->where('key', 'tosla_process_id')->first()->value;
            $refundResult = $tosla->refund($processId, $orderTransactionToRefund->amount, $record->user->phone, $record);

            // update order transaction payment status to refunded
            $orderTransactionToRefund->update([
                'payment_status_id' => 4, // 4 is refunded
            ]);
        }
    }
}
