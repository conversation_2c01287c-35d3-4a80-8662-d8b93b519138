<?php

if (!function_exists('get_order_status_text')) {
    function get_order_status_text($statu)
    {
        return (new $statu(\App\Models\Order::class))->getLabel();
    }
}


if (!function_exists('get_phone_with_standart_format')) {
    function get_phone_with_standart_format($val): string
    {
        $tel = Str::of($val)->remove('(')->remove(')')->remove(' ')->remove('-');

        if ($tel->startsWith('+'))
            $tel = $tel->remove('+90');

        if ($tel->startsWith('05'))
            $tel = substr($tel, 1);

        return $tel;
    }
}

if (!function_exists('payment_logger')) {
    function payment_logger($message, array $context = []): void
    {
        \Illuminate\Support\Facades\Log::channel('payment')->info($message, $context);
    }
}

if (!function_exists('hopi_logger')) {
    function hopi_logger($message, array $context = []): void
    {
        \Illuminate\Support\Facades\Log::channel('hopi')->info($message, $context);
    }
}
if (!function_exists('get_media_zoom_webp')) {
    function get_media_zoom_webp($media): ?string
    {
        if (!$media) {
            return null;
        }

        if ($media instanceof \Spatie\MediaLibrary\MediaCollections\Models\Media) {
            // get file name from removing the extension
            $fileName = Str::beforeLast($media->file_name, '.');
            return "https://kiralabunu.fra1.cdn.digitaloceanspaces.com/products/{$media->created_at->format('Y/m/d')}/conversions/{$fileName}-zoom_webp.webp";
        }

        return null;
    }
}

if (!function_exists('get_client_ip')) {
    /**
     * CloudFlare proxy arkasından gerçek ziyaretçi IP adresini alır
     *
     * @param \Illuminate\Http\Request $request
     * @return string|null IP adresi (IPv4)
     */
    function get_client_ip(\Illuminate\Http\Request $request): ?string
    {
        // Öncelikli olarak CloudFlare'ın sağladığı IP adresini al
        $ip = $request->header('cf-connecting-ip');

        // CloudFlare IP yoksa X-Forwarded-For header'ını kontrol et
        if (empty($ip) && $request->header('x-forwarded-for')) {
            $ip = trim(explode(',', $request->header('x-forwarded-for'))[0]);
        }

        // Hala bulunamadıysa standart IP adresini al
        if (empty($ip)) {
            $ip = $request->header('cf-pseudo-ipv4');
        }

        // IPv4 doğrulaması yap
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return $ip;
        }

        return null;
    }
}

if (!function_exists('getClientIPAddress')) {
    /**
     * CloudFlare proxy arkasından gerçek client IP adresini alır
     * IPv6 durumunda CloudFlare'nin IPv4 fallback header'ını tercih eder
     *
     * @param \Illuminate\Http\Request|null $request
     * @return string|null IP adresi (IPv4 veya IPv6)
     */
    function getClientIPAddress(\Illuminate\Http\Request $request = null): ?string
    {
        // Request yoksa global request'i kullan
        if (!$request) {
            $request = request();
        }

        // CloudFlare'ın sağladığı gerçek client IP'yi al
        $cfConnectingIp = $request->header('cf-connecting-ip');

        if (!empty($cfConnectingIp)) {
            // IP geçerli mi kontrol et
            if (filter_var($cfConnectingIp, FILTER_VALIDATE_IP)) {
                // IPv6 ise CloudFlare'nin IPv4 fallback'ini tercih et
                if (filter_var($cfConnectingIp, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
                    $cfPseudoIpv4 = $request->header('cf-pseudo-ipv4');
                    if (!empty($cfPseudoIpv4) && filter_var($cfPseudoIpv4, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                        return $cfPseudoIpv4;
                    }
                }

                // IPv4 ise direkt kullan
                return $cfConnectingIp;
            }
        }

        // CloudFlare header'ları yoksa diğer proxy header'larını kontrol et
        $forwardedIps = $request->header('x-forwarded-for');
        if (!empty($forwardedIps)) {
            // İlk IP'yi al (genellikle gerçek client IP)
            $firstIp = trim(explode(',', $forwardedIps)[0]);
            if (filter_var($firstIp, FILTER_VALIDATE_IP)) {
                return $firstIp;
            }
        }

        // X-Real-IP header'ını kontrol et
        $realIp = $request->header('x-real-ip');
        if (!empty($realIp) && filter_var($realIp, FILTER_VALIDATE_IP)) {
            return $realIp;
        }

        // CloudFlare pseudo IPv4'ü son çare olarak kontrol et
        $cfPseudoIpv4 = $request->header('cf-pseudo-ipv4');
        if (!empty($cfPseudoIpv4) && filter_var($cfPseudoIpv4, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return $cfPseudoIpv4;
        }

        // Hiçbiri yoksa Laravel'in varsayılan IP metodunu kullan
        $defaultIp = $request->ip();
        if (filter_var($defaultIp, FILTER_VALIDATE_IP)) {
            return $defaultIp;
        }

        // Son çare olarak server IP'lerini kontrol et
        foreach (['REMOTE_ADDR', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED'] as $key) {
            $serverIp = $_SERVER[$key] ?? null;
            if (!empty($serverIp) && filter_var($serverIp, FILTER_VALIDATE_IP)) {
                return $serverIp;
            }
        }

        return null;
    }
}

function array_get($array, $key, $default = null)
{
    // Eğer array boşsa veya key boşsa default değeri döndür
    if (!is_array($array) || empty($key)) {
        return $default;
    }

    // Eğer key direkt olarak array'de varsa değeri döndür
    if (array_key_exists($key, $array)) {
        return $array[$key];
    }

    // Nokta notasyonunu parçala
    $keys = explode('.', $key);

    // Array referansını tut
    $current = $array;

    foreach ($keys as $segment) {
        // Eğer current bir array değilse veya segment key'i yoksa default döndür
        if (!is_array($current) || !array_key_exists($segment, $current)) {
            return $default;
        }

        $current = $current[$segment];
    }

    return $current;
}
