<?php

namespace App\Services\Tosla;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class Tosla
{
    public $username;
    public $password;
    public $token;
    public $url;

    public function __construct()
    {
        $this->username = config('app.tosla.username');
        $this->password = config('app.tosla.password');
        $this->url = config('app.tosla.url');
    }

    public function getAuth()
    {
        logger()->channel('tosla')->info('ep', ['ep' => $this->url . 'auth/oauth/token']);
        $this->token = Http::withBasicAuth($this->username, $this->password)->asForm()->post($this->url . 'auth/oauth/token', [
            'grant_type' => 'client_credentials',
        ])->object()?->access_token;
        logger()->channel('tosla')->info('getAuth', ['token' => $this->token]);
        return $this;
    }

    public function generateRefcode($phoneNumber, $amount, $refCode)
    {
        logger()->channel('tosla')->info('generateRefcode', ['phoneNumber' => $this->cleanPhoneNumber($phoneNumber)->toString(), 'amount' => $amount, 'refCode' => $refCode]);
        logger()->channel('tosla')->info('ep', ['ep' => $this->url . 'gateway-third-party/ref-code/generateRefCode']);
        $res = Http::withToken($this->token)->post($this->url . 'gateway-third-party/ref-code/generateRefCode', [
            'companyId' => 48, //288, 288 sit ortam için idi 48 prod için
            'processId' => $refCode,
            'phoneNumber' => $this->cleanPhoneNumber($phoneNumber)->toString(),
            'amount' => $amount,
        ]);

        logger()->channel('tosla')->info('generateRefcode', ['response' => $res->body()]);
        if ($res->successful() && $res->object()->result) {
            // Save RefCode for trancaction
            auth()->user()->meta()->create([
                'key' => 'tosla_ref_code',
                'value' => $res->object()->refCode,
            ]);
            return $res->object();
        }

        logger()->channel('tosla')->error('generateRefcode', ['error' => $res->body()]);
        $this->closeConnection(); // For do not repeat the same process

        if (!$res->object()->result && $res->object()->errorMessage == 'Customer with given id not found') {
            throw new Exception('Telefon numarası ile eşleşen bir TOSLA kullanıcısı bulunamadı. Lütfen üyelik sırasında verdiğiniz telefon numaranızı kontrol ediniz.');
        }

        if (!$res->object()->result && $res->object()->errorMessage == 'Reference Code has already exist for this user') {
            throw new Exception('Daha önceden başlamış bir ödeme işlemi bulunmaktadır. Lütfen TOSLA uygulaması üzerinden devam ediniz.');
        }

        if (!$res->object()->result && $res->object()->errorMessage == 'Reference Code limit has been exceeded for this user') {
            throw new Exception('TOSLA ile işlem limitiniz doldu. Lütfen daha sonra tekrar deneyiniz.');
        }

        throw new Exception('TOSLA ile iletişim sırasında bir hata oluştu. Lütfen daha sonra tekrar deneyiniz.');
    }

    public function confirmPayment($phoneNumber, $amount)
    {
        return Http::withToken($this->token)->post($this->url . 'gateway-third-party/ref-code/generateRefCode', [
            'companyId' => 48,
            'processId' => Str::random(3),
            'phoneNumber' => $this->cleanPhoneNumber($phoneNumber),
            'amount' => $amount,
        ])->object();
    }

    private function cleanPhoneNumber($phone)
    {
        $no = Str::of($phone)->remove('(')->remove(')')->remove('+')->remove(' ')->remove('-');
        if ($no->startsWith(5))
            $no = $no->prepend('90');

        if ($no->startsWith(0))
            $no = $no->prepend('9');
        return $no;
    }

    public function closeConnection()
    {
        if (auth()->user()->meta()->where('key', 'tosla')->exists())
            auth()->user()->meta()->where('key', 'tosla')->delete();

        auth()->user()->meta()->create([
            'key' => 'tosla_process_completed_at',
            'value' => now(),
        ]);
    }

    public function refund($processId, $amount, $phoneNumber, $record)
    {
        $res = Http::withToken($this->token)->post(
            $this->url . 'gateway-third-party/ref-code/return/command/create',
            [
                'phoneNumber' => $this->cleanPhoneNumber($phoneNumber)->toString(),
                'processId' => $processId,
                'companyId' => 48,
                'externalTransactionId' => Str::random(10),
                'amount' => (float)$amount,
                'description' => 'Refund for ' . $processId,
            ]
        );

        if ($res->successful()) {
            // log success response
            logger()->channel('tosla')->info('refund create for processId: ' . $processId, ['response' => $res->json()]);

            // execute refund
            $resExecuteCommand = Http::withToken($this->token)->post(
                $this->url . 'gateway-third-party/ref-code/return/command/' . $res->object()->commandId . '/execute',
            );

            if ($resExecuteCommand->successful()) {
                logger()->channel('tosla')->info('refund execute for processId: ' . $processId, ['response' => $resExecuteCommand->json()]);

                // create meta for refund
                \App\Models\Order\Order::find($record->id)->meta()->createMany([
                    [
                        'key' => 'tosla_refund_at',
                        'value' => now(),
                    ],
                    [
                        'key' => 'tosla_refund_command_id',
                        'value' => $res->object()->commandId,
                    ],
                    [
                        'key' => 'tosla_refund_amount',
                        'value' => $amount,
                    ],
                    [
                        'key' => 'tosla_refund_transaction_id',
                        'value' => $resExecuteCommand->object()->transactionId,
                    ],
                ]);

                return $resExecuteCommand->object();
            }

            logger()->channel('tosla')->error('refund execute sürecinde hata for processId: ' . $processId, ['response' => $resExecuteCommand->body()]);
        }

        logger()->channel('tosla')->error('refund create sürecinde hata for processId: ' . $processId, ['response' => $res->body()]);
        throw new Exception('TOSLA ile iletişim sırasında bir hata oluştu. Lütfen daha sonra tekrar deneyiniz.');
    }
}
