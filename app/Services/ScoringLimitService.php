<?php

namespace App\Services;

use App\Events\ScoringCompleted;
use App\Models\ScoringLimit;
use App\Models\ScoringRequest;
use App\Models\ScoringResultNew;
use App\States\ScoringRequest\ApprovedState;
use App\States\ScoringRequest\RejectedState;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ScoringLimitService
{
    /**
     * Check if a scoring request can be auto-processed based on existing limits
     *
     * @param ScoringRequest $scoringRequest
     * @return bool True if auto-processed, false if should continue to Redis
     */
    public function checkAndProcessWithLimit(ScoringRequest $scoringRequest): bool
    {
        // Get the latest valid limit for this TCKN
        $scoringLimit = ScoringLimit::getLatestValidByTckn($scoringRequest->tckn);

        if (!$scoringLimit) {
            Log::info('No valid scoring limit found for TCKN', [
                'scoring_request_id' => $scoringRequest->id,
                'tckn' => $scoringRequest->tckn
            ]);
            return false; // Continue with normal flow (Redis)
        }

        // Check if limit has enough balance
        if ($scoringLimit->hasAvailableBalance($scoringRequest->requested_amount)) {
            // Auto-approve with limit
            return $this->processAutoApproval($scoringRequest, $scoringLimit);
        } else {
            // Auto-reject due to insufficient limit
            return $this->processAutoRejection($scoringRequest, $scoringLimit);
        }
    }

    /**
     * Process auto-approval using existing limit
     *
     * @param ScoringRequest $scoringRequest
     * @param ScoringLimit $scoringLimit
     * @return bool
     */
    protected function processAutoApproval(ScoringRequest $scoringRequest, ScoringLimit $scoringLimit): bool
    {
        return DB::transaction(function () use ($scoringRequest, $scoringLimit) {
            try {
                $limitBefore = $scoringLimit->remaining_limit;

                // Use the limit
                if (!$scoringLimit->useLimit($scoringRequest->requested_amount)) {
                    Log::error('Failed to use limit during auto-approval', [
                        'scoring_request_id' => $scoringRequest->id,
                        'scoring_limit_id' => $scoringLimit->id
                    ]);
                    return false;
                }

                // Update scoring request with auto-approval info
                $additionalData = $scoringRequest->additional_data ?? [];
                $additionalData['auto_processed'] = true;
                $additionalData['auto_process_type'] = 'limit_based_approval';
                $additionalData['auto_process_date'] = now()->toISOString();
                $additionalData['used_limit_id'] = $scoringLimit->id;
                $additionalData['limit_before'] = $limitBefore;
                $additionalData['limit_after'] = $scoringLimit->remaining_limit;

                $scoringRequest->update([
                    'status' => ApprovedState::class,
                    'additional_data' => $additionalData,
                    'manual_approved_amount' => $scoringRequest->requested_amount,
                    'manual_processed_at' => now()
                ]);

                // Create scoring result
                ScoringResultNew::create([
                    'scoring_request_id' => $scoringRequest->id,
                    'score' => $scoringLimit->score,
                    'is_approved' => true,
                    'approved_amount' => $scoringRequest->requested_amount,
                    'processed_at' => now()
                ]);

                // Add note
                $scoringRequest->notes()->create([
                    'content' => sprintf(
                        'Mevcut limit kullanılarak otomatik onaylandı (Limit ID: %d, Kullanılan: %s TL)',
                        $scoringLimit->id,
                        number_format($scoringRequest->requested_amount, 2, ',', '.')
                    )
                ]);

                Log::info('Scoring request auto-approved with existing limit', [
                    'scoring_request_id' => $scoringRequest->id,
                    'scoring_limit_id' => $scoringLimit->id,
                    'requested_amount' => $scoringRequest->requested_amount,
                    'limit_before' => $limitBefore,
                    'limit_after' => $scoringLimit->remaining_limit
                ]);

                // Trigger webhook event (listener will handle the delay)
                ScoringCompleted::dispatch($scoringRequest->fresh());

                return true;
            } catch (\Exception $e) {
                Log::error('Error during auto-approval process', [
                    'scoring_request_id' => $scoringRequest->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }
        });
    }

    /**
     * Process auto-rejection due to insufficient limit
     *
     * @param ScoringRequest $scoringRequest
     * @param ScoringLimit $scoringLimit
     * @return bool
     */
    protected function processAutoRejection(ScoringRequest $scoringRequest, ScoringLimit $scoringLimit): bool
    {
        return DB::transaction(function () use ($scoringRequest, $scoringLimit) {
            try {
                // Update scoring request with auto-rejection info
                $additionalData = $scoringRequest->additional_data ?? [];
                $additionalData['auto_processed'] = true;
                $additionalData['auto_process_type'] = 'insufficient_limit_rejection';
                $additionalData['auto_process_date'] = now()->toISOString();
                $additionalData['checked_limit_id'] = $scoringLimit->id;
                $additionalData['available_limit'] = $scoringLimit->remaining_limit;
                $additionalData['requested_amount'] = $scoringRequest->requested_amount;

                $scoringRequest->update([
                    'status' => RejectedState::class,
                    'additional_data' => $additionalData,
                    'manual_processed_at' => now()
                ]);

                // Create scoring result
                ScoringResultNew::create([
                    'scoring_request_id' => $scoringRequest->id,
                    'score' => $scoringLimit->score,
                    'is_approved' => false,
                    'approved_amount' => 0,
                    'processed_at' => now()
                ]);

                // Add note
                $scoringRequest->notes()->create([
                    'content' => sprintf(
                        'Yetersiz limit nedeniyle otomatik reddedildi (Mevcut: %s TL, Talep: %s TL)',
                        number_format($scoringLimit->remaining_limit, 2, ',', '.'),
                        number_format($scoringRequest->requested_amount, 2, ',', '.')
                    )
                ]);

                Log::info('Scoring request auto-rejected due to insufficient limit', [
                    'scoring_request_id' => $scoringRequest->id,
                    'scoring_limit_id' => $scoringLimit->id,
                    'requested_amount' => $scoringRequest->requested_amount,
                    'available_limit' => $scoringLimit->remaining_limit
                ]);

                // Trigger webhook event
                ScoringCompleted::dispatch($scoringRequest->fresh());

                return true;
            } catch (\Exception $e) {
                Log::error('Error during auto-rejection process', [
                    'scoring_request_id' => $scoringRequest->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }
        });
    }

    /**
     * Check if auto-processing is enabled for this source
     *
     * @param int $sourceId
     * @return bool
     */
    private function isEnabledForSource(int $sourceId): bool
    {
        if (!config('scoring.enabled', true)) {
            return false;
        }

        $enabledSources = config('scoring.enabled_sources', '*');
        
        // If string and equals *, enabled for all
        if (is_string($enabledSources) && $enabledSources === '*') {
            return true;
        }
        
        // Convert to array if needed
        if (is_string($enabledSources)) {
            $enabledSources = array_map('trim', explode(',', $enabledSources));
        }
        
        // Check if source ID is in the list
        return in_array((string)$sourceId, $enabledSources, true);
    }

    /**
     * Process auto-approval with multiplier calculation
     *
     * @param ScoringRequest $scoringRequest
     * @param ScoringLimit $scoringLimit
     * @param array $evaluation
     * @return bool
     */
    protected function processAutoApprovalWithMultiplier(ScoringRequest $scoringRequest, ScoringLimit $scoringLimit, array $evaluation): bool
    {
        return DB::transaction(function () use ($scoringRequest, $scoringLimit, $evaluation) {
            try {
                $limitBefore = $scoringLimit->remaining_limit;
                $approvedAmount = $evaluation['approved_amount'];

                // Use the limit
                if (!$scoringLimit->useLimit($approvedAmount)) {
                    Log::error('Failed to use limit during auto-approval', [
                        'scoring_request_id' => $scoringRequest->id,
                        'scoring_limit_id' => $scoringLimit->id
                    ]);
                    return false;
                }

                // Update scoring request with auto-approval info
                $additionalData = $scoringRequest->additional_data ?? [];
                $additionalData['auto_processed'] = true;
                $additionalData['auto_process_type'] = 'limit_based_approval_with_multiplier';
                $additionalData['auto_process_date'] = now()->toISOString();
                $additionalData['used_limit_id'] = $scoringLimit->id;
                $additionalData['limit_before'] = $limitBefore;
                $additionalData['limit_after'] = $scoringLimit->remaining_limit;
                $additionalData['applied_multiplier'] = $evaluation['multiplier'];
                $additionalData['max_rent_price'] = $evaluation['max_rent_price'] ?? null;

                $scoringRequest->update([
                    'status' => ApprovedState::class,
                    'additional_data' => $additionalData,
                    'manual_approved_amount' => $approvedAmount,
                    'manual_processed_at' => now()
                ]);

                // Create scoring result
                ScoringResultNew::create([
                    'scoring_request_id' => $scoringRequest->id,
                    'score' => $scoringLimit->score,
                    'is_approved' => true,
                    'approved_amount' => $approvedAmount,
                    'processed_at' => now()
                ]);

                // Add note
                $scoringRequest->notes()->create([
                    'content' => sprintf(
                        'Mevcut limit kullanılarak otomatik onaylandı (Limit ID: %d, Kullanılan: %s TL, Çarpan: %s)',
                        $scoringLimit->id,
                        number_format($approvedAmount, 2, ',', '.'),
                        $evaluation['multiplier']
                    )
                ]);

                Log::info('Scoring request auto-approved with multiplier', [
                    'scoring_request_id' => $scoringRequest->id,
                    'scoring_limit_id' => $scoringLimit->id,
                    'approved_amount' => $approvedAmount,
                    'multiplier' => $evaluation['multiplier'],
                    'limit_before' => $limitBefore,
                    'limit_after' => $scoringLimit->remaining_limit
                ]);

                // Check if moderation required and send email
                if ($evaluation['requires_moderation'] ?? false) {
                    $this->sendModerationNotification($scoringRequest, $evaluation);
                }

                // Trigger webhook event
                ScoringCompleted::dispatch($scoringRequest->fresh());

                return true;
            } catch (\Exception $e) {
                Log::error('Error during auto-approval process with multiplier', [
                    'scoring_request_id' => $scoringRequest->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }
        });
    }

    /**
     * Process auto-rejection due to low score
     *
     * @param ScoringRequest $scoringRequest
     * @param ScoringLimit $scoringLimit
     * @param array $evaluation
     * @return bool
     */
    protected function processAutoRejectionByScore(ScoringRequest $scoringRequest, ScoringLimit $scoringLimit, array $evaluation): bool
    {
        return DB::transaction(function () use ($scoringRequest, $scoringLimit, $evaluation) {
            try {
                // Update scoring request with auto-rejection info
                $additionalData = $scoringRequest->additional_data ?? [];
                $additionalData['auto_processed'] = true;
                $additionalData['auto_process_type'] = 'low_score_rejection';
                $additionalData['auto_process_date'] = now()->toISOString();
                $additionalData['score'] = $scoringLimit->score;
                $additionalData['rejection_reason'] = $evaluation['evaluation_reason'];

                $scoringRequest->update([
                    'status' => RejectedState::class,
                    'additional_data' => $additionalData,
                    'manual_processed_at' => now()
                ]);

                // Create scoring result
                ScoringResultNew::create([
                    'scoring_request_id' => $scoringRequest->id,
                    'score' => $scoringLimit->score,
                    'is_approved' => false,
                    'approved_amount' => 0,
                    'processed_at' => now()
                ]);

                // Add note
                $scoringRequest->notes()->create([
                    'content' => sprintf(
                        'Düşük skor nedeniyle otomatik reddedildi (Skor: %d, Sebep: %s)',
                        $scoringLimit->score,
                        $evaluation['evaluation_reason']
                    )
                ]);

                Log::info('Scoring request auto-rejected due to low score', [
                    'scoring_request_id' => $scoringRequest->id,
                    'score' => $scoringLimit->score,
                    'reason' => $evaluation['evaluation_reason']
                ]);

                // Trigger webhook event
                ScoringCompleted::dispatch($scoringRequest->fresh());

                return true;
            } catch (\Exception $e) {
                Log::error('Error during auto-rejection process by score', [
                    'scoring_request_id' => $scoringRequest->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }
        });
    }

    /**
     * Send moderation notification email
     *
     * @param ScoringRequest $scoringRequest
     * @param array $evaluation
     * @return void
     */
    private function sendModerationNotification(ScoringRequest $scoringRequest, array $evaluation): void
    {
        try {
            if (!config('scoring.moderation.send_notifications', true)) {
                return;
            }

            SendScoringModerationEmailJob::dispatch($scoringRequest, $evaluation)
                ->onQueue('emails');

            Log::info('Moderation notification queued', [
                'scoring_request_id' => $scoringRequest->id,
                'product_value' => $evaluation['product_value'] ?? 0
            ]);
        } catch (\Exception $e) {
            // Don't fail the main process if email fails
            Log::error('Failed to queue moderation notification', [
                'scoring_request_id' => $scoringRequest->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
