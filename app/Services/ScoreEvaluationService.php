<?php

namespace App\Services;

use App\Models\ScoringRequest;
use Illuminate\Support\Facades\Log;

class ScoreEvaluationService
{
    /**
     * Minimum onay skoru
     */
    protected $minApprovalScore;


    public function __construct()
    {
        $this->minApprovalScore = config('scoring.score_thresholds.standard', 275);
    }

    /**
     * <PERSON>kor değerlendirmesi yap
     *
     * @param int $score 500 üzerinden skor
     * @param float $requestedAmount Talep edilen tutar
     * @return array ['is_approved' => bool, 'approved_amount' => float, ...]
     */
    public function evaluate(int $score, float $requestedAmount): array
    {
        // Input validation
        if ($score < 0 || $requestedAmount <= 0) {
            return [
                'is_approved' => false,
                'approved_amount' => 0,
                'score' => $score,
                'requested_amount' => $requestedAmount,
                'minimum_score' => $this->minApprovalScore,
                'evaluation_reason' => $this->getEvaluationReason($score),
                'score_band' => $this->getScoreBand($score)
            ];
        }

        // Temel business logic
        $isApproved = $score >= $this->minApprovalScore;

        if ($isApproved) {
            // Onaylandıysa talep edilen tutarın tamamını ver
            $approvedAmount = $requestedAmount;
        } else {
            // Reddedildiyse 0 tutar
            $approvedAmount = 0;
        }

        return [
            'is_approved' => $isApproved,
            'approved_amount' => $approvedAmount,
            'score' => $score,
            'requested_amount' => $requestedAmount,
            'minimum_score' => $this->minApprovalScore,
            'evaluation_reason' => $this->getEvaluationReason($score),
            'score_band' => $this->getScoreBand($score)
        ];
    }

    /**
     * Gelecekte daha karmaşık değerlendirme için yapılandırılabilir
     *
     * @param int $score
     * @param float $requestedAmount
     * @param array $options Ek parametreler
     * @return array
     */
    public function evaluateAdvanced(int $score, float $requestedAmount, array $options = []): array
    {
        // Gelecekteki gelişmiş değerlendirme mantığı için placeholder
        // Örnek parametreler:
        // - risk_multiplier: Risk çarpanı
        // - max_approval_amount: Maksimum onay tutarı
        // - score_bands: Skor bantları ve tutarları

        $riskMultiplier = $options['risk_multiplier'] ?? 1.0;
        $maxApprovalAmount = $options['max_approval_amount'] ?? $requestedAmount;

        $isApproved = $score >= $this->minApprovalScore;

        if ($isApproved) {
            // Risk çarpanı ile tutarı ayarla
            $calculatedAmount = $requestedAmount * $riskMultiplier;
            $approvedAmount = min($calculatedAmount, $maxApprovalAmount);
        } else {
            $approvedAmount = 0;
        }

        return [
            'is_approved' => $isApproved,
            'approved_amount' => $approvedAmount,
            'score' => $score,
            'requested_amount' => $requestedAmount,
            'minimum_score' => $this->minApprovalScore,
            'evaluation_reason' => $this->getEvaluationReason($score),
            'score_band' => $this->getScoreBand($score),
            'calculated_amount' => $calculatedAmount ?? 0,
            'risk_multiplier' => $riskMultiplier
        ];
    }

    /**
     * Değerlendirme sebebini döndür
     *
     * @param int $score
     * @return string
     */
    private function getEvaluationReason(int $score): string
    {
        if ($score >= $this->minApprovalScore) {
            if ($score >= 450) {
                return "Yüksek skor ({$score}) - Tam onay";
            } elseif ($score >= 400) {
                return "Orta-yüksek skor ({$score}) - Onaylandı";
            } else {
                return "Minimum skor ({$score}) - Onaylandı";
            }
        } else {
            if ($score >= 300) {
                return "Düşük skor ({$score}) - Minimum skor " . $this->minApprovalScore . " altında reddedildi";
            } elseif ($score >= 200) {
                return "Çok düşük skor ({$score}) - Reddedildi";
            } else {
                return "Kritik düşük skor ({$score}) - Reddedildi";
            }
        }
    }

    /**
     * Skor bandını döndür
     *
     * @param int $score
     * @return string
     */
    private function getScoreBand(int $score): string
    {
        if ($score >= 450) {
            return 'excellent';
        } elseif ($score >= 400) {
            return 'very_good';
        } elseif ($score >= 350) {
            return 'good';
        } elseif ($score >= 300) {
            return 'fair';
        } else {
            return 'poor';
        }
    }

    /**
     * Skor bantlarını döndür (admin paneli için)
     *
     * @return array
     */
    public function getScoreBands(): array
    {
        return [
            [
                'min_score' => 450,
                'max_score' => 500,
                'description' => 'Yüksek Risk Profili',
                'approval_rate' => 100,
                'color' => 'success'
            ],
            [
                'min_score' => 400,
                'max_score' => 449,
                'description' => 'Orta-Yüksek Risk Profili',
                'approval_rate' => 100,
                'color' => 'success'
            ],
            [
                'min_score' => 350,
                'max_score' => 399,
                'description' => 'Orta Risk Profili',
                'approval_rate' => 100,
                'color' => 'warning'
            ],
            [
                'min_score' => 300,
                'max_score' => 349,
                'description' => 'Orta-Düşük Risk Profili',
                'approval_rate' => 0,
                'color' => 'danger'
            ],
            [
                'min_score' => 200,
                'max_score' => 299,
                'description' => 'Düşük Risk Profili',
                'approval_rate' => 0,
                'color' => 'danger'
            ],
            [
                'min_score' => 0,
                'max_score' => 199,
                'description' => 'Kritik Risk Profili',
                'approval_rate' => 0,
                'color' => 'dark'
            ],
        ];
    }

    /**
     * Minimum onay skorunu güncelle (gelecekte config'den okunabilir)
     *
     * @param int $minScore
     * @return void
     */
    public function setMinApprovalScore(int $minScore): void
    {
        // Gelecekte cache veya config'e yazılabilir
        // Şu an için sabit değer kullanıyoruz
    }

    /**
     * Çarpan ile skor değerlendirmesi yap (yeni sistem)
     *
     * @param ScoringRequest $scoringRequest
     * @param int $score
     * @return array
     */
    public function evaluateWithMultiplier(ScoringRequest $scoringRequest, int $score, float $maxRentPrice): array
    {
        // Config değerlerini al
        $rejectionThreshold = config('scoring.score_thresholds.rejection', 275);
        $standardThreshold = config('scoring.score_thresholds.standard', 275);
        $baseMonths = config('scoring.calculation.base_months', 18);

        Log::info('evaluateWithMultiplier for scoring request ' . $scoringRequest->id, [
            'score' => $score,
            'requested_amount' => $scoringRequest->requested_amount,
            'additional_data' => $scoringRequest->additional_data,
            'scoring_source_id' => $scoringRequest->scoring_source_id,
        ]);

        // Skor < 275 ise doğrudan red
        if ($score < $rejectionThreshold) {
            Log::info('Scoring auto-rejected: Score below rejection threshold', [
                'scoring_request_id' => $scoringRequest->id,
                'score' => $score,
                'rejection_threshold' => $rejectionThreshold
            ]);

            $scoringRequest->notes()->create([
                'content' => 'Skorlama otomatik reddedildi. Skor: ' . $score . ' - Red sınırı: ' . $rejectionThreshold,
            ]);

            return [
                'is_approved' => false,
                'approved_amount' => 0,
                'score' => $score,
                'requested_amount' => (float) $scoringRequest->requested_amount,
                'evaluation_reason' => "Skor ({$score}) red sınırı {$rejectionThreshold} altında - Otomatik red",
                'multiplier' => 1,
                'requires_moderation' => false
            ];
        }

        // Çarpan hesaplama
        $multiplier = $this->calculateMultiplier($scoringRequest, $score);

        // Max kira bedeli hesaplama (FindexScoringService'den alınan logic)
        // Not: Gerçek hesaplama için FindexScoringService'deki calculateRentRange metodu kullanılabilir
        // $maxRentPrice = $scoringRequest->requested_amount / $scoringRequest->requested_duration_months;
        $approvedAmount = $maxRentPrice * $scoringRequest->requested_duration_months * $multiplier;

        // Mal bedeli kontrolü (additional_data'dan alınacak)
        $productValue = $this->getProductValue($scoringRequest);
        $requiresModeration = $this->checkModerationRequired($productValue);

        Log::info('Scoring evaluation with multiplier for scoring request ' . $scoringRequest->id, [
            'scoring_request_id' => $scoringRequest->id,
            'score' => $score,
            'multiplier' => $multiplier,
            'max_rent_price' => $maxRentPrice,
            'approved_amount' => $approvedAmount,
            'product_value' => $productValue,
            'requires_moderation' => $requiresModeration
        ]);

        $scoringRequest->notes()->create([
            'content' => 'Skorlama çarpanlı değerlendirildi. Skor: ' . $score . ' - Çarpan: ' . $multiplier,
        ]);

        return [
            'is_approved' => true,
            'approved_amount' => round($approvedAmount, 2),
            'score' => $score,
            'requested_amount' => (float) $scoringRequest->requested_amount,
            'evaluation_reason' => $this->getMultiplierEvaluationReason($score, $multiplier),
            'multiplier' => $multiplier,
            'requires_moderation' => $requiresModeration,
            'product_value' => $productValue,
            'max_rent_price' => round($maxRentPrice, 2)
        ];
    }

    /**
     * Çarpan hesapla
     *
     * @param ScoringRequest $scoringRequest
     * @param int $score
     * @return float
     */
    private function calculateMultiplier(ScoringRequest $scoringRequest, int $score): float
    {
        $standardThreshold = config('scoring.score_thresholds.standard', 375);

        // Skor 275-375 arası ise çarpan 1
        if ($score <= $standardThreshold) {
            return 1.0;
        }

        // Skor > 375 ise partner çarpanını kullan
        $additionalData = $scoringRequest->additional_data ?? [];
        $partnerMultiplier = $additionalData['partner_data']['model_line_multiplier'] ?? null;

        // Partner çarpanı varsa kullan
        if ($partnerMultiplier !== null) {
            return (float) $partnerMultiplier;
        }

        // Kiralama süresi çarpanını kontrol et
        $rentalMonths = $scoringRequest->requested_duration_months;
        $rentalPeriodMultipliers = config('scoring.multipliers.rental_periods', []);

        if (isset($rentalPeriodMultipliers[$rentalMonths])) {
            return (float) $rentalPeriodMultipliers[$rentalMonths];
        }

        // Default çarpan
        return (float) config('scoring.multipliers.default', 1.0);
    }

    /**
     * Mal bedelini al
     *
     * @param ScoringRequest $scoringRequest
     * @return float
     */
    private function getProductValue(ScoringRequest $scoringRequest): float
    {
        $additionalData = $scoringRequest->additional_data ?? [];

        // Mal bedeli farklı alanlarda olabilir
        $productValue = $additionalData['product_value'] ??
            $additionalData['product_price'] ??
            $additionalData['asset_value'] ??
            0;

        return (float) $productValue;
    }

    /**
     * Moderasyon gerekli mi kontrol et
     *
     * @param float $productValue
     * @return bool
     */
    private function checkModerationRequired(float $productValue): bool
    {
        $moderationThreshold = config('scoring.moderation.required_above_amount', 500000);
        return $productValue > $moderationThreshold;
    }

    /**
     * Çarpanlı değerlendirme sebebi
     *
     * @param int $score
     * @param float $multiplier
     * @return string
     */
    private function getMultiplierEvaluationReason(int $score, float $multiplier): string
    {
        $standardThreshold = config('scoring.score_thresholds.standard', 375);

        if ($score <= $standardThreshold) {
            return "Skor ({$score}) standart aralıkta - Çarpan: {$multiplier}";
        } else {
            return "Skor ({$score}) yüksek - Partner/Süre çarpanı uygulandı: {$multiplier}";
        }
    }
}
