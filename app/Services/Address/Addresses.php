<?php

namespace App\Services\Address;

use ArrayAccess;
use Illuminate\Contracts\Support\Arrayable;

class Addresses implements ArrayAccess, Arrayable
{
    public ShippingAddress $shipping;

    public BillingAddress $billing;

    public function __construct($shipping = [], $billing = [])
    {
        $this->shipping = new ShippingAddress($shipping);
        $this->billing = new BillingAddress($billing);
    }

    public function offsetExists($offset): bool
    {
        $result = in_array($offset, ['shipping', 'billing']);

        if ($result) {
            return $this->{$offset}->isNotEmpty();
        }

        return false;
    }

    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->{$offset} ?? collect();
    }

    public function offsetSet($offset, $value): void
    {
        //
    }

    public function offsetUnset($offset): void
    {
        //
    }

    public function toArray()
    {
        return [
            'shipping' => $this->shipping->toArray(),
            'billing' => $this->billing->toArray(),
        ];
    }

    public function wrapToArray()
    {
        return [
            'shipping' => $this->shipping->wrapToArray(),
            'billing' => $this->billing->wrapToArray(),
        ];
    }
}
