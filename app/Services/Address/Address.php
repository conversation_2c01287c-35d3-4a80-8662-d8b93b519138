<?php

namespace App\Services\Address;

use ArrayAccess;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Str;

abstract class Address implements Arrayable, ArrayAccess, Jsonable
{
    protected array $parameters = [];

    protected array $modifiedParameters = [];

    public string $type;

    public string $name;

    public string $country;

    public string $city;

    public string $district;

    public string $address;

    public string $latitude;

    public string $longitude;

    public string $postalCode;

    public string $building;

    public string $floor;

    public string $flatNo;

    public string $doorBell;

    public string $firstName;

    public string $lastName;

    public string $phoneNumber;

    public string $fullName;

    public string $companyName;

    public string $taxOffice;

    public string $taxNo;

    public function __construct($address = [])
    {
        $this->setParameters($address);
    }

    abstract public function render(): string;

    public function setParameters($address = [])
    {
        $this->parameters = collect($address)
            ->mapWithKeys(fn($value, $key) => [Str::camel($key) => $value])
            ->toArray();

        $this->modifiedParameters = $this->parameters;

        $properties = $this->getPublicPropertiesDefinedBySubClass();

        foreach ($properties as $property) {
            $this->{$property} = $this->parameters[$property] ?? $this->{$property} ?? '';
        }

        $this->fullName = sprintf('%s %s', $this->firstName, $this->lastName);

        $this->modifiedParameters['fullName'] = $this->fullName;

        $this->modifiedParameters = collect($this->modifiedParameters)
            ->mapWithKeys(fn($value, $key) => [Str::snake($key) => $value])
            ->toArray();
    }

    protected function getPublicPropertiesDefinedBySubClass()
    {
        $publicProperties = array_filter((new \ReflectionClass($this))->getProperties(), function ($property) {
            return $property->isPublic() && !$property->isStatic();
        });

        $data = [];

        foreach ($publicProperties as $property) {
            $data[] = $property->getName();
        }

        return $data;
    }

    public function __get($key)
    {
        $camelKey = Str::camel($key);

        if (
            isset($this->parameters[$key]) ||
            isset($this->parameters[$camelKey])
        ) {
            return $this->parameters[$key] ?? $this->parameters[$camelKey];
        }

        return null;
    }

    public function isEmpty()
    {
        return empty(array_filter($this->parameters));
    }

    public function isNotEmpty()
    {
        return !$this->isEmpty();
    }

    public function toArray()
    {
        if ($this->isEmpty()) {
            return [];
        }

        return $this->modifiedParameters;
    }

    public function wrapToArray()
    {
        if ($this->isEmpty()) {
            $properties = $this->getPublicPropertiesDefinedBySubClass();

            return collect($properties)->mapWithKeys(fn($key) => [$key => null])->toArray();
        }

        return $this->toArray();
    }

    public function toJson($options = 0)
    {
        return json_encode($this->toArray(), $options);
    }

    public function offsetExists($offset): bool
    {
        return isset($this->modifiedParameters[$offset]);
    }

    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->modifiedParameters[$offset];
    }

    public function offsetSet($offset, $value): void
    {
        $this->modifiedParameters[$offset] = $value;
    }

    public function offsetUnset($offset): void
    {
        unset($this->modifiedParameters[$offset]);
    }

    public function __toString()
    {
        return $this->render();
    }
}
