<?php

namespace App\Services;

use App\Models\Iyzico3DRequest;
use App\Traits\Payable;
use App\Exceptions\Card\PayableMustHaveCreditCardException;
use App\Exceptions\Fields\BillFieldsException;
use App\Exceptions\Card\CardRemoveException;
use App\Exceptions\Fields\CreditCardFieldsException;
use App\Exceptions\Transaction\TransactionSaveException;
use App\Exceptions\Transaction\TransactionVoidException;
use App\Exceptions\Iyzipay\IyzipayAuthenticationException;
use App\Exceptions\Iyzipay\IyzipayConnectionException;
use App\Models\CreditCard;
use App\Models\Transaction;
use App\Traits\ManagesPlans;
use App\Traits\PreparesCreditCardRequest;
use App\Traits\PreparesTransactionRequest;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Iyzipay\Model\ApiTest;
use Iyzipay\Model\Payment;
use Iyzipay\Options;
use Iyzipay\Model\Locale;

class Iyzipay
{

    use PreparesCreditCardRequest, PreparesTransactionRequest, ManagesPlans;

    /**
     * @var Options
     */
    protected $apiOptions;

    public function __construct()
    {
        $this->initializeApiOptions();
        $this->checkApiOptions();
    }

    /**
     * Adds credit card for billable & payable model.
     *
     * @param array $attributes
     *
     * @return CreditCard
     * @throws BillFieldsException
     * @throws CreditCardFieldsException
     */
    public function addCreditCard($payable, array $attributes = []): CreditCard|JsonResponse
    {
        $this->validateBillable($payable);

        $attributes['number'] = Str::remove(' ', $attributes['number']); // remove spaces from credit card number before validation

        try {
            $this->validateCreditCardAttributes($attributes);
            $card = $this->createCardOnIyzipay($payable, $attributes);

            $creditCardModel = new CreditCard([
                'alias' => $attributes['ccName'] ?? $card->getCardFamily() . ' Kartım',
                'number' => substr($attributes['number'], -4),
                'token' => $card->getCardToken(),
                'bank' => $card->getCardBankName(),
                'bin_number' => $card->getBinNumber(),
                'holder' => $attributes['holder'],
                'month' => $attributes['month'],
                'year' => $attributes['year'],
                'card_type' => $card->getCardType(),
                'card_association' => $card->getCardAssociation(),
                'card_family' => $card->getCardFamily(),
                'service_name' => 'IYZICO',
                'service_env' => env('IYZIPAY_ENV'),
            ]);

            $payable->creditCards()->save($creditCardModel);

            $payable->iyzipay_key = $card->getCardUserKey();
            $payable->save();

            return $creditCardModel;
        } catch (\Exception $e) {
            //dd($e->getMessage(), $e->getLine(), $e->getFile(), 'IYZIPAY KART SAKLAMA HATASI');
            //return response()->json(['message' => $e->getMessage(), 'status' => false], 422);
            throw new CreditCardFieldsException($e->getMessage());
        }
    }

    public function start3DRequest($payable, array $attributes = [], $bearerToken)
    {
        $this->validateBillable($payable);

        $attributes['number'] = Str::remove(' ', $attributes['number']); // remove spaces from credit card number before validation
        $attributes['cvc'] = Str::of($attributes['cvc'])->padLeft(3, 0); // add left padding to cvv
        try {
            $this->validateCreditCardAttributes($attributes);

            $iyReq = Iyzico3DRequest::create([
                'user_id' => $payable->id,
                'token' => Str::of(Str::random(10))->upper(),

                //                'alias' => $attributes['ccName'] ?? $card->getCardFamily() . ' Kartım',
                'number' => substr($attributes['number'], -4),
                //                'token' => $card->getCardToken(),
                //                'bank' => $card->getCardBankName(),
                //                'bin_number' => $card->getBinNumber(),
                'holder' => $attributes['holder'],
                'month' => $attributes['month'],
                'year' => $attributes['year'],
                //                'card_type' => $card->getCardType(),
                //                'card_association' => $card->getCardAssociation(),
                //                'card_family' => $card->getCardFamily(),
            ]);


            $request = new \Iyzipay\Request\CreatePaymentRequest();
            $request->setLocale(\Iyzipay\Model\Locale::TR);
            $request->setConversationId(time());
            $request->setPrice("1");
            $request->setPaidPrice("1");
            $request->setCurrency(\Iyzipay\Model\Currency::TL);
            $request->setInstallment(1);
            $request->setBasketId(time());
            $request->setPaymentChannel(\Iyzipay\Model\PaymentChannel::WEB);
            $request->setPaymentGroup(\Iyzipay\Model\PaymentGroup::PRODUCT);

            // Set callback url for 3D payment depending on source
            $source = 'cart';
            if (data_get($attributes, 'source') == 'profile') {
                $source = 'profile';
            }

            $request->setCallbackUrl(env('FRONTEND_URL') . "/callback?source=" . $source . "&token=" . $iyReq->token);

            $paymentCard = new \Iyzipay\Model\PaymentCard();
            $paymentCard->setCardHolderName($attributes['holder']);
            $paymentCard->setCardNumber($attributes['number']);
            $paymentCard->setExpireMonth($attributes['month']);
            $paymentCard->setExpireYear($attributes['year']);
            $paymentCard->setCvc($attributes['cvc']);
            $paymentCard->setRegisterCard(1);
            $request->setPaymentCard($paymentCard);

            $buyer = new \Iyzipay\Model\Buyer();
            $buyer->setId($payable->id);
            $buyer->setName($payable->first_name);
            $buyer->setSurname($payable->last_name);
            $buyer->setGsmNumber($payable->phone);
            $buyer->setEmail($payable->email);
            $buyer->setIdentityNumber($payable->tckn);
            //            $buyer->setLastLoginDate("2015-10-05 12:43:35");
            //            $buyer->setRegistrationDate("2013-04-21 15:12:09");
            $buyer->setRegistrationAddress("Istanbul Türkiye");
            $buyer->setIp("************");
            $buyer->setCity("Istanbul");
            $buyer->setCountry("Türkiye");
            //            $buyer->setZipCode("34732");
            $request->setBuyer($buyer);

            $shippingAddress = new \Iyzipay\Model\Address();
            $shippingAddress->setContactName($payable->full_name_db);
            $shippingAddress->setCity("Istanbul");
            $shippingAddress->setCountry("Türkiye");
            $shippingAddress->setAddress("Istanbul Türkiye");
            //            $shippingAddress->setZipCode("34742");
            $request->setShippingAddress($shippingAddress);

            $billingAddress = new \Iyzipay\Model\Address();
            $billingAddress->setContactName($payable->full_name_db);
            $billingAddress->setCity("Istanbul");
            $billingAddress->setCountry("Türkiye");
            $billingAddress->setAddress("Istanbul Türkiye");
            //            $billingAddress->setZipCode("34742");
            $request->setBillingAddress($billingAddress);

            $basketItems = array();
            $firstBasketItem = new \Iyzipay\Model\BasketItem();
            $firstBasketItem->setId("BI101");
            $firstBasketItem->setName("CC");
            $firstBasketItem->setCategory1("CC");
            $firstBasketItem->setItemType(\Iyzipay\Model\BasketItemType::VIRTUAL);
            $firstBasketItem->setPrice("1");
            $basketItems[0] = $firstBasketItem;

            $request->setBasketItems($basketItems);

            // make request
            return $threedsInitialize = \Iyzipay\Model\ThreedsInitialize::create($request, self::options());

            //            $card = $this->createCardOnIyzipay($payable, $attributes);

            //            $creditCardModel = new CreditCard([
            //                'alias' => $attributes['ccName'] ?? $card->getCardFamily() . ' Kartım',
            //                'number' => substr($attributes['number'], -4),
            //                'token' => $card->getCardToken(),
            //                'bank' => $card->getCardBankName(),
            //                'bin_number' => $card->getBinNumber(),
            //                'holder' => $attributes['holder'],
            //                'month' => $attributes['month'],
            //                'year' => $attributes['year'],
            //                'card_type' => $card->getCardType(),
            //                'card_association' => $card->getCardAssociation(),
            //                'card_family' => $card->getCardFamily(),
            //                'service_name' => 'IYZICO',
            //                'service_env' => env('IYZIPAY_ENV'),
            //            ]);
            //
            //            $payable->creditCards()->save($creditCardModel);

            //$payable->iyzipay_key = $card->getCardUserKey();
            //$payable->save();

            //return $creditCardModel;
        } catch (\Exception $e) {
            //dd($e->getMessage(), $e->getLine(), $e->getFile(), 'IYZIPAY KART SAKLAMA HATASI');
            //return response()->json(['message' => $e->getMessage(), 'status' => false], 422);
            throw new CreditCardFieldsException($e->getMessage());
        }
    }

    public function complete3DRequest($payable, array $attributes = [])
    {
        $request = new \Iyzipay\Request\CreateThreedsPaymentRequest();
        $request->setLocale(\Iyzipay\Model\Locale::TR);
        $request->setConversationId($attributes['conversationId']);
        $request->setPaymentId($attributes['paymentId']);
        $request->setConversationData($attributes['conversationData']);


        $threedsPayment = \Iyzipay\Model\ThreedsPayment::create($request, self::options());
        Log::info('iyzico 3D payment request', [
            'request' => $request,
            'return' => $threedsPayment,
        ]);

        Cache::put('threedsPayment', $threedsPayment, 60 * 24 * 30);

        //        $threedsPayment = Cache::get('threedsPayment');

        if ($threedsPayment->getStatus() == 'success') {

            // Kullanıcı UserKey'ini güncelle
            //            $payable->iyzipay_key = $threedsPayment->getCardUserKey();
            //            $payable->save();


            // Iyzico 3D Request talebini güncelle
            $iyzico3DRequest = Iyzico3DRequest::where('token', $attributes['token'])
                ->first();

            $iyzico3DRequest->update([
                'status' => $attributes['status'],
                'conversation_id' => $attributes['conversationId'],
                'payment_id' => $attributes['paymentId'],
                'conversation_data' => $attributes['conversationData'],
                'md_status' => $attributes['mdStatus'],
            ]);

            // Kullanıcıya kart bilgilerini kaydet
            $creditCardModel = new CreditCard([
                //'alias' => $attributes['ccName'] ?? $threedsPayment->getCardFamily() . ' Kartım',
                'number' => $iyzico3DRequest->number,
                'token' => $threedsPayment->getCardToken(),
                'iyzipay_key' => $threedsPayment->getCardUserKey(),
                //'bank' => $threedsPayment->getCardBankName(), // .?
                'bin_number' => $threedsPayment->getBinNumber(), // 589004
                'holder' => $iyzico3DRequest->holder,
                'month' => $iyzico3DRequest->month,
                'year' => $iyzico3DRequest->year,
                'card_type' => $threedsPayment->getCardType(),
                'card_association' => $threedsPayment->getCardAssociation(),
                'card_family' => $threedsPayment->getCardFamily(),
                'service_name' => 'IYZICO',
                'service_env' => env('IYZIPAY_ENV'),
            ]);

            $payable->creditCards()->save($creditCardModel);

            // Iyzico 3D Request talebini güncelle
            $iyzico3DRequest = Iyzico3DRequest::where('token', $attributes['token'])
                ->first()
                ->update([
                    'status' => $attributes['status'],
                    'conversation_id' => $attributes['conversationId'],
                    'payment_id' => $attributes['paymentId'],
                    'conversation_data' => $attributes['conversationData'],
                    'md_status' => $attributes['mdStatus'],
                ]);

            return true;
        }

        return false;

        //    -rawResult: "{"status":"success","locale":"tr","systemTime":1680047917845,"conversationId":"123456789","price":1.00000000,"paidPrice":1.20000000,"installment":1,"paymentId":"19096930","fraudStatus":1,"merchantCommissionRate":20.00000000,"merchantCommissionRateAmount":0.20000000,"iyziCommissionRateAmount":0.04800000,"iyziCommissionFee":0.25000000,"cardType":"CREDIT_CARD","cardAssociation":"MASTER_CARD","cardFamily":"Paraf","cardToken":"ZcjyOFS2VNOjZP7MrjNejTsZym0=","cardUserKey":"L2COi19TFaBf0QmvWcnpbn1TwU4=","binNumber":"552879","lastFourDigits":"0008","basketId":"B67832","currency":"TRY","itemTransactions":[{"itemId":"BI101","paymentTransactionId":"20329850","transactionStatus":2,"price":0.30000000,"paidPrice":0.36000000,"merchantCommissionRate":20.00000000,"merchantCommissionRateAmount":0.06000000,"iyziCommissionRateAmount":0.01440000,"iyziCommissionFee":0.07500000,"blockageRate":0E-8,"blockageRateAmountMerchant":0E-8,"blockageRateAmountSubMerchant":0,"blockageResolvedDate":"2023-04-06 00:00:00","subMerchantPrice":0,"subMerchantPayoutRate":0E-8,"subMerchantPayoutAmount":0,"merchantPayoutAmount":0.27060000,"convertedPayout":{"paidPrice":0.36000000,"iyziCommissionRateAmount":0.01440000,"iyziCommissionFee":0.07500000,"blockageRateAmountMerchant":0E-8,"blockageRateAmountSubMerchant":0E-8,"subMerchantPayoutAmount":0E-8,"merchantPayoutAmount":0.27060000,"iyziConversionRate":0,"iyziConversionRateAmount":0,"currency":"TRY"}},{"itemId":"BI102","paymentTransactionId":"20329857","transactionStatus":2,"price":0.50000000,"paidPrice":0.60000000,"merchantCommissionRate":20.00000000,"merchantCommissionRateAmount":0.10000000,"iyziCommissionRateAmount":0.02400000,"iyziCommissionFee":0.12500000,"blockageRate":0E-8,"blockageRateAmountMerchant":0E-8,"blockageRateAmountSubMerchant":0,"blockageResolvedDate":"2023-04-06 00:00:00","subMerchantPrice":0,"subMerchantPayoutRate":0E-8,"subMerchantPayoutAmount":0,"merchantPayoutAmount":0.45100000,"convertedPayout":{"paidPrice":0.60000000,"iyziCommissionRateAmount":0.02400000,"iyziCommissionFee":0.12500000,"blockageRateAmountMerchant":0E-8,"blockageRateAmountSubMerchant":0E-8,"subMerchantPayoutAmount":0E-8,"merchantPayoutAmount":0.45100000,"iyziConversionRate":0,"iyziConversionRateAmount":0,"currency":"TRY"}},{"itemId":"BI103","paymentTransactionId":"20329858","transactionStatus":2,"price":0.20000000,"paidPrice":0.24000000,"merchantCommissionRate":20.00000000,"merchantCommissionRateAmount":0.04000000,"iyziCommissionRateAmount":0.00960000,"iyziCommissionFee":0.05000000,"blockageRate":0E-8,"blockageRateAmountMerchant":0E-8,"blockageRateAmountSubMerchant":0,"blockageResolvedDate":"2023-04-06 00:00:00","subMerchantPrice":0,"subMerchantPayoutRate":0E-8,"subMerchantPayoutAmount":0,"merchantPayoutAmount":0.18040000,"convertedPayout":{"paidPrice":0.24000000,"iyziCommissionRateAmount":0.00960000,"iyziCommissionFee":0.05000000,"blockageRateAmountMerchant":0E-8,"blockageRateAmountSubMerchant":0E-8,"subMerchantPayoutAmount":0E-8,"merchantPayoutAmount":0.18040000,"iyziConversionRate":0,"iyziConversionRateAmount":0,"currency":"TRY"}}],"authCode":"952169","phase":"AUTH","mdStatus":1,"hostReference":"mock00001iyzihostrfn"} ◀"
        //    -status: "success"
        //    -errorCode: null
        //    -errorMessage: null
        //    -errorGroup: null
        //    -locale: "tr"
        //    -systemTime: 1680047917845
        //    -conversationId: "123456789"
        //    -price: 1.0
        //    -paidPrice: 1.2
        //    -installment: 1
        //    -currency: "TRY"
        //    -paymentId: "19096930"
        //    -paymentStatus: null
        //    -fraudStatus: 1
        //    -merchantCommissionRate: 20.0
        //    -merchantCommissionRateAmount: 0.2
        //    -iyziCommissionRateAmount: 0.048
        //    -iyziCommissionFee: 0.25
        //    -cardType: "CREDIT_CARD"
        //    -cardAssociation: "MASTER_CARD"
        //    -cardFamily: "Paraf"
        //    -cardToken: "ZcjyOFS2VNOjZP7MrjNejTsZym0="
        //    -cardUserKey: "L2COi19TFaBf0QmvWcnpbn1TwU4="
        //    -binNumber: "552879"
        //    -basketId: "B67832"
        //    -paymentItems: array:3 [▶]
        //    -connectorName: null
        //    -authCode: "952169"
        //    -phase: "AUTH"
        //    -lastFourDigits: "0008"
        //    -posOrderId: null

    }

    public static function options()
    {
        $options = new \Iyzipay\Options();

        //dd(env('IYZIPAY_ENV'), env('IYZIPAY_ENV') === 'sandbox');
        if (env('IYZIPAY_ENV') === 'sandbox') {
            $options->setApiKey(env('SANDBOX_IYZIPAY_API_KEY'));
            $options->setSecretKey(env('SANDBOX_IYZIPAY_SECRET_KEY'));
            $options->setBaseUrl(env('SANDBOX_IYZIPAY_BASE_URL'));
        } else {
            $options->setApiKey(env('IYZIPAY_API_KEY'));
            $options->setSecretKey(env('IYZIPAY_SECRET_KEY'));
            $options->setBaseUrl(env('IYZIPAY_BASE_URL'));
        }

        return $options;
    }

    /**
     * Remove credit card for billable & payable model.
     *
     * @param CreditCard $creditCard
     *
     * @return bool
     * @throws CardRemoveException
     */
    public function removeCreditCard(CreditCard $creditCard): bool
    {
        $this->removeCardOnIyzipay($creditCard);
        $creditCard->delete();

        return true;
    }

    /**
     * @param $payable
     * @param Collection $products
     * @param $currency
     * @param $installment
     * @param bool $subscription
     *
     * @return Transaction $transactionModel
     * @throws TransactionSaveException
     */
    public function singlePayment($payable, CreditCard $cc, Collection $products, $currency, $installment, $subscription = false): Transaction
    {

        //        dd($payable->creditCards);
        $this->validateBillable($payable);
        //        $this->validateHasCreditCard($payable);

        $messages = []; // @todo imporove here
        //        foreach ($payable->creditCards as $creditCard) {
        //            try {
        //                $transaction = $this->createTransactionOnIyzipay(
        //                    $payable,
        //                    $creditCard,
        //                    compact('products', 'currency', 'installment'),
        //                    $subscription
        //                );
        //
        //                return $this->storeTransactionModel($transaction, $payable, $products, $creditCard);
        //            } catch (TransactionSaveException $e) {
        //                $messages[] = $creditCard->number . ': ' . $e->getMessage();
        //                continue;
        //            }
        //        }

        try {
            //$creditCard = $payable->creditCards->where('service_env', 'sandbox')->last();
            //dd($creditCard, $payable->creditCards);

            $transaction = $this->createTransactionOnIyzipay(
                $payable,
                $cc,
                compact('products', 'currency', 'installment'),
                $subscription
            );

            return $this->storeTransactionModel($transaction, $payable, $products, $cc);
        } catch (TransactionSaveException $e) {
            $messages[] = $cc->number . ': ' . $e->getMessage();
        }

        throw new TransactionSaveException(implode(', ', $messages));
    }

    /**
     * @param Transaction $transactionModel
     *
     * @return Transaction
     * @throws TransactionVoidException
     */
    public function void(Transaction $transactionModel): Transaction
    {
        $cancel = $this->createCancelOnIyzipay($transactionModel);

        $transactionModel->voided_at = Carbon::now();
        $refunds = $transactionModel->refunds;
        $refunds[] = [
            'type' => 'void',
            'amount' => $cancel->getPrice(),
            'iyzipay_key' => $cancel->getPaymentId()
        ];

        $transactionModel->refunds = $refunds;
        $transactionModel->save();

        return $transactionModel;
    }

    public function partitialRefund(Transaction $transactionModel, $amount): Transaction
    {
        $cancel = $this->createRefundOnIyzipay($transactionModel, $amount);

        $refunds = $transactionModel->refunds;
        $refunds[] = [
            'type' => 'void',
            'amount' => $cancel->getPrice(),
            'iyzipay_key' => $cancel->getPaymentId()
        ];

        $transactionModel->refunds = $refunds;
        $transactionModel->save();

        return $transactionModel;
    }

    /**
     * Initializing API options with the given credentials.
     */
    private function initializeApiOptions()
    {
        $this->apiOptions = new Options();
        $this->apiOptions->setBaseUrl(config('iyzipay.baseUrl'));
        $this->apiOptions->setApiKey(config('iyzipay.apiKey'));
        $this->apiOptions->setSecretKey(config('iyzipay.secretKey'));
    }

    /**
     * Check if api options has been configured successfully.
     *
     * @throws IyzipayAuthenticationException
     * @throws IyzipayConnectionException
     */
    private function checkApiOptions()
    {
        try {
            $check = ApiTest::retrieve($this->apiOptions);
        } catch (\Exception $e) {
            throw new IyzipayConnectionException();
        }

        if ($check->getStatus() != 'success') {
            throw new IyzipayAuthenticationException();
        }
    }

    /**
     * @param $payable
     *
     * @throws BillFieldsException
     */
    private function validateBillable($payable): void
    {
        if (!$payable->isBillable()) {
            throw new BillFieldsException();
        }
    }

    /**
     * @param $payable
     *
     * @throws PayableMustHaveCreditCardException
     */
    private function validateHasCreditCard($payable): void
    {
        if ($payable->creditCards->isEmpty()) {
            throw new PayableMustHaveCreditCardException();
        }
    }

    /**
     * @param Payment $transaction
     * @param $payable
     * @param Collection $products
     * @param CreditCard $creditCard
     *
     * @return Transaction
     */
    private function storeTransactionModel(
        Payment    $transaction,
        $payable,
        Collection $products,
        CreditCard $creditCard
    ): Transaction {
        $iyzipayProducts = [];
        foreach ($transaction->getPaymentItems() as $paymentItem) {
            $iyzipayProducts[] = [
                'iyzipay_key' => $paymentItem->getPaymentTransactionId(),
                'paidPrice' => $paymentItem->getPaidPrice(),
                'product' => $products->where(
                    $products[0]->getKeyName(),
                    $paymentItem->getItemId()
                )->first()->toArray()
            ];
        }

        $transactionModel = new Transaction([
            'amount' => $transaction->getPaidPrice(),
            'products' => $iyzipayProducts,
            'iyzipay_key' => $transaction->getPaymentId(),
            'currency' => $transaction->getCurrency()
        ]);

        $transactionModel->creditCard()->associate($creditCard);
        $payable->transactions()->save($transactionModel);

        return $transactionModel->fresh();
    }

    protected function getLocale(): string
    {
        return (config('app.locale') == 'tr') ? Locale::TR : Locale::EN;
    }

    protected function getOptions(): Options
    {
        return $this->apiOptions;
    }
}
