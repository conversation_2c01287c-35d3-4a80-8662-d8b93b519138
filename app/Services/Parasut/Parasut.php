<?php

namespace App\Services\Parasut;

use App\Jobs\createRentingInvoice;
use App\Jobs\createWaybill;
use App\Jobs\sendContractToParasut;
use App\Jobs\sendCustomerToParasutJob;
use App\Jobs\sendProductToParasutJob;
use App\Models\Order;
use App\Models\OrderTransaction;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Bus;
use yedincisenol\Parasut\Client;

class Parasut
{
    protected Client $client;
    public mixed $token;

    public function __construct()
    {
        // Get token from cache
        $this->token = cache('parasut_token', null);

        // If token is not in cache, get a new token
        if (!$this->token) {
            $this->getFreshTokenFromService();
        }
    }

    public function createWaybill(Collection $orderItems)
    {
        // Send products to parasut
        $sendProductToParasutJobs = $orderItems->map(function ($item) {
            return new sendProductToParasutJob($item->itemStock);
        });

        // Batch job for waybill
        $batch = Bus::batch($sendProductToParasutJobs)
            ->then(function () use ($orderItems) {
                Bus::chain([
                    new sendCustomerToParasutJob(Order::find($orderItems->first()->order->id)),
                    new createWaybill($orderItems),
                    //                    new createRentingInvoice($orderItems, $this->token),
                ])->dispatch();
                //                    ->then(function (Batch $batch) {
                //                    })
                //                    ->dispatch();
            })->dispatch();

        $orderItems->toQuery()->update(['waybill_batch' => $batch->id]);
        return $batch->id;
    }

    public function createRentingInvoice($orderTransaction, $delay = 0)
    {
        // Kiralama faturası düzenlemek için
        // 1. Önce parasuta ürünleri gönder
        // 2. Sonra parasuta müşteriyi gönder
        // 3. Sonra parasuta faturayı gönder
        logger()->channel('parasut')->info('createRentingInvoice chain started ' . $orderTransaction->order->order_number, [
            'orderTransaction' => $orderTransaction->id,
            'token' => $this->token,
        ]);

        Bus::chain([
            (new sendContractToParasut($orderTransaction->order)),
            (new sendCustomerToParasutJob($orderTransaction->order)),
            (new createRentingInvoice(OrderTransaction::find($orderTransaction->id))),
        ])->delay(now()->addMinutes($delay))->dispatch();
    }

    protected function getFreshTokenFromService(): void
    {
        // Get token from service
        $this->client = new Client();
        $this->client->login();
        $this->token = $this->client->getToken();

        // Save token to cache with expiration time given by the service
        cache(['parasut_token' => $this->token], now()->addSeconds($this->client->getExpiresAt() - now()->timestamp));
    }

    /**
     * Get token from cache or get a new token from service
     */
    public static function getToken(): string
    {
        // Get token from cache
        $token = cache('parasut_token', null);

        // If token is not in cache, get a new token
        if (!$token) {
            new self();
        }

        return cache()->get('parasut_token');
    }
}
