<?php

namespace App\Services;

use App\Jobs\SendScoringModerationEmailJob;
use App\Models\ScoringRequest;
use App\Models\ScoringResultNew;
use App\Models\ScoringLimit;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ScoringResultService
{
    /**
     * Skorlama sonucunu kaydeder ve scoring limit oluşturur
     *
     * @param ScoringRequest $scoringRequest
     * @param int|null $score
     * @param bool $isApproved
     * @param float $approvedAmount
     * @param string|null $findexJournalId
     * @param Carbon|null $processedAt
     * @param array|null $evaluationData Additional evaluation data (multiplier, moderation, etc.)
     * @return ScoringResultNew
     */
    public function createResult(
        ScoringRequest $scoringRequest,
        ?int $score,
        bool $isApproved,
        float $approvedAmount,
        ?string $findexJournalId = null,
        ?Carbon $processedAt = null,
        ?array $evaluationData = null
    ): ScoringResultNew {
        // Scoring request için daha önce result tanımı var mı kontrol et
        $existingResult = ScoringResultNew::where('scoring_request_id', $scoringRequest->id)->first();

        if ($existingResult) {
            Log::warning('Scoring result already exists for this request, returning existing result', [
                'scoring_request_id' => $scoringRequest->id,
                'existing_result_id' => $existingResult->id,
                'existing_score' => $existingResult->score,
                'existing_is_approved' => $existingResult->is_approved,
                'existing_approved_amount' => $existingResult->approved_amount,
                'existing_processed_at' => $existingResult->processed_at,
            ]);

            return $existingResult;
        }

        $result = DB::transaction(function () use (
            $scoringRequest,
            $score,
            $isApproved,
            $approvedAmount,
            $findexJournalId,
            $processedAt
        ) {
            // Create scoring result
            $result = ScoringResultNew::create([
                'scoring_request_id' => $scoringRequest->id,
                'score' => $score,
                'is_approved' => $isApproved,
                'approved_amount' => $approvedAmount,
                'processed_at' => $processedAt ?? now(),
            ]);

            // Findex journal ID'yi ekle (ScoringRequest'e)
            if ($findexJournalId) {
                $additionalData = $scoringRequest->additional_data ?? [];
                $additionalData['findex_journal_id'] = $findexJournalId;
                $scoringRequest->update(['additional_data' => $additionalData]);
            }

            // Create scoring limit if approved
            if ($isApproved && $score !== null) {
                $this->createScoringLimit($scoringRequest, $score, $approvedAmount);
            }

            return $result;
        });

        // Check if moderation is required and send notification (OUTSIDE transaction)
        Log::info('Checking moderation requirement', [
            'evaluationData' => $evaluationData,
            'requires_moderation' => $evaluationData['requires_moderation'] ?? false
        ]);

        if ($evaluationData && ($evaluationData['requires_moderation'] ?? false)) {
            $this->sendModerationNotification($scoringRequest, $evaluationData);
        }

        return $result;
    }

    /**
     * Create scoring limit for the user if exists
     *
     * @param ScoringRequest $scoringRequest
     * @param int $score
     * @param float $approvedAmount
     * @return void
     */
    protected function createScoringLimit(ScoringRequest $scoringRequest, int $score, float $approvedAmount): void
    {
        try {
            // Find user by TCKN
            $user = null;
            if (!empty($scoringRequest->tckn)) {
                $user = User::where('tckn', $scoringRequest->tckn)->first();
            }

            // Create scoring limit
            $scoringLimit = ScoringLimit::createFromScoringResult(
                $scoringRequest,
                $approvedAmount,
                $score,
                $user
            );

            Log::info('Scoring limit created', [
                'scoring_limit_id' => $scoringLimit->id,
                'scoring_request_id' => $scoringRequest->id,
                'tckn' => $scoringRequest->tckn,
                'user_id' => $user?->id,
                'score' => $score,
                'approved_limit' => $approvedAmount,
                'remaining_limit' => $approvedAmount,
                'valid_until' => $scoringLimit->valid_until
            ]);

            // If user exists, update their meta with current scoring limit ID
            if ($user) {
                $user->setCurrentScoringLimit($scoringLimit);

                Log::info('User meta updated with scoring limit', [
                    'user_id' => $user->id,
                    'scoring_limit_id' => $scoringLimit->id
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to create scoring limit', [
                'error' => $e->getMessage(),
                'scoring_request_id' => $scoringRequest->id,
                'tckn' => $scoringRequest->tckn
            ]);
            // Don't throw the exception to avoid failing the entire transaction
            // Scoring limit is supplementary, main scoring result should still be saved
        }
    }

    /**
     * Send moderation notification email
     *
     * @param ScoringRequest $scoringRequest
     * @param array $evaluationData
     * @return void
     */
    private function sendModerationNotification(ScoringRequest $scoringRequest, array $evaluationData): void
    {
        try {
            Log::info('sendModerationNotification called', [
                'config_enabled' => config('scoring.moderation.send_notifications'),
                'evaluation_data' => $evaluationData
            ]);

            if (!config('scoring.moderation.send_notifications', true)) {
                Log::info('Moderation notifications disabled in config');
                return;
            }

            SendScoringModerationEmailJob::dispatch($scoringRequest, $evaluationData)
                ->onQueue('emails');

            Log::info('Moderation notification queued from ScoringResultService', [
                'scoring_request_id' => $scoringRequest->id,
                'product_value' => $evaluationData['product_value'] ?? 0
            ]);
        } catch (\Exception $e) {
            // Don't fail the main process if email fails
            Log::error('Failed to queue moderation notification from ScoringResultService', [
                'scoring_request_id' => $scoringRequest->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
