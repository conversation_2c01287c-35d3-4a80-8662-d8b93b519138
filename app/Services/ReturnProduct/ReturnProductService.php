<?php

namespace App\Services\ReturnProduct;

use App\Jobs\sendSupportRequestToHelpScout;
use App\Models\Lunar\ProductVariant;
use App\Models\Order\Order;
use App\Models\SupportRequest;
use App\States\SupportRequest\SupportReceived;

class ReturnProductService
{
    private $delivered_at;
    private ?SupportRequest $supportRequest = null;
    private ProductVariant $productVariant;
    private Order $order;

    public function handle()
    {
        // if support not exists, create a new support request for return product
        if (!$this->supportRequest) $this->openSupportRequest(7, 'İade Ürün Talebi MHZ', cs_sp_status: \App\States\SupportRequestCustomerService\SupportCompleted::class);

        // Save returned product infos
        $this->supportRequest->saveReturnedProductInfos($this->delivered_at);

        // Send notification to the customer
        $this->order->orderItems()->where('product_id', $this->productVariant->id)->first()->sendProductReturnSmsAndEmail();

        // Send product to "Ara Depo" if it's suitable
        $orderItem = $this->order->orderItems()
            ->where('product_id', $this->productVariant->id)
            ->whereNull('cancelled_at')
            ->where('is_user_suitable_control', true)
            ->first();

        if ($orderItem) {
            $orderItem->addProductToInterInventoryStock();
        }
    }

    public function openSupportRequest($supportRequestType, $note, $status = SupportReceived::class, $cs_sp_status = \App\States\SupportRequestCustomerService\SupportReceived::class, $cargo_return_code = null): void
    {
        $this->supportRequest = SupportRequest::create([
            'product_type' => ProductVariant::class,
            'product_id' => $this->productVariant->id,
            'user_id' => auth()->id(),
            'support_requests_type_id' => $supportRequestType,
            'customer_message' => $note,
            'status' => $status,
            'cs_sp_status' => $cs_sp_status,
            'order_id' => $this->order->id,
            'cargo_return_code' => $cargo_return_code,
        ]);

        // send copy of support request to helpscout
        sendSupportRequestToHelpScout::dispatch($this->supportRequest);
    }

    public function getSupportRequest(): ?SupportRequest
    {
        return $this->supportRequest;
    }

    public function setSupportRequest(?SupportRequest $supportRequest): void
    {
        $this->supportRequest = $supportRequest;
    }

    public function getDeliveredAt()
    {
        return $this->delivered_at;
    }

    public function setDeliveredAt($delivered_at): void
    {
        $this->delivered_at = $delivered_at;
    }

    public function getProductVariant(): ?ProductVariant
    {
        return $this->productVariant;
    }

    public function setProductVariant(?ProductVariant $productVariant): void
    {
        $this->productVariant = $productVariant;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function setOrder(Order $order): void
    {
        $this->order = $order;
    }

}
