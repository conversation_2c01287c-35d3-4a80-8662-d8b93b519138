<?php

namespace App\Services\Hopi;

use App\Models\HopiCampaign;
use App\Models\Order\Order;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use SoapClient;
use SoapHeader;
use SoapVar;

class Hopi
{
    protected SoapClient $query;
    public string $xml;

    public function __construct()
    {
        $this->xml = <<<EOT
            <wsse:Security SOAP-ENV:mustUnderstand="1" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
                <wsse:UsernameToken>
                    <wsse:Username>%s</wsse:Username>
                    <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">%s</wsse:Password>
                </wsse:UsernameToken>
            </wsse:Security>
EOT;

        $this->xml = sprintf($this->xml, config('hopi.username'), config('hopi.password'));
        $this->query = new SoapClient(config('hopi.ep'), ['trace' => 1, 'cache_wsdl' => WSDL_CACHE_NONE]);
        $header = new SoapHeader('http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd', 'Security', new SoapVar($this->xml, XSD_ANYXML), true);
        $this->query->__setSoapHeaders($header);
    }

    /**
     * @param $token
     * @return mixed
     */
    public function GetBirdUserInfo($token)
    {
        try {
            //            return $this->query->GetBirdUserInfo([
            //                'merchantCode' => 'kiralamini',
            //                'storeCode' => 'kiralabunu',
            //                'token' => $token,
            //            ]);

            $res = $this->query->GetBirdUserInfo([
                'merchantCode' => 'kiralamini',
                'storeCode' => 'kiralabunu',
                'token' => $token,
            ]);

            hopi_logger('GetBirdUserInfoRequest XML', [$this->query->__getLastRequest()]);
            hopi_logger('GetBirdUserInfoResponse XML', [$this->query->__getLastResponse()]);
            return $res;
        } catch (Exception $e) {
            hopi_logger('GetBirdUserInfoRequest Exception', [$this->query->__getLastRequest()]);
            hopi_logger('GetBirdUserInfoResponse Exception', [$this->query->__getLastResponse()]);
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * @param $token
     * @return mixed
     */
    public function StartCoinTransactionRequest($birdId, $amount, $billAmount)
    {
        try {
            $res = $this->query->StartCoinTransaction([
                'merchantCode' => 'kiralamini',
                'storeCode' => 'kiralabunu',
                'birdId' => $birdId,
                'amount' => $amount,
                'billAmount' => $billAmount,
            ]);
            hopi_logger('HopiStartCoinTransactionRequestResponse', [$this->query->__getLastResponse()]);
            //            logger([$res]);
            //return simplexml_load_string($this->query->__getLastResponse());
            //            return [
            //                'provisionId' => Str::of($this->query->__getLastResponse())->betweenFirst('<ns2:provisionId>', '</ns2:provisionId>')->__toString(),
            //            ];
            return $res;
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    public function CompleteCoinTransactionRequest($provisionId)
    {
        try {
            $res = $this->query->CompleteCoinTransaction([
                'merchantCode' => 'kiralamini',
                'storeCode' => 'kiralabunu',
                'provisionId' => $provisionId,
            ]);
            hopi_logger('HopiCompleteCoinTransactionRequestResponse', [$this->query->__getLastRequest()]);
        } catch (Exception $e) {
            hopi_logger('HopiCompleteCoinTransactionRequestResponseException', [$this->query->__getLastRequest()]);
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    public function CancelCoinTransactionRequest($provisionId)
    {
        try {
            return $this->query->CancelCoinTransaction([
                'merchantCode' => 'kiralamini',
                'storeCode' => 'kiralabunu',
                'provisionId' => $provisionId,
            ]);
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    public function NotifyCheckoutRequest($birdId, $purchaseDate, $hopiTxID, $firstMonthAmount, $usedHopiBalance, $orderNumber, $selectedCampaign, $productList)
    {
        $payload = [
            'merchantCode' => 'kiralamini',
            'storeCode' => 'kiralabunu',
            'birdId' => $birdId,
            //            'cashDeskTag' => 001,
            'dateTime' => $purchaseDate,
            'transactionId' => $orderNumber,
            'paymentDetails' => [
                'percent' => 20,
                'amount' => $firstMonthAmount,
            ],
            'subtotalDetails' => [
                'percent' => 20,
                'amount' => $firstMonthAmount,
            ]
        ];

        if ($hopiTxID) {
            $payload['usedCoinDetails'] = [
                'provisionId' => $hopiTxID,
                'amount' => $usedHopiBalance,
            ];
        }

        if ($selectedCampaign && $selectedCampaign != "none") {
            $payload['usedCampaignDetails'] = [
                'campaignCode' => $selectedCampaign,
                'amountDetails' => [
                    'percent' => 20,
                    'amount' => $firstMonthAmount,
                ],
                'benefit' => [
                    //                    'discounts' => [ // Indirim kampanyası ise discounts, paracık kazanımı ise o zaman coins
                    //                        'percent' => 20,
                    //                        'amount' => $usedHopiBalance,
                    //                    ]
                    //                    'coins' => HopiCampaign::where('code', $selectedCampaign)->first()->coin($firstMonthAmount), // Kampanyadan kazanılması gereken paracık bu ksımdan
                    'coins' => HopiCampaign::coins($firstMonthAmount, $selectedCampaign), // Kampanyadan kazanılması gereken paracık bu ksımdan
                ]
            ];
        } else {
            $payload['campaignFreePaymentDetails'] = [
                'percent' => 20,
                'amount' => $firstMonthAmount,
            ];
        }

        foreach ($productList as $product) {
            $payload['transactionInfos'][] = [
                'barcode' => $product['barcode'],
                'quantity' => $product['quantity'],
                //'amount' => $firstMonthAmount,
                'amount' => $product['amount'],
                'campaign' => $selectedCampaign,
            ];
        }

        hopi_logger('NotifyCheckoutRequest Payload', $payload);

        try {
            $res = $this->query->NotifyCheckout($payload);
            hopi_logger('NotifyCheckoutRequest XML', [$this->query->__getLastRequest()]);
            hopi_logger('NotifyCheckoutResponse XML', [$this->query->__getLastResponse()]);
            return $res;
        } catch (Exception $e) {
            hopi_logger('NotifyCheckoutRequest Exception', [$this->query->__getLastRequest()]);
            hopi_logger('NotifyCheckoutResponse Exception', [$this->query->__getLastResponse()]);
            return [
                'status' => 'error',
                'message' => $e->getMessage() . ' ' . $this->query->__getLastResponse()
            ];
        }
    }

    public function StartReturnTransactionRequest(Order $order, Collection $orderItemsToReturn)
    {
        $payload = [
            'merchantCode' => 'kiralamini',
            'storeCode' => 'kiralabunu',
            'transactionId' => $order->order_number,
        ];

        $campaign = $order->meta()->where('key', 'hopi_selected_campaign')->first();
        if ($campaign->value && $campaign->value != 'none') {
            $payload['returnCampaignDetails'] = [
                'campaignCode' => $campaign->value,
                'returnPayment' => $order->orderTransactions()->withTrashed()->first()->amount, // ? İptaller ilk başta olacağından dolayı ilk ödeme planı kadar iade olacak
            ];
        } else {
            $payload['campaignFreeAmount'] = $order->orderTransactions()->withTrashed()->first()->amount; // ? İptaller ilk başta olacağından dolayı ilk ödeme planı kadar iade olacak
        }

        foreach ($orderItemsToReturn as $item) {
            $payload['transactionInfos'][] = [
                'barcode' => $item->product_id,
                'quantity' => $item->quantity,
                'amount' => $item->total,
                'campaign' => $campaign->value,
            ];
        }

        try {
            $res = $this->query->StartReturnTransaction($payload);
            hopi_logger('HopiStartReturnTransactionRequest', [$this->query->__getLastRequest()]);
            hopi_logger('HopiStartReturnTransactionResponse', [$this->query->__getLastResponse()]);
            return $res;
        } catch (\Exception $e) {
            hopi_logger('HopiStartReturnTransactionRequestResponseException', [$this->query->__getLastRequest()]);
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    public function CompleteReturnTransactionRequest($provisionId)
    {
        try {
            $res = $this->query->CompleteReturnTransaction([
                'merchantCode' => 'kiralamini',
                'storeCode' => 'kiralabunu',
                'returnTrxId' => $provisionId,
            ]);
            hopi_logger('HopiCompleteReturnTransactionResponse', [$this->query->__getLastRequest()]);
        } catch (Exception $e) {
            hopi_logger('HopiCompleteReturnTransactionRequestResponseException', [$this->query->__getLastRequest()]);
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    public function RefundCoinRequest($provisionID, $usedCoin)
    {
        try {
            $res = $this->query->RefundCoin([
                'merchantCode' => 'kiralamini',
                'storeCode' => 'kiralabunu',
                'provisionId' => $provisionID,
                'amount' => $usedCoin,
            ]);
            hopi_logger('HopiRefundCoinRequest', [$this->query->__getLastRequest()]);
        } catch (Exception $e) {
            hopi_logger('HopiRefundCoinRequestResponseException', [$this->query->__getLastRequest()]);
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }
}
