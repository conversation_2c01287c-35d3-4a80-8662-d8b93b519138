<?php

namespace App\Services\Cargo;

use App\Models\Order\OrderItem;
use App\States\Order\OrderRenting;

class UpdateOrderStatusByCargo
{

    private OrderItem $orderItem;

    public function __construct(OrderItem $orderItem)
    {
        $this->orderItem = $orderItem;
    }

    public function updateOrderStatusIfSuitable(): bool
    {
        $approvedTotal = $this->orderItem->order->items->filter(function ($item) {
            return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1 && $item->cancelled_at == null;
        })->count();

        $shipped = $this->orderItem->order->items->filter(function ($item) {
            return $item->payment_control == 1 && $item->findex_control == 1 && $item->is_user_suitable_control == 1 && $item->cancelled_at == null && $item->cargo_at != null;
        })->count();

        if ($approvedTotal == $shipped) {
            $this->orderItem->order()->update([
                'status' => OrderRenting::class,
            ]);
            return true;
        }

        return false;
    }
}
