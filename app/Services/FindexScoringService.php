<?php

namespace App\Services;

use DOMDocument;
use DOMXPath;
use Exception;
use Illuminate\Support\Facades\Log;

class FindexScoringService
{
    /**
     * Skor hesaplama ağırlıkları
     */
    private array $weights = [
        'credit_mix' => 0.05,                    // %5
        'credit_history_length' => 0.10,         // %10
        'credit_card_payment_success' => 0.30,   // %30
        'consumer_loan_payment_success' => 0.20, // %20
        'overdraft_payment_success' => 0.10,     // %10
        'credit_card_ratio' => 0.10,             // %10
        'kmh_ratio' => 0.05,                     // %5
        'consumer_loan_ratio' => -0.05,          // -%5
        'credit_card_limit_ratio' => 0.10       // %10
    ];

    /**
     * Risk kategorileri
     */
    private const RISK_CATEGORIES = [
        ['min' => 0, 'max' => 175, 'category' => 'EN RİSKLİ', 'description' => 'Kiralama önerilmez'],
        ['min' => 176, 'max' => 275, 'category' => 'RİSKLİ', 'description' => '<PERSON><PERSON><PERSON> belge<PERSON> istenmeli'],
        ['min' => 276, 'max' => 375, 'category' => 'ORTA RİSKLİ', 'description' => 'Standart değerlendirme'],
        ['min' => 376, 'max' => 450, 'category' => 'AZ RİSKLİ', 'description' => 'Düşük risk'],
        ['min' => 451, 'max' => 500, 'category' => 'İYİ', 'description' => 'Minimum risk']
    ];

    /**
     * Findex HTML verisinden skorlama yap
     */
    public function calculateScoreFromHtml(string $htmlContent): array
    {
        try {
            // HTML'den verileri çıkar
            $extractedData = $this->extractDataFromHtml($htmlContent);
            // dd($extractedData);

            // Log extracted data
            Log::info('Findeks HTML verisinden çıkarılan parametreler:', $extractedData);

            // Skorlama parametrelerini hesapla
            $scoringParams = $this->calculateScoringParameters($extractedData);

            // Log calculated parameters
            Log::info('Hesaplanan skorlama parametreleri:', $scoringParams);

            // Final skoru hesapla
            $finalScore = $this->calculateFinalScore($scoringParams, $extractedData);

            // Kira aralığını hesapla
            $rentRange = $this->calculateRentRange(
                $finalScore['score'],
                $extractedData['total_limit'],
                $extractedData['total_borc']
            );

            // Risk kategorisini belirle
            $riskCategory = $this->getRiskCategory($finalScore['score']);

            // Sonuç array'i oluştur
            $result = [
                'score' => $finalScore['score'],
                'score_details' => $finalScore['details'],
                'risk_category' => $riskCategory,
                'rent_range' => $rentRange,
                'extracted_data' => $extractedData,
                'scoring_parameters' => $scoringParams,
                'weights_used' => $finalScore['weights']
            ];

            // Log final result
            Log::info('Findeks skorlama sonucu:', $result);

            return $result;
        } catch (Exception $e) {
            Log::error('Findeks skorlama hatası: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * HTML'den veri çıkar
     */
    private function extractDataFromHtml(string $htmlContent): array
    {
        $dom = new DOMDocument();
        // HTML encoding issues için UTF-8 wrapper ekle
        $htmlContent = '<?xml encoding="UTF-8">' . $htmlContent;
        @$dom->loadHTML($htmlContent, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $xpath = new DOMXPath($dom);

        // Temel bilgileri çıkar
        $raporBilgileri = $xpath->query("//div[@class='rapor-bilgisi']");

        $data = [
            'limitler_toplami' => $raporBilgileri->length > 0 ? trim($raporBilgileri->item(0)->textContent) : '0',
            'borclar_toplami' => $raporBilgileri->length > 1 ? trim($raporBilgileri->item(1)->textContent) : '0',
            'kredili_urunler' => $raporBilgileri->length > 2 ? intval(trim($raporBilgileri->item(2)->textContent)) : 0,

            // Bireysel Krediler tablosu
            'calısılan_kurum_sayisi' => $this->extractTableValue($xpath, 'Çalışılan Kurum Sayısı'),
            'son_kredi_kullanim_tarihi' => $this->extractTableValue($xpath, 'Son Kredi Kullanım Tarihi'),
            'borc_limit_orani' => $this->extractPercentage($xpath, 'Borç / Limit Oranı'),
            'en_olumsuz_durum' => $this->extractTableValue($xpath, 'Ödeme Tarihçesindeki En Olumsuz Durum'),
            'gecikmedeki_hesap_sayisi' => $this->extractTableValue($xpath, 'Gecikmedeki Hesap Sayısı'),
            'gecikmedeki_bakiye' => $this->extractTableValue($xpath, 'Gecikmedeki Bakiye Toplamı'),
            'mevcut_en_uzun_gecikme' => $this->extractTableValue($xpath, 'Mevcut En Uzun Gecikme Süresi'),
            'bildirimde_bulunan_kurum' => $this->extractTableValue($xpath, 'Bildirimde Bulunan Finans Kuruluşu Sayısı'),
            'varlik_yonetim_borc' => $this->extractAmount($xpath, "//th[contains(text(),'Varlık Yönetim')]/following-sibling::td"),

            // Takibe alınmış krediler
            'takibe_alinmis_kredi_sayisi' => $this->extractTableValue($xpath, 'Toplam Takibe Alınmış Kredi Sayısı'),
            'takibe_alinmis_bakiye' => $this->extractAmount($xpath, "//th[contains(text(),'Takibe Alınmış Kredilerin Bakiye Toplamı')]/following-sibling::td"),

            // Kredi detayları
            'kredi_kartlari' => $this->extractCreditDetails($xpath, 'Kredi Kartları'),
            'tuketici_kredileri' => $this->extractCreditDetails($xpath, 'Tüketici Kredileri'),
            'konut_kredisi' => $this->extractCreditDetails($xpath, 'Konut Kredisi'),
            'kmh' => $this->extractCreditDetails($xpath, 'Kredili Mevduat Hesabı'),

            // Hesap geçmişi için en eski hesap tarihini bul
            'en_eski_hesap_tarihi' => $this->findOldestAccountDate($xpath)
        ];

        // Ödeme başarı oranlarını hesapla
        $data['kredi_karti_odeme_basarisi'] = $this->calculatePaymentSuccess($xpath, 'Kredi Kartları');
        $data['tuketici_kredisi_odeme_basarisi'] = $this->calculatePaymentSuccess($xpath, 'Tüketici Kredileri');
        $data['kmh_odeme_basarisi'] = $this->calculatePaymentSuccess($xpath, 'Kredili Mevduat Hesabı');

        // Toplam limit ve borç hesapla
        $data['total_limit'] = floatval(str_replace(['.', ',', 'TL', ' '], ['', '.', '', ''], $data['limitler_toplami']));
        $data['total_borc'] = floatval(str_replace(['.', ',', 'TL', ' '], ['', '.', '', ''], $data['borclar_toplami']));

        return $data;
    }

    /**
     * Skorlama parametrelerini hesapla
     */
    private function calculateScoringParameters(array $data): array
    {
        // Kredi karması (0-1 arası normalize)
        $creditMix = min(max($data['kredili_urunler'] / 20, 0), 1);

        // Kredi geçmişi uzunluğu (yıl cinsinden)
        $creditHistoryLength = $this->calculateCreditHistoryLength($data['en_eski_hesap_tarihi']);
        $normalizedHistoryLength = min(max($creditHistoryLength / 20, 0), 1);

        // Ödeme başarı oranları (zaten 0-1 arasında)
        $ccPaymentSuccess = $data['kredi_karti_odeme_basarisi'] / 100;
        $clPaymentSuccess = $data['tuketici_kredisi_odeme_basarisi'] / 100;
        $odPaymentSuccess = $data['kmh_odeme_basarisi'] / 100;

        // Kullanım oranları hesapla
        $availableLimit = max($data['total_limit'] - $data['total_borc'], 1); // 0'a bölmeyi önle

        $ccRatio = 0;
        $kmhRatio = 0;
        $clRatio = 0;
        $ccLimitRatio = 0;

        if ($availableLimit > 0) {
            // Kredi kartı kullanım oranı
            $ccAvailable = ($data['kredi_kartlari']['limit'] ?? 0) - ($data['kredi_kartlari']['borc'] ?? 0);
            $ccRatio = $ccAvailable / $availableLimit;

            // KMH kullanım oranı
            $kmhAvailable = ($data['kmh']['limit'] ?? 0) - ($data['kmh']['borc'] ?? 0);
            $kmhRatio = $kmhAvailable / $availableLimit;

            // Tüketici kredisi kullanım oranı
            $clAvailable = ($data['tuketici_kredileri']['limit'] ?? 0) - ($data['tuketici_kredileri']['borc'] ?? 0);
            $clRatio = $clAvailable / $availableLimit;
        }

        // Kredi kartı limit oranı (toplam limite göre)
        if ($data['total_limit'] > 0) {
            $ccLimitRatio = ($data['kredi_kartlari']['limit'] ?? 0) / $data['total_limit'];
        }

        return [
            'credit_mix' => $creditMix,
            'credit_history_length' => $normalizedHistoryLength,
            'credit_card_payment_success' => $ccPaymentSuccess,
            'consumer_loan_payment_success' => $clPaymentSuccess,
            'overdraft_payment_success' => $odPaymentSuccess,
            'credit_card_ratio' => $ccRatio,
            'kmh_ratio' => $kmhRatio,
            'consumer_loan_ratio' => $clRatio,
            'credit_card_limit_ratio' => $ccLimitRatio,
            'raw_history_years' => $creditHistoryLength
        ];
    }

    /**
     * Final skoru hesapla
     */
    private function calculateFinalScore(array $params, array $extractedData): array
    {
        // Ağırlıkları kopyala
        $weights = $this->weights;

        // Dinamik ağırlık ayarlaması
        $hasConsumerLoan = ($extractedData['tuketici_kredileri']['limit'] ?? 0) > 0;
        $hasKmh = ($extractedData['kmh']['limit'] ?? 0) > 0;

        if (!$hasConsumerLoan) {
            $weights['credit_card_payment_success'] += 0.10;
            $weights['overdraft_payment_success'] += 0.10;
            $weights['consumer_loan_payment_success'] = 0;
        }

        if (!$hasKmh) {
            $weights['credit_card_payment_success'] += 0.05;
            $weights['consumer_loan_payment_success'] += 0.05;
            $weights['overdraft_payment_success'] = 0;
        }

        // Temel skor hesaplama
        $score = 0;
        $details = [];

        foreach ($weights as $key => $weight) {
            $value = $params[$key] ?? 0;
            $contribution = $value * $weight;
            $score += $contribution;

            $details[$key] = [
                'value' => $value,
                'weight' => $weight,
                'contribution' => $contribution
            ];
        }

        // Özel durumlar
        $penalties = [];

        // Kanuni takip cezası
        $hasLegalFollowup = $this->hasLegalFollowup($extractedData);
        if ($hasLegalFollowup) {
            $score *= 0.90;
            $penalties[] = 'Kanuni takip cezası (%10)';
        }

        // Konut kredisi bonusu
        if (($extractedData['konut_kredisi']['limit'] ?? 0) > 0) {
            $score *= 1.02;
            $penalties[] = 'Konut kredisi bonusu (%2)';
        }

        // 0-500 aralığına ölçekle
        $finalScore = min($score * 500, 500);

        return [
            'score' => round($finalScore, 2),
            'raw_score' => $score,
            'details' => $details,
            'penalties' => $penalties,
            'weights' => $weights
        ];
    }

    /**
     * Kira aralığını hesapla
     */
    private function calculateRentRange(float $score, float $totalLimit, float $totalBorc): array
    {
        if ($score <= 175) {
            return ['min' => 0, 'max' => 0, 'recommended' => 0];
        }

        $scoreRatio = $score / 500;
        $availableLimit = $totalLimit - $totalBorc;
        $rentAmount = ($availableLimit * $scoreRatio) / 18;

        if ($rentAmount < 150) {
            return ['min' => 0, 'max' => 0, 'recommended' => 0];
        }

        // $minRent = intval($rentAmount * 0);
        $maxRent = intval($rentAmount * 1.2);

        return [
            'min' => 0,
            'max' => $maxRent,
            'recommended' => intval($rentAmount)
        ];
    }

    /**
     * Risk kategorisini belirle
     */
    private function getRiskCategory(float $score): array
    {
        foreach (self::RISK_CATEGORIES as $category) {
            if ($score >= $category['min'] && $score <= $category['max']) {
                return $category;
            }
        }

        return self::RISK_CATEGORIES[0]; // Varsayılan en riskli
    }

    /**
     * Kanuni takip kontrolü
     */
    private function hasLegalFollowup(array $data): bool
    {
        $worstCase = $data['en_olumsuz_durum'] ?? '';

        // Kanuni takip veya 4+ ödeme gecikmesi
        if (stripos($worstCase, 'Kanuni Takip') !== false) {
            return true;
        }

        // En uzun gecikme süresi 4 ay ve üzeri
        $longestDelay = intval($data['mevcut_en_uzun_gecikme'] ?? 0);
        if ($longestDelay >= 4) {
            return true;
        }

        return false;
    }

    /**
     * Kredi geçmişi uzunluğunu hesapla (yıl)
     */
    private function calculateCreditHistoryLength(?string $oldestDate): float
    {
        if (!$oldestDate) {
            return 0;
        }

        try {
            $oldest = \DateTime::createFromFormat('d/m/Y', $oldestDate);
            if (!$oldest) {
                return 0;
            }

            $now = new \DateTime();
            $diff = $now->diff($oldest);

            return $diff->y + ($diff->m / 12);
        } catch (Exception) {
            return 0;
        }
    }

    /**
     * XPath ile metin çıkar
     */
    private function extractTableValue(DOMXPath $xpath, string $label): ?string
    {
        $query = "//th[contains(text(), '$label')]/following-sibling::td";
        $nodes = $xpath->query($query);

        if ($nodes->length > 0) {
            return trim($nodes->item(0)->textContent);
        }

        return null;
    }

    /**
     * Tutar çıkar
     */
    private function extractAmount(DOMXPath $xpath, string $query): string
    {
        $nodes = $xpath->query($query);

        if ($nodes->length > 0) {
            $text = trim($nodes->item(0)->textContent);
            // Sadece TL ve boşlukları kaldır, sayıları koru
            return preg_replace('/[^\d.,\-]/', '', $text);
        }

        return '0';
    }

    /**
     * Sayı çıkar
     */
    private function extractNumber(DOMXPath $xpath, string $query): int
    {
        $nodes = $xpath->query($query);

        if ($nodes && $nodes->length > 0) {
            $text = trim($nodes->item(0)->textContent);
            // Sadece sayıları al
            return intval(preg_replace('/[^0-9]/', '', $text));
        }

        return 0;
    }

    /**
     * Yüzde çıkar
     */
    private function extractPercentage(DOMXPath $xpath, string $label): float
    {
        $value = $this->extractTableValue($xpath, $label);
        if ($value) {
            return floatval(str_replace('%', '', $value));
        }

        return 0;
    }

    /**
     * Kredi detaylarını çıkar
     */
    private function extractCreditDetails(DOMXPath $xpath, string $creditType): array
    {
        // Accordion header'dan limit ve borç bilgilerini al
        $query = "//div[contains(text(), '$creditType')]/parent::div/div[contains(@class,'col-xs-3')]";
        $nodes = $xpath->query($query);

        $limit = 0;
        $borc = 0;

        if ($nodes && $nodes->length >= 2) {
            $limitText = trim($nodes->item(0)->textContent);
            $borcText = trim($nodes->item(1)->textContent);

            // TL ve noktalama temizliği
            $limit = $this->parseAmount($limitText);
            $borc = $this->parseAmount($borcText);
        }

        return [
            'limit' => $limit,
            'borc' => $borc,
            'exists' => $limit > 0
        ];
    }

    /**
     * Tutar parse et
     */
    private function parseAmount(string $text): float
    {
        // Önce sadece sayı, nokta ve virgül bırak
        $cleaned = preg_replace('/[^\d.,]/', '', $text);

        // Boşsa 0 döndür
        if (empty($cleaned)) {
            return 0;
        }

        // Türkiye formatını düzenle:
        // Örnek: 24.600 -> 24600, 24.600,50 -> 24600.50

        // Eğer virgül varsa, bu ondalık ayıraçtır
        if (strpos($cleaned, ',') !== false) {
            // Son virgülü bulup noktaya çevir
            $lastCommaPos = strrpos($cleaned, ',');
            $cleaned = substr($cleaned, 0, $lastCommaPos) . '.' . substr($cleaned, $lastCommaPos + 1);
        }

        // Tüm nokta ve virgülleri kaldır (ondalık nokta hariç)
        // Son nokta ondalık noktası olabilir
        $parts = explode('.', $cleaned);
        if (count($parts) > 1) {
            $decimal = array_pop($parts);
            $whole = str_replace(',', '', implode('', $parts));
            $cleaned = $whole . '.' . $decimal;
        } else {
            $cleaned = str_replace(',', '', $cleaned);
        }

        return floatval($cleaned);
    }

    /**
     * En eski hesap tarihini bul
     */
    private function findOldestAccountDate(DOMXPath $xpath): ?string
    {
        $query = "//td[text()='Açılış Tarihi:']/following-sibling::td[1]";
        $nodes = $xpath->query($query);

        $oldestDate = null;
        $oldestTimestamp = PHP_INT_MAX;

        foreach ($nodes as $node) {
            $dateStr = trim($node->textContent);
            if ($dateStr && $dateStr !== '-') {
                $date = \DateTime::createFromFormat('d/m/Y', $dateStr);
                if ($date && $date->getTimestamp() < $oldestTimestamp) {
                    $oldestTimestamp = $date->getTimestamp();
                    $oldestDate = $dateStr;
                }
            }
        }

        return $oldestDate;
    }

    /**
     * Ödeme başarı oranını hesapla
     */
    private function calculatePaymentSuccess(DOMXPath $xpath, string $creditType): float
    {
        // İlgili kredi tipinin accordion içeriğini bul
        $query = "//div[contains(text(), '$creditType')]/parent::div/following-sibling::div//ul[@class='durum']/li";
        $nodes = $xpath->query($query);

        if ($nodes->length === 0) {
            return 100; // Veri yoksa varsayılan olarak %100 başarılı kabul et
        }

        $totalMonths = 0;
        $successfulMonths = 0;

        foreach ($nodes as $node) {
            if ($node instanceof \DOMElement) {
                $class = $node->getAttribute('class');
                $totalMonths++;

                if ($class === 'basarili') {
                    $successfulMonths++;
                }
            }
        }

        if ($totalMonths === 0) {
            return 100;
        }

        return ($successfulMonths / $totalMonths) * 100;
    }
}
