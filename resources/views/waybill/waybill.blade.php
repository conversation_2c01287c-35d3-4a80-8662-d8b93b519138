<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <META http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style type="text/css">
        #mainbody {
            background-color: #FFFFFF;
            font-family: '<PERSON>hom<PERSON>', "Times New Roman", Times, serif;
            font-size: 11px;
            color: #666666;
        }

        #mainbody h1,
        #mainbody h2 {
            padding-bottom: 3px;
            padding-top: 3px;
            margin-bottom: 5px;
            text-transform: uppercase;
            font-family: Arial, Helvetica, sans-serif;
        }

        #mainbody h1 {
            font-size: 1.4em;
            text-transform: none;
        }

        #mainbody h2 {
            font-size: 1em;
            color: brown;
        }

        #mainbody h3 {
            font-size: 1em;
            color: #333333;
            text-align: justify;
            margin: 0;
            padding: 0;
        }

        #mainbody h4 {
            font-size: 1.1em;
            font-style: bold;
            font-family: Arial, Helvetica, sans-serif;
            color: #000000;
            margin: 0;
            padding: 0;
        }

        #mainbody hr {
            height: 2px;
            color: #000000;
            background-color: #000000;
            border-bottom: 1px solid #000000;
        }

        #mainbody p,
        #mainbody ul,
        #mainbody ol {
            margin-top: 1.5em;
        }

        #mainbody ul,
        #mainbody ol {
            margin-left: 3em;
        }

        #mainbody blockquote {
            margin-left: 3em;
            margin-right: 3em;
            font-style: italic;
        }

        #mainbody a {
            text-decoration: none;
            color: #70A300;
        }

        #mainbody a:hover {
            border: none;
            color: #70A300;
        }

        #despatchTable {
            border-collapse: collapse;
            font-size: 11px;
            float: right;
            border-color: gray;
        }

        #ettnTable {
            border-collapse: collapse;
            font-size: 11px;
            border-color: gray;
        }

        #customerPartyTable {
            border-width: 0px;
            border-spacing: ;
            border-style: inset;
            border-color: gray;
            border-collapse: collapse;
            background-color:
        }

        #customerIDTable {
            border-width: 2px;
            border-spacing: ;
            border-style: inset;
            border-color: gray;
            border-collapse: collapse;
            background-color:
        }

        #customerIDTableTd {
            border-width: 2px;
            border-spacing: ;
            border-style: inset;
            border-color: gray;
            border-collapse: collapse;
            background-color:
        }

        #lineTable {
            border-width: 0px;
            border-spacing: ;
            border-style: inset;
            border-color: black;
            border-collapse: collapse;
            background-color: ;
        }

        #mainbody td.lineTableTd {
            border-width: 0px;
            padding: 1px;
            border-style: inset;
            border-color: black;
            background-color: white;
        }

        #mainbody tr.lineTableTr {
            border-width: 0px;
            padding: 0px;
            border-style: inset;
            border-color: black;
            background-color: white;
            -moz-border-radius: ;
        }

        #lineTableDummyTd {
            border-width: 1px;
            border-color: white;
            padding: 1px;
            border-style: inset;
            border-color: black;
            background-color: white;
        }

        #mainbody td.lineTableBudgetTd {
            border-width: 2px;
            border-spacing: 0px;
            padding: 1px;
            border-style: inset;
            border-color: black;
            background-color: white;
            -moz-border-radius: ;
        }

        #notesTable {
            border-width: 2px;
            border-spacing: ;
            border-style: inset;
            border-color: black;
            border-collapse: collapse;
            background-color:
        }

        #notesTableTd {
            border-width: 0px;
            border-spacing: ;
            border-style: inset;
            border-color: black;
            border-collapse: collapse;
            background-color:
        }

        #mainbody table {
            border-spacing: 0px;
        }

        #budgetContainerTable {
            border-width: 0px;
            border-spacing: 0px;
            border-style: inset;
            border-color: black;
            border-collapse: collapse;
            background-color: ;
        }

        #mainbody td {
            border-color: gray;
        }</style>
    <title>e-Belge</title></head>
<body style="margin-left=0.6in; margin-right=0.6in; margin-top=0.79in; margin-bottom=0.79in" id="mainbody">
<table cellpadding="0px" width="800" cellspacing="0px" border="0" style="border-color:blue; ">
    <tbody>
    <tr valign="top" style="height: 250px">
        <td width="40%"><br>
        </td>
        <td valign="middle" align="center" width="20%"><br><br>
        </td>
        <td width="5%"></td>
        <td align="right">
            <div id="qrcode" style="height: 150px">
                {!!  DNS1D::getBarcodeSVG( Str::of($orderItem->order->id)->padLeft(7, 0)->prepend('5')->append($orderItem->id) , 'C39', 2, 150)!!}
            </div>
            <div style="visibility: hidden; height: 20px;width: 20px; ; display:none" id="qrvalue">
            </div>
        </td>
    </tr>
    <tr valign="top" style="height:118px; ">
        <td valign="bottom" align="right" width="40%">
            <table border="0" align="left" id="customerPartyTable" style="margin-left: 30px">
                <tbody>
                <tr style="height:71px; ">
                    <td>
                        <table border="0" align="center">
                            <tbody>
                            {{--                            <tr>--}}
                            {{--                                <td align="left" style="width:469px; "><span style="font-weight:bold; ">SAYIN</span></td>--}}
                            {{--                            </tr>--}}
                            <tr>
                                <td align="left" style="width:469px; ">{{$orderItem->order->user->full_name_db}}<br></td>
                            </tr>
                            <tr>
                                <td align="left" style="width:469px; ">{{$orderItem->order->user->address->address}} {{$orderItem->order->user->address->county . ' ' . $orderItem->order->user->address->city}}</td>
                            </tr>
                            <tr align="left">
                                <td align="left" style="width:469px; ">Tel: {{$orderItem->order->user->phone}}</td>
                            </tr>
                            @if(false)
                                <tr align="left">
                                    <td>Vergi Dairesi: KADIK&Ouml;Y</td>
                                </tr>
                                <tr align="left">
                                    <td>VKN: 8900228392</td>
                                </tr>
                            @else
                                <tr align="left">
                                    <td>TCKN: {{$orderItem->order->user->tckn}}</td>
                                </tr>
                            @endif
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>
            <br></td>
        <td align="right" width="20%"></td>
        <td colspan="2" valign="bottom" align="center" width="40%">
            <table id="despatchTable" border="0">
                <tbody>
                <tr style="height:13px; ">
                    <td align="left"></td>
                    <td align="right">{{ today()->format('d.m.Y') }}</td>
                </tr>
                <tr style="height:13px; ">
                    <td align="left"></td>
                    <td align="right">{{ today()->format('d.m.Y') }}</td>
                </tr>
                </tbody>
            </table>
        </td>
    </tr>
    <tr align="left">
        <td id="ettnTable" valign="top" align="left"></td>
    </tr>
    </tbody>
</table>
<div id="lineTableAligner"><span>&nbsp;</span></div>
<table width="800" id="lineTable" border="0" style="margin-top: 100px; margin-left: 30px">
    <tbody>
    <tr class="lineTableTr">
        <td>&nbsp;</td>
        {{--        <td align="center" style="width:3%" class="lineTableTd"><span style="font-weight:bold;">Sıra No</span></td>--}}
        {{--        <td align="center" style="width:20%" class="lineTableTd"><span style="font-weight:bold;">Mal Hizmet</span></td>--}}
        {{--        <td align="center" style="width:17%; " class="lineTableTd"><span style="font-weight:bold;">Diğer Vergiler</span></td>--}}
        {{--        <td align="center" style="width:10.6%" class="lineTableTd"><span style="font-weight:bold;">Mal Hizmet Tutarı</span></td>--}}
    </tr>
    <tr class="lineTableTr">
        <td class="lineTableTd" style="width:13%">&nbsp;1</td>
        <td class="lineTableTd" style="width:20%">{{ $orderItem->product->product->getName }}
            <br>{{ $orderItem->itemStock->sn }}</td>
        <td align="right" class="lineTableTd">&nbsp;</td>
        <td align="right" class="lineTableTd">&nbsp;</td>
    </tr>
    {{--    <tr class="lineTableTr">--}}
    {{--        <td class="lineTableTd">&nbsp;</td>--}}
    {{--    </tr>--}}
    {{--    <tr class="lineTableTr">--}}
    {{--        <td class="lineTableTd">&nbsp;</td>--}}
    {{--    </tr>--}}
    {{--    <tr class="lineTableTr">--}}
    {{--        <td class="lineTableTd">&nbsp;</td>--}}
    {{--    </tr>--}}
    {{--    <tr class="lineTableTr">--}}
    {{--        <td class="lineTableTd">&nbsp;</td>--}}
    {{--    </tr>--}}
    {{--    <tr class="lineTableTr">--}}
    {{--        <td class="lineTableTd">&nbsp;</td>--}}
    {{--    </tr>--}}
    </tbody>
</table>
<table width="800px" table-layout="fixed" id="budgetContainerTable">
    <tbody>
    <tr>
        <td valign="top" align="right">
            {{--            <table>--}}
            {{--                <tbody>--}}
            {{--                <tr align="right">--}}
            {{--                    <td></td>--}}
            {{--                    <td width="200px" align="right" class="lineTableBudgetTd"><span style="font-weight:bold; ">Mal Hizmet Toplam Tutarı</span></td>--}}
            {{--                    <td align="right" style="width:81px; " class="lineTableBudgetTd">1.900,00 TL</td>--}}
            {{--                </tr>--}}
            {{--                <tr align="right">--}}
            {{--                    <td></td>--}}
            {{--                    <td width="200px" align="right" class="lineTableBudgetTd"><span style="font-weight:bold; ">Toplam İskonto</span></td>--}}
            {{--                    <td align="right" style="width:81px; " class="lineTableBudgetTd">0,00 TL</td>--}}
            {{--                </tr>--}}
            {{--                <tr align="right">--}}
            {{--                    <td></td>--}}
            {{--                    <td align="right" width="211px" class="lineTableBudgetTd"><span style="font-weight:bold; ">Hesaplanan KDV(%18)</span></td>--}}
            {{--                    <td align="right" style="width:82px; " class="lineTableBudgetTd"> 342,00 TL</td>--}}
            {{--                </tr>--}}
            {{--                <tr align="right">--}}
            {{--                    <td></td>--}}
            {{--                    <td align="right" width="200px" class="lineTableBudgetTd"><span style="font-weight:bold; ">Vergiler Dahil Toplam Tutar</span></td>--}}
            {{--                    <td align="right" style="width:82px; " class="lineTableBudgetTd">2.242,00 TL</td>--}}
            {{--                </tr>--}}
            {{--                <tr align="right">--}}
            {{--                    <td></td>--}}
            {{--                    <td align="right" width="200px" class="lineTableBudgetTd"><span style="font-weight:bold; ">&Ouml;denecek Tutar</span></td>--}}
            {{--                    <td align="right" style="width:82px; " class="lineTableBudgetTd">2.242,00 TL</td>--}}
            {{--                </tr>--}}
            {{--                </tbody>--}}
            {{--            </table>--}}
        </td>
    </tr>
    </tbody>
</table>

</body>
</html>
