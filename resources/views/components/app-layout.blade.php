<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.bunny.net/css2?family=Nunito:wght@400;600;700&display=swap">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Livewire Styles -->
    @livewireStyles

    <!-- Scripts -->
    {{-- Vite veya Mix kullanılıyorsa buraya eklenebilir, ama CDN kullandığımız için zorunlu değil --}}
    {{-- @vite(['resources/css/app.css', 'resources/js/app.js']) --}}

    <script>
        // İsteğe bağlı: Tailwind yapılandırması (CDN ile de çalışır)
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Nunito', 'sans-serif'],
                    },
                }
            }
        }
    </script>
</head>
<body class="font-sans antialiased">
<div class="min-h-screen bg-gray-100">
    {{-- Üst Navigasyon veya Başlık Alanı (İsteğe Bağlı) --}}
    @if (isset($header))
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                {{ $header }}
            </div>
        </header>
    @endif

    <!-- Sayfa İçeriği -->
    <main>
        {{ $slot }}
    </main>
</div>

<!-- Livewire Scripts -->
@livewireScripts
</body>
</html>
