<div>
    {{-- Hat<PERSON>/Başarı <PERSON>jları --}}
    @if (session()->has('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline">{{ session('success') }}</span>
        </div>
    @endif
    @if (session()->has('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif
    @if (session()->has('info'))
        <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline">{{ session('info') }}</span>
        </div>
    @endif

    {{-- <PERSON><PERSON>ü<PERSON>me Göstergesi --}}
    <div wire:loading wire:target="updatedTckn" class="mb-4 text-gray-600">
        Kullanıcı ve sipariş bilgileri yükleniyor, lütfen bekleyin...
    </div>

    {{-- Form Alanları --}}
    <div class="mb-4">
        <label for="tckn" class="block text-sm font-medium text-gray-700">TC Kimlik Numarası</label>
        <input type="text" id="tckn" wire:model.lazy="tckn"
               maxlength="11"
               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
               placeholder="11 Haneli TCKN giriniz">
        @error('tckn') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
    </div>

    {{-- Kullanıcı ve Sipariş Bilgileri (Kullanıcı Bulunduysa) --}}
    @if ($userFound && $user)
        <div class="mt-6 p-4 border rounded-md bg-gray-50">
            <h3 class="text-lg font-medium text-gray-900 mb-2">Kullanıcı Bilgileri</h3>
            <p><strong>Ad Soyad:</strong> {{ $user->full_name_db }}</p>
            <p><strong>E-posta:</strong> {{ $user->email }}</p>
            <p><strong>TCKN:</strong> {{ $user->tckn }}</p>

            <div class="mt-6">
                <h4 class="text-md font-medium text-gray-800 mb-2">Siparişler ve Pegasus Puan Durumu</h4>
                @if($ordersWithMeta->isNotEmpty())
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100">
                            <tr>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sipariş ID</th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sipariş No</th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tarih</th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tutar</th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PGS Siparişi mi?</th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Puan Gönderildi mi?</th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">İşlem</th>
                            </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                            @foreach ($ordersWithMeta as $order)
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ $order->id }}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ $order->order_number }}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{{ $order->created_at->format('d.m.Y H:i') }}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{{-- Sipariş tutarını formatlayarak gösterin --}} {{ number_format($order->total, 2, ',', '.') }} TL</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm">
                                        @if ($order->pgsMetaInfo['is_pgs_order'])
                                            <span class="text-green-600 font-semibold">Evet</span>
                                        @else
                                            <span class="text-red-600 font-semibold">Hayır</span>
                                        @endif
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm">
                                        @if ($order->pgsMetaInfo['sended_pgs_reward_points'])
                                            {{-- Puan gönderilmişse, gönderilen puan miktarını gösterelim --}}
                                            <span class="text-green-600 font-semibold">Evet ({{ $order->pgsMetaInfo['reward_points_value'] ?? '?' }} Puan)</span>
                                        @else
                                            <span class="text-red-600 font-semibold">Hayır</span>
                                        @endif
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium">
                                        {{-- Sadece PGS Siparişiyse ve Puan Gönderilmemişse butonu aktif et --}}
                                        @if ($order->pgsMetaInfo['is_pgs_order'] && !$order->pgsMetaInfo['sended_pgs_reward_points'])
                                            <button wire:click.prevent="sendRewardForOrder({{ $order->id }})"
                                                    wire:loading.attr="disabled"
                                                    wire:target="sendRewardForOrder({{ $order->id }})"
                                                    class="inline-flex items-center px-3 py-1 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-900 focus:outline-none focus:border-blue-900 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                                                <span wire:loading wire:target="sendRewardForOrder({{ $order->id }})" class="animate-spin rounded-full h-3 w-3 border-t-2 border-b-2 border-white mr-1"></span>
                                                Puan Gönder
                                            </button>
                                        @elseif ($order->pgsMetaInfo['sended_pgs_reward_points'])
                                            <span class="text-gray-400 text-xs">Gönderildi</span>
                                        @else
                                            <span class="text-gray-400 text-xs">Uygun Değil</span>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="mt-4 text-gray-500">Bu kullanıcı için uygun sipariş bulunamadı.</p>
                @endif
            </div>
        </div>
    @elseif (!empty($tckn) && !$loading && !session()->has('error') && strlen($tckn ?? '') === 11)
        {{-- TCKN girildi ama kullanıcı bulunamadıysa (hata mesajı zaten gösteriliyor) --}}
        <p class="mt-4 text-orange-600">Bu TCKN ile eşleşen kullanıcı bulunamadı.</p>
    @endif

    {{-- Genel Yükleme Durumu (Butonlar için) --}}
    <div wire:loading wire:target="sendRewardForOrder" class="fixed bottom-4 right-4 bg-gray-700 text-white text-sm px-3 py-2 rounded-md shadow-lg z-50">
        <div class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Puan gönderme işlemi yapılıyor...
        </div>
    </div>
</div>
