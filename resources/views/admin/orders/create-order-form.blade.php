<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('JSON ile Ye<PERSON>ş Oluştur') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    @if (session('success'))
                        <div class="mb-4 font-medium text-sm text-green-600">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="mb-4 font-medium text-sm text-red-600">
                            {{ session('error') }}
                        </div>
                    @endif

                    @if ($errors->any())
                        <div class="mb-4">
                            <div class="font-medium text-red-600">{{ __('Whoops! Something went wrong.') }}</div>
                            <ul class="mt-3 list-disc list-inside text-sm text-red-600">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('admin.order.create_from_json') }}">
                        @csrf

                        <div>
                            <label for="order_number" class="block font-medium text-sm text-gray-700">{{ __('Sipariş Numarası') }}</label>
                            <input id="order_number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" type="text" name="order_number" value="{{ old('order_number', 'KBXHGLHFWOZT') }}" required autofocus />
                        </div>

                        <div class="mt-4">
                            <label for="amount_collected" class="block font-medium text-sm text-gray-700">{{ __('Tahsil Edilen Tutar (Opsiyonel)') }}</label>
                            <input id="amount_collected" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" type="number" step="0.01" name="amount_collected" value="{{ old('amount_collected', '288.50') }}" />
                        </div>

                        <div class="mt-4">
                            <label for="iyzico_transaction_id" class="block font-medium text-sm text-gray-700">{{ __('Iyzico İşlem ID') }}</label>
                            <input id="iyzico_transaction_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" type="text" name="iyzico_transaction_id" value="{{ old('iyzico_transaction_id', '1234567890') }}" required />
                        </div>

                        <div class="mt-4">
                            <label for="json_payload" class="block font-medium text-sm text-gray-700">{{ __('JSON Payload (Sipariş ve Kullanıcı Detayları)') }}</label>
                            <textarea id="json_payload" name="json_payload" rows="15" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>{!! old('json_payload', $json_payload) !!}</textarea>
                        </div>

                        <div class="mt-4">
                            <label for="order_items_json" class="block font-medium text-sm text-gray-700">{{ __('Sipariş Ürünleri (JSON Array)') }}</label>
                            <textarea id="order_items_json" name="order_items_json" rows="10" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>{{ old('order_items_json', json_encode([
                                [
                                    "product_type" => "App\\Models\\Lunar\\ProductVariant",
                                    "product_id" => 779, // Örnek ürün ID
                                    "quantity" => 1,
                                    "price" => 2885, // Birim fiyat
                                    "sub_total" => 2404.17, // Vergiler hariç alt toplam
                                    "tax_included" => true,
                                    "tax_rate" => 20,
                                    "tax_amount" => 480.83,
                                    "total" => 2885, // Bu ürün için toplam
                                    "has_insurance" => false,
                                    "insurance_price" => 0,
                                    "plan" => 7 // Örneğin kiralama ayı veya plan ID
                                ]
                            ], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES)) }}</textarea>
                            <p class="mt-1 text-sm text-gray-500">Her bir sipariş kalemini içeren bir JSON dizisi girin.</p>
                        </div>

                        <div class="mt-4">
                            <label for="campaign_info_json" class="block font-medium text-sm text-gray-700">{{ __('Kampanya/Kupon Bilgileri (JSON)') }}</label>
                            <textarea id="campaign_info_json" name="campaign_info_json" rows="5" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">{{ old('campaign_info_json', json_encode(
                                [
                                    "campaignPrice" => 0, // Uygulanan kampanya indirimi veya özel fiyat
                                    "insurancePrice" => 0, // Eklenen toplam sigorta bedeli (eğer ürünler dışında ayrıca yönetiliyorsa)
                                    "coupon_id" => null // Uygulanan kuponun ID'si
                                ], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES
                            )) }}</textarea>
                            <p class="mt-1 text-sm text-gray-500">Sipariş geneline uygulanacak kampanya, sigorta ve kupon bilgilerini girin.</p>
                        </div>

                        <div class="flex items-center justify-end mt-4">
                            <button type="submit" class="ml-3 inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                                {{ __('Sipariş Oluştur') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout> 