<x-filament::page>

    <style>
        .selected-tab {
            border-bottom: 10px solid #70d44b;
            font-weight: bolder;
        }
    </style>
    <div class="sm:hidden">
        <label for="tabs" class="sr-only">Select a tab</label>
        <!-- Use an "onChange" listener to redirect the user to the selected tab URL. -->
        <select id="tabs" name="tabs" class="block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
            <option selected>My Account</option>
            <option>Company</option>
        </select>
    </div>
    <div class="hidden sm:block">
        <nav class="isolate flex divide-x divide-gray-200 rounded-lg shadow" aria-label="Tabs">
            <!-- Current: "text-gray-900", Default: "text-gray-500 hover:text-gray-700" -->
            <a href="#" class="{{ $selectedTab == 'tab1' ? $activeTabClass : $inactiveTabClass  }} rounded-l-lg group relative min-w-0 flex-1 overflow-hidden bg-white py-4 px-4 text-sm font-medium text-center hover:bg-gray-50 focus:z-10" aria-current="page" wire:click="changeSelectedTab('tab1')">
                <span>Henüz Satın Alınmamışlar</span>
                <span aria-hidden="true" class="bg-indigo-500 absolute inset-x-0 bottom-0 h-0.5"></span>
            </a>

            <a href="#" class="{{ $selectedTab == 'tab2' ? $activeTabClass : $inactiveTabClass  }} group relative min-w-0 flex-1 overflow-hidden bg-white py-4 px-4 text-sm font-medium text-center hover:bg-gray-50 focus:z-10" wire:click="changeSelectedTab('tab2')">
                <span>Satın Alınmışlar</span>
                <span aria-hidden="true" class="bg-transparent absolute inset-x-0 bottom-0 h-0.5"></span>
            </a>

        </nav>
    </div>

    @if($selectedTab == 'tab1')
        <livewire:product-purchase-table></livewire:product-purchase-table>
    @elseif($selectedTab == 'tab2')
        <livewire:product-purchase-buyed-table></livewire:product-purchase-buyed-table>
    @endif
</x-filament::page>
