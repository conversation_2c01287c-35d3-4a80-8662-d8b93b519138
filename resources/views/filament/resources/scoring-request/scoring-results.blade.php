@php
    $scoringResults = $getRecord()->scoringResults()->orderBy('created_at', 'desc')->get();
@endphp

@if($scoringResults->isNotEmpty())
<div class="space-y-4 mt-6">
    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Skorlama Sonuçları
        </h3>

        @foreach($scoringResults as $result)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h4 class="text-sm font-medium text-gray-900">
                            {{ ucfirst($result->service_name) }} Skorlama Servisi
                        </h4>
                        <span class="text-xs text-gray-500">
                            {{ $result->created_at->format('d.m.Y H:i') }}
                        </span>
                    </div>
                </div>

                <div class="p-4 space-y-3">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        @if($result->findex_score)
                            <div class="bg-blue-50 rounded-lg p-3">
                                <div class="text-xs text-blue-600 font-medium mb-1">
                                    Findex Skoru
                                </div>
                                <div class="text-2xl font-bold text-blue-700">
                                    {{ number_format($result->findex_score, 0) }}
                                </div>
                            </div>
                        @endif

                        @if($result->scoring_service_score)
                            <div class="bg-green-50 rounded-lg p-3">
                                <div class="text-xs text-green-600 font-medium mb-1">
                                    Servis Skoru
                                </div>
                                <div class="text-2xl font-bold text-green-700">
                                    {{ number_format($result->scoring_service_score, 0) }}
                                </div>
                            </div>
                        @endif

                        @if($result->service_response_time)
                            <div class="bg-gray-50 rounded-lg p-3">
                                <div class="text-xs text-gray-600 font-medium mb-1">
                                    Yanıt Zamanı
                                </div>
                                <div class="text-lg font-semibold text-gray-700">
                                    {{ $result->service_response_time->format('d.m.Y H:i:s') }}
                                </div>
                            </div>
                        @endif
                    </div>

                    @if($result->response_data)
                        <div class="mt-4">
                            <details class="group">
                                <summary class="cursor-pointer text-sm font-medium text-gray-700 hover:text-primary-600 flex items-center">
                                    <svg class="w-4 h-4 mr-1 transform transition-transform group-open:rotate-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                    Detaylı Yanıt Verisi
                                </summary>
                                
                                <div class="mt-3 bg-gray-50 rounded-lg p-4 border border-gray-200">
                                    @if($result->service_name === 'skorlabunu')
                                        <div class="space-y-2 text-sm">
                                            @if(isset($result->response_data['risk_status']))
                                                <div class="flex justify-between">
                                                    <span class="text-gray-600">Risk Durumu:</span>
                                                    <span class="font-medium {{ $result->response_data['risk_status'] === 'low' ? 'text-green-600' : ($result->response_data['risk_status'] === 'medium' ? 'text-yellow-600' : 'text-red-600') }}">
                                                        {{ ucfirst($result->response_data['risk_status']) }}
                                                    </span>
                                                </div>
                                            @endif
                                            
                                            @if(isset($result->response_data['min_rent_amount']))
                                                <div class="flex justify-between">
                                                    <span class="text-gray-600">Min Kiralama Tutarı:</span>
                                                    <span class="font-medium text-gray-900">
                                                        {{ number_format($result->response_data['min_rent_amount'], 2, ',', '.') }} ₺
                                                    </span>
                                                </div>
                                            @endif
                                            
                                            @if(isset($result->response_data['max_rent_amount']))
                                                <div class="flex justify-between">
                                                    <span class="text-gray-600">Max Kiralama Tutarı:</span>
                                                    <span class="font-medium text-gray-900">
                                                        {{ number_format($result->response_data['max_rent_amount'], 2, ',', '.') }} ₺
                                                    </span>
                                                </div>
                                            @endif
                                            
                                            @if(isset($result->response_data['rental_recommended']))
                                                <div class="flex justify-between">
                                                    <span class="text-gray-600">Kiralama Önerisi:</span>
                                                    <span class="font-medium {{ $result->response_data['rental_recommended'] ? 'text-green-600' : 'text-red-600' }}">
                                                        {{ $result->response_data['rental_recommended'] ? 'Evet' : 'Hayır' }}
                                                    </span>
                                                </div>
                                            @endif

                                            @if(isset($result->response_data['tracking_id']))
                                                <div class="flex justify-between">
                                                    <span class="text-gray-600">Takip ID:</span>
                                                    <span class="font-mono text-xs text-gray-900">
                                                        {{ $result->response_data['tracking_id'] }}
                                                    </span>
                                                </div>
                                            @endif
                                        </div>

                                        {{-- Okunan Rapor Verileri --}}
                                        @if(isset($result->response_data['local_scoring_details']))
                                            @php
                                                $details = $result->response_data['local_scoring_details'];
                                                $extractedData = $details['extracted_data'] ?? [];
                                                $scoringParams = $details['scoring_parameters'] ?? [];
                                                $scoreDetails = $details['score_details'] ?? [];
                                            @endphp
                                            
                                            {{-- Okunan Ham Veriler --}}
                                            @if(!empty($extractedData))
                                                <div class="mt-4 border-t pt-4">
                                                    <h5 class="text-sm font-semibold text-gray-800 mb-3">📊 Okunan Rapor Verileri</h5>
                                                    <div class="grid grid-cols-2 gap-2 text-xs">
                                                        @if(isset($extractedData['limitler_toplami']))
                                                            <div class="flex justify-between bg-gray-50 p-2 rounded">
                                                                <span class="text-gray-600">Limitler Toplamı:</span>
                                                                <span class="font-medium">{{ $extractedData['limitler_toplami'] }}</span>
                                                            </div>
                                                        @endif
                                                        @if(isset($extractedData['borclar_toplami']))
                                                            <div class="flex justify-between bg-gray-50 p-2 rounded">
                                                                <span class="text-gray-600">Borçlar Toplamı:</span>
                                                                <span class="font-medium">{{ $extractedData['borclar_toplami'] }}</span>
                                                            </div>
                                                        @endif
                                                        @if(isset($extractedData['kredili_urunler']))
                                                            <div class="flex justify-between bg-gray-50 p-2 rounded">
                                                                <span class="text-gray-600">Kredili Ürünler:</span>
                                                                <span class="font-medium">{{ $extractedData['kredili_urunler'] }}</span>
                                                            </div>
                                                        @endif
                                                        @if(isset($extractedData['calısılan_kurum_sayisi']))
                                                            <div class="flex justify-between bg-gray-50 p-2 rounded">
                                                                <span class="text-gray-600">Çalışılan Kurum Sayısı:</span>
                                                                <span class="font-medium">{{ $extractedData['calısılan_kurum_sayisi'] }}</span>
                                                            </div>
                                                        @endif
                                                        @if(isset($extractedData['borc_limit_orani']))
                                                            <div class="flex justify-between bg-gray-50 p-2 rounded">
                                                                <span class="text-gray-600">Borç/Limit Oranı:</span>
                                                                <span class="font-medium">%{{ $extractedData['borc_limit_orani'] }}</span>
                                                            </div>
                                                        @endif
                                                        @if(isset($extractedData['en_olumsuz_durum']))
                                                            <div class="flex justify-between bg-gray-50 p-2 rounded col-span-2">
                                                                <span class="text-gray-600">En Olumsuz Durum:</span>
                                                                <span class="font-medium">{{ $extractedData['en_olumsuz_durum'] }}</span>
                                                            </div>
                                                        @endif
                                                        @if(isset($extractedData['kredi_karti_odeme_basarisi']))
                                                            <div class="flex justify-between bg-gray-50 p-2 rounded">
                                                                <span class="text-gray-600">Kredi Kartı Ödeme Başarısı:</span>
                                                                <span class="font-medium">%{{ number_format($extractedData['kredi_karti_odeme_basarisi'], 1) }}</span>
                                                            </div>
                                                        @endif
                                                        @if(isset($extractedData['tuketici_kredisi_odeme_basarisi']))
                                                            <div class="flex justify-between bg-gray-50 p-2 rounded">
                                                                <span class="text-gray-600">Tüketici Kredisi Ödeme Başarısı:</span>
                                                                <span class="font-medium">%{{ number_format($extractedData['tuketici_kredisi_odeme_basarisi'], 1) }}</span>
                                                            </div>
                                                        @endif
                                                        @if(isset($extractedData['kmh_odeme_basarisi']))
                                                            <div class="flex justify-between bg-gray-50 p-2 rounded">
                                                                <span class="text-gray-600">KMH Ödeme Başarısı:</span>
                                                                <span class="font-medium">%{{ number_format($extractedData['kmh_odeme_basarisi'], 1) }}</span>
                                                            </div>
                                                        @endif
                                                        @if(isset($extractedData['en_eski_hesap_tarihi']))
                                                            <div class="flex justify-between bg-gray-50 p-2 rounded">
                                                                <span class="text-gray-600">En Eski Hesap Tarihi:</span>
                                                                <span class="font-medium">{{ $extractedData['en_eski_hesap_tarihi'] }}</span>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            @endif

                                            {{-- Hesaplanan Skorlama Parametreleri --}}
                                            @if(!empty($scoringParams))
                                                <div class="mt-4 border-t pt-4">
                                                    <h5 class="text-sm font-semibold text-gray-800 mb-3">🔢 Hesaplanan Parametreler</h5>
                                                    <div class="grid grid-cols-2 gap-2 text-xs">
                                                        @php
                                                            $parameterLabels = [
                                                                'credit_mix' => 'Kredi Karması',
                                                                'credit_history_length' => 'Kredi Geçmişi Uzunluğu',
                                                                'credit_card_payment_success' => 'Kredi Kartı Ödeme Başarısı',
                                                                'consumer_loan_payment_success' => 'Tüketici Kredisi Ödeme Başarısı',
                                                                'overdraft_payment_success' => 'KMH Ödeme Başarısı',
                                                                'credit_card_ratio' => 'Kredi Kartı Kullanım Oranı',
                                                                'kmh_ratio' => 'KMH Kullanım Oranı',
                                                                'consumer_loan_ratio' => 'Tüketici Kredisi Kullanım Oranı',
                                                                'credit_card_limit_ratio' => 'Kredi Kartı Limit Oranı',
                                                                'raw_history_years' => 'Kredi Geçmişi (Yıl)'
                                                            ];
                                                        @endphp
                                                        @foreach($scoringParams as $key => $value)
                                                            @if($key !== 'raw_history_years')
                                                                <div class="flex justify-between bg-blue-50 p-2 rounded">
                                                                    <span class="text-gray-600">{{ $parameterLabels[$key] ?? ucwords(str_replace('_', ' ', $key)) }}:</span>
                                                                    <span class="font-medium">{{ number_format($value, 3) }}</span>
                                                                </div>
                                                            @endif
                                                        @endforeach
                                                        @if(isset($scoringParams['raw_history_years']))
                                                            <div class="flex justify-between bg-blue-50 p-2 rounded">
                                                                <span class="text-gray-600">Kredi Geçmişi (Yıl):</span>
                                                                <span class="font-medium">{{ number_format($scoringParams['raw_history_years'], 1) }}</span>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            @endif

                                            {{-- Skor Detayları ve Ağırlıklar --}}
                                            @if(!empty($scoreDetails))
                                                <div class="mt-4 border-t pt-4">
                                                    <h5 class="text-sm font-semibold text-gray-800 mb-3">⚖️ Skor Hesaplama Detayları</h5>
                                                    <div class="overflow-x-auto">
                                                        <table class="min-w-full text-xs">
                                                            <thead>
                                                                <tr class="bg-gray-100">
                                                                    <th class="px-2 py-1 text-left">Parametre</th>
                                                                    <th class="px-2 py-1 text-right">Değer</th>
                                                                    <th class="px-2 py-1 text-right">Ağırlık</th>
                                                                    <th class="px-2 py-1 text-right">Katkı</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                @php
                                                                    $detailLabels = [
                                                                        'credit_mix' => 'Kredi Karması',
                                                                        'credit_history_length' => 'Kredi Geçmişi Uzunluğu',
                                                                        'credit_card_payment_success' => 'Kredi Kartı Ödeme Başarısı',
                                                                        'consumer_loan_payment_success' => 'Tüketici Kredisi Ödeme Başarısı',
                                                                        'overdraft_payment_success' => 'KMH Ödeme Başarısı',
                                                                        'credit_card_ratio' => 'Kredi Kartı Kullanım Oranı',
                                                                        'kmh_ratio' => 'KMH Kullanım Oranı',
                                                                        'consumer_loan_ratio' => 'Tüketici Kredisi Kullanım Oranı',
                                                                        'credit_card_limit_ratio' => 'Kredi Kartı Limit Oranı'
                                                                    ];
                                                                @endphp
                                                                @foreach($scoreDetails as $key => $detail)
                                                                    <tr class="border-b">
                                                                        <td class="px-2 py-1">{{ $detailLabels[$key] ?? ucwords(str_replace('_', ' ', $key)) }}</td>
                                                                        <td class="px-2 py-1 text-right">{{ number_format($detail['value'] ?? 0, 3) }}</td>
                                                                        <td class="px-2 py-1 text-right">{{ number_format(($detail['weight'] ?? 0) * 100, 0) }}%</td>
                                                                        <td class="px-2 py-1 text-right font-medium">{{ number_format($detail['contribution'] ?? 0, 4) }}</td>
                                                                    </tr>
                                                                @endforeach
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            @endif
                                        @endif
                                    @else
                                        <pre class="text-xs overflow-x-auto text-gray-700">{{ json_encode($result->response_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                                    @endif
                                </div>
                            </details>
                        </div>
                    @endif
                </div>
            </div>
        @endforeach
    </div>
</div>
@endif