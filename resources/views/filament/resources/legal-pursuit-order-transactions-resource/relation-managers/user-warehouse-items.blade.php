<div class="space-y-4">
    <div class="overflow-hidden overflow-x-auto rounded-xl border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800">
            <h3 class="text-xl font-bold">Sipariş<PERSON> Olu<PERSON> Deposunda Olan <PERSON></h3>
        </div>
        <div class="relative overflow-y-auto">
            <table class="w-full text-left divide-y table-auto">
                <thead>
                    <tr class="bg-gray-50 dark:bg-gray-700">
                        <th class="px-4 py-2 whitespace-nowrap font-medium text-sm text-gray-600 dark:text-gray-300"><PERSON><PERSON><PERSON><PERSON> V<PERSON>ant ID</th>
                        <th class="px-4 py-2 whitespace-nowrap font-medium text-sm text-gray-600 dark:text-gray-300">Ür<PERSON>n <PERSON>i</th>
                        <th class="px-4 py-2 whitespace-nowrap font-medium text-sm text-gray-600 dark:text-gray-300"><PERSON><PERSON><PERSON><PERSON></th>
                        <th class="px-4 py-2 whitespace-nowrap font-medium text-sm text-gray-600 dark:text-gray-300">Varyant</th>
                        <th class="px-4 py-2 whitespace-nowrap font-medium text-sm text-gray-600 dark:text-gray-300">SN</th>
                    </tr>
                </thead>
                <tbody class="divide-y">
                    @forelse ($productStocks as $stock)
                        <tr class="border-b">
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">{{ $stock->product_id }}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                @if($stock->product->product->getFirstPhoto)
                                    <img src="{{ $stock->product->product->getFirstPhoto }}" alt="Ürün" class="w-20 h-20 object-cover rounded">
                                @else
                                    -
                                @endif
                            </td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">{{ $stock->product->product->name }}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">{{ $stock->product->getVaariant }}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">{{ $stock->sn }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="px-4 py-2 text-sm text-center text-gray-500 dark:text-gray-400">Kullanıcı deposunda ürün bulunamadı.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div> 