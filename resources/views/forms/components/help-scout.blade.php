@php
    $isCollapsed = $isCollapsed();
    $isCollapsible = $isCollapsible() ;
@endphp
<div
    @if ($isCollapsible)
        x-data="{
            isCollapsed: @js($isCollapsed),
        }"
    x-on:open-form-section.window="if ($event.detail.id == $el.id) isCollapsed = false"
    x-on:collapse-form-section.window="if ($event.detail.id == $el.id) isCollapsed = true"
    x-on:toggle-form-section.window="if ($event.detail.id == $el.id) isCollapsed = ! isCollapsed"
    x-on:expand-concealing-component.window="
            error = $el.querySelector('[data-validation-error]')

            if (! error) {
                return
            }

            isCollapsed = false

            if (document.body.querySelector('[data-validation-error]') !== error) {
                return
            }

            setTimeout(() => $el.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'start' }), 200)
        "
    @endif
    id="{{ $getId() }}"
    {{
        $attributes
            ->merge($getExtraAttributes())
            ->class([
                'filament-forms-section-component',
                'rounded-xl border border-gray-300 bg-white' =>  true,
                'md:grid-cols-2' => true,
                'md:order-last' => false,
                'dark:border-gray-600 dark:bg-gray-800' => config('forms.dark_mode'),
            ])
    }}>
    <div
        @class([
            'filament-forms-section-header-wrapper flex overflow-hidden rounded-t-xl rtl:space-x-reverse min-h-[56px] items-center bg-gray-100 px-4 py-2',
        ])
        @if ($isCollapsible)
            x-bind:class="{ 'rounded-b-xl': isCollapsed }"
        x-on:click="isCollapsed = ! isCollapsed"
        @endif
    >
        <div
            @class([
                'filament-forms-section-header flex-1 space-y-1',
                'cursor-pointer' => $isCollapsible,
            ])
        >
            <h3
                @class([
                    'pointer-events-none flex flex-row items-center font-bold tracking-tight',
                ])
            >
                @if ($icon = $getIcon())
                    <x-dynamic-component
                        :component="$icon"
                        @class([
                            'mr-1',
                        ])
                    />
                @endif

                {{ $getHeading() }}
            </h3>

            @if ($description = $getDescription())
                <p
                    @class([
                        'text-gray-500',
                    ])
                >
                    {{ $description }}
                </p>
            @endif
        </div>
        @if ($isCollapsible)
            <button
                x-on:click.stop="isCollapsed = ! isCollapsed"
                x-bind:class="{
                    '-rotate-180': ! isCollapsed,
                }"
                type="button"
                @class([
                    'flex transform items-center justify-center rounded-full text-primary-500 outline-none hover:bg-gray-500/5 focus:bg-primary-500/10',
                    '-rotate-180' => ! $isCollapsed,
                ])
            >
                <svg
                    @class([
                        'h-5 w-5' =>true,
                    ])
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 9l-7 7-7-7"
                    />
                </svg>
            </button>
        @endif
    </div>
    <div
        @if ($isCollapsible)
            x-bind:class="{ 'invisible h-0 !m-0 overflow-y-hidden': isCollapsed }"
        x-bind:aria-expanded="(! isCollapsed).toString()"
        @if ($isCollapsed) x-cloak @endif
        @endif
        @class([
            'filament-forms-section-content-wrapper p-6',
        ])
    >
        <div
            @class([
                'filament-forms-section-content',
            ])
        >

            <span>
                <a href="{{ $getLink() }}" class="text-primary-500 hover:text-primary-600" target="_blank">Yazışmayı HelpScout üzerinde görüntüle</a>
            </span>

            {!! $getHelpScoutThread() !!}

            {{ $getChildComponentContainer() }}
        </div>
    </div>

</div>
