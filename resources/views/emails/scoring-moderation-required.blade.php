<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moderasyon Gerekli - Skorlama Talebi</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #dc3545;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .content {
            padding: 30px;
        }
        .alert {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .alert-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 5px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .info-table th {
            background: #f8f9fa;
            padding: 10px;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
        }
        .info-table td {
            padding: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        .highlight {
            background: #ffebee;
            font-weight: bold;
            color: #c62828;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
            margin-top: 20px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .footer {
            background: #f8f9fa;
            padding: 15px;
            text-align: center;
            font-size: 12px;
            color: #6c757d;
        }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .badge-danger {
            background: #dc3545;
            color: white;
        }
        .badge-warning {
            background: #ffc107;
            color: #212529;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚠️ MODERASYON GEREKLİ</h1>
        </div>
        
        <div class="content">
            <div class="alert">
                <div class="alert-title">DİKKAT: Yüksek Değerli Mal Bedeli</div>
                Bu skorlama talebinde mal bedeli <strong>{{ $productValue }} TL</strong> olarak belirtilmiştir ve 
                moderasyon limitinin (500.000 TL) üzerindedir. Manuel inceleme gerekmektedir.
            </div>

            <h2>Skorlama Talebi Detayları</h2>
            
            <table class="info-table">
                <tr>
                    <th>Talep ID</th>
                    <td>{{ $scoringRequest->ulid }}</td>
                </tr>
                <tr>
                    <th>Müşteri Adı</th>
                    <td>{{ $scoringRequest->full_name }}</td>
                </tr>
                <tr>
                    <th>TCKN</th>
                    <td>{{ $scoringRequest->tckn }}</td>
                </tr>
                <tr>
                    <th>Email</th>
                    <td>{{ $scoringRequest->email }}</td>
                </tr>
                <tr>
                    <th>Talep Tarihi</th>
                    <td>{{ $scoringRequest->created_at->format('d.m.Y H:i') }}</td>
                </tr>
                <tr>
                    <th>Kaynak</th>
                    <td>{{ $scoringRequest->scoringSource->name ?? 'Bilinmiyor' }}</td>
                </tr>
            </table>

            <h2>Skorlama Sonuçları</h2>
            
            <table class="info-table">
                <tr>
                    <th>Skor</th>
                    <td>
                        <strong>{{ $score }}</strong>
                        @if($score < 275)
                            <span class="badge badge-danger">RED</span>
                        @elseif($score < 375)
                            <span class="badge badge-warning">STANDART</span>
                        @else
                            <span class="badge badge-warning">YÜKSEK</span>
                        @endif
                    </td>
                </tr>
                <tr>
                    <th>Uygulanan Çarpan</th>
                    <td>{{ $multiplier }}</td>
                </tr>
                <tr>
                    <th>Talep Edilen Tutar</th>
                    <td>{{ $requestedAmount }} TL</td>
                </tr>
                <tr>
                    <th>Hesaplanan Limit</th>
                    <td>{{ $approvedAmount }} TL</td>
                </tr>
                <tr class="highlight">
                    <th>Mal Bedeli</th>
                    <td>{{ $productValue }} TL</td>
                </tr>
            </table>

            @if(isset($evaluation['evaluation_reason']))
            <p><strong>Değerlendirme Sebebi:</strong> {{ $evaluation['evaluation_reason'] }}</p>
            @endif

            <div style="text-align: center; margin-top: 30px;">
                <a href="{{ $adminUrl }}" class="btn">
                    Talebi İncele
                </a>
            </div>
        </div>
        
        <div class="footer">
            <p>Bu email otomatik olarak gönderilmiştir. Lütfen yanıtlamayın.</p>
            <p>© {{ date('Y') }} Kiralabunu Skorlama Sistemi</p>
        </div>
    </div>
</body>
</html>